use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tokio::sync::broadcast;
use serde_json::Value;

pub type EventCallback = Arc<dyn Fn(&str, &Value) + Send + Sync>;

#[derive(Clone)]
pub struct EventEmitter {
    channels: Arc<Mutex<HashMap<String, broadcast::Sender<Value>>>>,
}

impl EventEmitter {
    pub fn new() -> Self {
        Self {
            channels: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub fn emit(&self, event: &str, payload: Value) -> Result<(), Box<dyn std::error::Error>> {
        let mut channels = self.channels.lock().unwrap();
        
        if let Some(sender) = channels.get(event) {
            let _ = sender.send(payload);
        } else {
            // Create new channel if it doesn't exist
            let (sender, _) = broadcast::channel(100);
            let _ = sender.send(payload);
            channels.insert(event.to_string(), sender);
        }
        
        Ok(())
    }

    pub fn subscribe(&self, event: &str) -> broadcast::Receiver<Value> {
        let mut channels = self.channels.lock().unwrap();
        
        if let Some(sender) = channels.get(event) {
            sender.subscribe()
        } else {
            let (sender, receiver) = broadcast::channel(100);
            channels.insert(event.to_string(), sender);
            receiver
        }
    }
}

lazy_static::lazy_static! {
    static ref GLOBAL_EVENT_EMITTER: EventEmitter = EventEmitter::new();
}

pub fn emit_event(event: &str, payload: Value) -> Result<(), Box<dyn std::error::Error>> {
    GLOBAL_EVENT_EMITTER.emit(event, payload)
}

pub fn subscribe_event(event: &str) -> broadcast::Receiver<Value> {
    GLOBAL_EVENT_EMITTER.subscribe(event)
}

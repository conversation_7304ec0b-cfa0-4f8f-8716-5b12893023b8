use std::fs;
#[cfg(target_os = "windows")]
use std::os::windows::process::CommandExt;
use std::path::{Path, PathBuf};
use std::process::Command;

use crate::utils::path::{get_d2t_path, handle_windows_path};
use anyhow::Error;
use fancy_regex::Regex;

/// d2t转换
/// @Param: opt_img_out_dir:
/// 可选参数。指定解析出的图片保存文件夹的绝对路径。
/// @Param: opt_img_url_pfx:
/// 可选参数。指定解析出的图片URL前缀替换字符串。需要与opt_img_out_dir配合使用，否则图片的URL很可能错误
pub fn d2t(
    resources_path: &Path,
    docx_path: &Path,
    opt_img_out_dir: Option<&Path>,
    opt_img_url_pfx: Option<&str>,
) -> Result<String, Box<dyn std::error::Error>> {
    fn move_generated_files(docx_path: &Path) -> Result<PathBuf, Box<dyn std::error::Error>> {
        let file_stem = docx_path
            .file_stem()
            .ok_or(Error::msg("找不到docx文件"))?
            .to_str()
            .ok_or("找不到docx文件")?;
        let parent_path = docx_path.parent().unwrap();
        let target_dir_path = parent_path.join(file_stem);
        if !target_dir_path.exists() {
            fs::create_dir_all(&target_dir_path)?;
        }

        let _ = fs::rename(
            parent_path.join(format!("{}.tex", file_stem)),
            target_dir_path.join(format!("{}.tex", file_stem)),
        );
        let _ = fs::rename(
            parent_path.join(format!("{}.xml", file_stem)),
            target_dir_path.join(format!("{}.xml", file_stem)),
        );
        let _ = fs::rename(
            parent_path.join(format!("{}.log", file_stem)),
            target_dir_path.join(format!("{}.log", file_stem)),
        );
        let _ = fs::rename(
            parent_path.join(format!("{}.csv", file_stem)),
            target_dir_path.join(format!("{}.csv", file_stem)),
        );
        let target_docx_temp_dir_path = &target_dir_path.join(format!("{}.docx.tmp", file_stem));
        if target_docx_temp_dir_path.exists() {
            fs::remove_dir_all(target_docx_temp_dir_path)?;
        }
        let _ = fs::rename(
            parent_path.join(format!("{}.docx.tmp", file_stem)),
            target_docx_temp_dir_path,
        );
        let target_debug_dir_path = &target_dir_path.join(format!("{}.debug", file_stem));
        if target_debug_dir_path.exists() {
            fs::remove_dir_all(target_debug_dir_path)?;
        }
        let _ = fs::rename(
            parent_path.join(format!("{}.debug", file_stem)),
            target_debug_dir_path,
        );

        Ok(target_dir_path.join(format!("{}.tex", file_stem)))
    }

    fn copy_images_to_out_dir(
        docx_path: &Path,
        opt_img_out_dir: Option<&Path>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(img_out_dir) = opt_img_out_dir {
            let file_stem = docx_path
                .file_stem()
                .ok_or(Error::msg("找不到docx文件"))?
                .to_str()
                .ok_or("找不到docx文件")?;
            let parent_dir = docx_path
                .parent()
                .ok_or(Error::msg("找不到docx文件所在文件夹"))?;
            let src_img_dir = parent_dir
                .join(file_stem)
                .join(format!("{}.docx.tmp", file_stem))
                .join("word")
                .join("media");
            if !src_img_dir.exists() || !src_img_dir.is_dir() {
                return Ok(());
            }
            let dist_img_dir = img_out_dir.join(file_stem);
            if !dist_img_dir.exists() {
                fs::create_dir_all(dist_img_dir.clone())?;
            }
            for entry in fs::read_dir(src_img_dir)? {
                if let Ok(entry) = entry {
                    let temp_path = entry.path();
                    if !temp_path.is_file() {
                        continue;
                    }
                    let opt_file_name = entry.file_name();
                    let opt_file_name_str = opt_file_name.to_str();
                    if opt_file_name_str.is_none() {
                        continue;
                    }
                    let file_name_str = opt_file_name_str.unwrap();
                    let dist_file_path = dist_img_dir.join(file_name_str);
                    let _ = fs::copy(temp_path, dist_file_path);
                }
            }
        }
        Ok(())
    }

    fn handle_image_url_in_tex_file(
        tex_path: &Path,
        img_url_pfx: Option<&str>,
    ) -> Result<String, Box<dyn std::error::Error>> {
        let mut tex_content = fs::read_to_string(tex_path)?;
        if let Some(img_url_pfx) = img_url_pfx {
            let file_stem = tex_path
                .file_stem()
                .ok_or(Error::msg("找不到tex文件"))?
                .to_str()
                .ok_or("找不到tex文件")?;
            let file_stem_regex = file_stem.replace(".", "[.]");
            let img_url_p =
                Regex::new(format!("{}[.]docx[.]tmp[/]word[/]media[/]", file_stem_regex).as_str())
                    .unwrap();
            // 替换url
            if img_url_p.is_match(tex_content.as_str())? {
                tex_content = img_url_p
                    .replace_all(
                        tex_content.as_str(),
                        format!("{}/{}/", img_url_pfx, file_stem).replace("\\", "/"),
                    )
                    .into_owned();
                fs::write(tex_path, tex_content.as_str())?;
            }
        }

        Ok(tex_content)
    }

    let docx_path_str = handle_windows_path(docx_path)?;

    let binding = get_d2t_path(resources_path)?;
    let d2t_path = binding.as_path();
    // 创建一个 Command 实例来执行命令，设置工作目录为当前目录的上一级
    let mut output = Command::new(
        d2t_path
            .canonicalize()?
            .to_str()
            .ok_or(Error::msg(format!("找不到d2t文件: {:?}", d2t_path)))?,
    );
    #[cfg(target_os = "windows")]
    {
        let output = output.creation_flags(0x08000000);
        let output = output.arg(docx_path_str.as_str()); // 添加参数
    }
    #[cfg(not(target_os = "windows"))]
    {
        let docx_path = PathBuf::from(docx_path_str.clone());
        let docx_parent_str = docx_path.parent().unwrap().to_str().unwrap();


        let _output = output
            .arg("-o")
            .arg(docx_parent_str)
            .arg(docx_path_str.as_str());
    }

    let output = output
        .output() // 执行命令并获取输出
        .expect("failed to execute process");

    if output.status.success() {
        let tex_path = move_generated_files(docx_path)?;
        if !tex_path.exists() {
            return Err(Box::from(Error::msg("找不到d2t生成的tex文件")));
        }
        copy_images_to_out_dir(docx_path, opt_img_out_dir)?;
        let tex_content = handle_image_url_in_tex_file(tex_path.as_path(), opt_img_url_pfx)?;
        Ok(tex_content)
    } else {
        // 输出错误信息
        let stderr = String::from_utf8_lossy(&output.stdout);
        Err(Box::from(Error::msg(stderr.to_string())))
    }
}

#[test]
fn test01() {
    let opt_img_out_dir = Path::new(
        "C:\\Users\\<USER>\\AppData\\Roaming\\com.qctchina.proposition-assistant\\userUpload",
    );
    let resource_path = Path::new("D:\\docx2tex\\d2t.bat");
    let docx_path = Path::new(
        r"C:\Users\<USER>\AppData\Roaming\com.qctchina.proposition-assistant\task\156\paperDocx\602cb7c742aca5a88fbb8f04bf119f6b.docx",
    );
    let tex_content = d2t(
        resource_path,
        &docx_path,
        Some(opt_img_out_dir),
        Some("userUpload"),
    )
    .unwrap();
    println!("tex_content: {}", tex_content);
}

use crate::services::system_feature::SAGCRFeature;
use reqwest::ClientBuilder;
use serde_json::Value;
use std::time;

// 定义一个结构体来模拟类的行为
pub struct GceRPC;

impl GceRPC {
    /// 检查拼写和语法
    pub async fn check_sag(sagcr_feat: &SAGCRFeature, text: &str) -> Vec<Vec<String>> {
        let dft_rst = vec![vec![0.to_string(), text.to_string()]];
        if text.is_empty() {
            return vec![vec![0.to_string(), "".to_string()]];
        }
        if !sagcr_feat.enable {
            return dft_rst;
        }
        let result = ClientBuilder::new()
            .connect_timeout(time::Duration::from_millis(300))
            .read_timeout(time::Duration::from_millis(30000))
            .build();
        if result.is_err() {
            return dft_rst;
        }
        let client = result.unwrap();
        let result = client
            .post(&sagcr_feat.url)
            .json(&serde_json::json!({ "text": text,"data": text, "key": get_gce_key() }))
            .send()
            .await;

        if result.is_err() {
            return dft_rst;
        }
        let response = result.unwrap();
        if !response.status().is_success() {
            return dft_rst;
        }
        let result = response.text().await;
        if result.is_err() {
            return dft_rst;
        }
        let body_text = result.unwrap();
        // 尝试解析 JSON 文本
        let result = serde_json::from_str(&body_text);
        if result.is_err() {
            return dft_rst;
        }
        let parsed: Value = result.unwrap();
        if let Value::Array(json_array) = parsed {
            // 解析成功并且是一个数组
            let result: Vec<Vec<String>> = json_array
                .into_iter()
                .map(|item| {
                    item.as_array()
                        .map(|arr| {
                            arr.iter()
                                .enumerate()
                                .map(|(idx, v)| {
                                    if idx == 0 {
                                        v.as_i64().unwrap_or(0).to_string()
                                    } else {
                                        v.as_str().unwrap_or("").to_string()
                                    }
                                })
                                .collect()
                        })
                        .unwrap_or_else(Vec::new)
                })
                .collect();
            result
        } else {
            // 如果解析失败或者不是一个数组，返回默认值
            return dft_rst;
        }
    }
}

fn get_gce_key() -> String {
    // 返回 GCE key
    "cEeU3pkZlaBzZ73EyEKr4psiQaIu6Ccd".to_string()
}

#[tokio::test]
async fn test02() {
    let sagcr_feat = SAGCRFeature {
        enable: true,
        // url: "http://127.0.0.1:9005/qc/gce_infer".to_string(),
        url: "http://192.168.4.22:9003/csc/".to_string(),
    };
    let text = "行正事务";
    let result = GceRPC::check_sag(&sagcr_feat, text).await;
    println!("{:#?}", result);
}

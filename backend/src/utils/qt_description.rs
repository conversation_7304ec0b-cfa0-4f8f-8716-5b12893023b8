use indexmap::IndexMap;
use regex::Regex;
use crate::utils::number::{to_num_str_float, usize_to_chinese};

fn get_qt_full_mark_str(opt_que_infos: Option<&Vec<(usize, f64)>>) -> String {
    let qt_full_mark_str = if let Some(que_infos) = opt_que_infos {
        to_num_str_float(que_infos.iter().map(|i| i.1).sum::<f64>())
    } else {
        "2".to_string()
    };
    qt_full_mark_str
}

fn get_q_sc_to_idxes_map(opt_que_infos: Option<&Vec<(usize, f64)>>) -> IndexMap<String, Vec<usize>> {
    let mut q_sc_to_idxes_map: IndexMap<String, Vec<usize>> = IndexMap::new();
    if let Some(que_infos) = opt_que_infos {
        for (q_idx, qs) in que_infos.iter() {
            let qs_str = to_num_str_float(*qs);
            let idxes = q_sc_to_idxes_map.entry(qs_str).or_insert(Vec::new());
            idxes.push(*q_idx);
        }
    }
    q_sc_to_idxes_map
}

pub struct QueTypeDescTemp {
    pub desc_temp: String,
    pub answer_desc_temp: String,
}

pub trait QueTypeDescUtil {
    fn generate(
        &self,
        que_type_name: &str,
        score_type: i32,
        qt_idx: usize,
        que_count: usize,
        opt_que_infos: Option<&Vec<(usize, f64)>>,
        opt_note: Option<String>,
    ) -> QueTypeDescTemp;
}

pub struct DefaultQtDescUtil {}

pub struct HebeiStheeQtDescUtil {}

pub struct FujianStheeQtDescUtil {}

impl QueTypeDescUtil for DefaultQtDescUtil {
    fn generate(
        &self,
        que_type_name: &str,
        score_type: i32,
        qt_idx: usize,
        que_count: usize,
        opt_que_infos: Option<&Vec<(usize, f64)>>,
        _opt_note: Option<String>,
    ) -> QueTypeDescTemp {
        let qt_full_mark_str = get_qt_full_mark_str(opt_que_infos);
        let q_sc_to_idxes_map = get_q_sc_to_idxes_map(opt_que_infos);

        let mut desc_temp = "".to_string();
        let mut answer_desc_temp = "".to_string();
        let chn_num = usize_to_chinese(qt_idx + 1);
        desc_temp.push_str(&chn_num);
        answer_desc_temp.push_str(&chn_num);
        desc_temp.push_str("、");
        answer_desc_temp.push_str("、");
        desc_temp.push_str(que_type_name);
        answer_desc_temp.push_str(que_type_name);
        desc_temp.push_str("：");
        answer_desc_temp.push_str("：");
        if que_count <= 1 {
            desc_temp.push_str(&format!("本题{}分。", qt_full_mark_str));
            answer_desc_temp.push_str(&format!("本题{}分。", qt_full_mark_str));
        } else {
            if score_type == 1 {
                desc_temp.push_str(&format!("本大题共10空，每空1分，共{}分。", qt_full_mark_str));
                answer_desc_temp.push_str(&format!("本大题共10空，每空1分，共{}分。", qt_full_mark_str));
            } else {
                if q_sc_to_idxes_map.is_empty() || q_sc_to_idxes_map.len() == 1 {
                    let que_score = if q_sc_to_idxes_map.is_empty() {
                        "1"
                    } else {
                        q_sc_to_idxes_map.iter().next().unwrap().0
                    };
                    desc_temp.push_str(&format!("本大题共{}小题，每小题{}分，共{}分。", que_count, que_score, qt_full_mark_str));
                    answer_desc_temp.push_str(&format!("本大题共{}小题，每小题{}分，共{}分。", que_count, que_score, qt_full_mark_str));
                } else {
                    let que_score_info = q_sc_to_idxes_map.iter()
                        .map(|(sc, idxes)| format!(
                            "第{}小题{}分",
                            idxes.iter().map(|i| (i + 1).to_string()).collect::<Vec<String>>().join("、"),
                            sc
                        ))
                        .collect::<Vec<String>>()
                        .join(",");
                    desc_temp.push_str(&format!("本大题共{}小题，{}，共{}分。", que_count, que_score_info, qt_full_mark_str));
                    answer_desc_temp.push_str(&format!("本大题共{}小题，{}，共{}分。", que_count, que_score_info, qt_full_mark_str));
                }

                if que_type_name.contains("单项选择") || que_type_name.contains("单选") {
                    desc_temp
                        .push_str("在每小题列出的备选项中只有一项是最符合题目要求的，请将其选出。");
                } else if que_type_name.contains("多项选择") || que_type_name.contains("多选")
                {
                    desc_temp.push_str("在每小题列出的备选项中至少有两项是符合题目要求的，请将其选出，错选、多选或少选均无分。");
                } else if que_type_name.contains("双项选择")
                    || que_type_name.contains("双选")
                    || que_type_name.contains("二项选择")
                    || que_type_name.contains("二选")
                {
                    desc_temp.push_str("在每小题列出的备选项中只有两项是符合题目要求的，请将其选出，错选、多选或少选均无分。");
                } else if que_type_name == "选择题" {
                    desc_temp.push_str("在每小题列出的备选项中有一个或一个以上是符合题目要求的，请将其选出，错选、多选或少选均无分。");
                } else if que_type_name == "配伍选择题" {
                    desc_temp.push_str("备选答案在前，试题在后。每组5小题。每组均对应同一组备选答案，每小题只有一个正确答案，每个备选答案可重复选用，也可不选用。请将其选出，并写在答题纸上。");
                } else if que_type_name.contains("判断改错题") {
                    desc_temp.push_str(
                        "判断下列每小题的正误。正确的打“√”；错误的打“×”，并改正划线部分。",
                    );
                } else if que_type_name == "判断题" {
                    desc_temp.push_str("判断下列每小题的正误。正确的打“√”，错误的打“×”，如全部“√”或“×”的，本大题不给分。");
                } else if que_type_name == "判断说明题" {
                    desc_temp.push_str("判断下列每小题的正误，并简要说明理由。");
                } else if que_type_name == "选择填空题" {
                    desc_temp.push_str("从A、B、C、D、E、F、G、H、I、J中选出一项是最符合题目要求的，每空只能选择一个选项，每个选项只能用一次。请将其选出，并写在答题纸上。");
                } else if que_type_name == "配伍题" {
                    desc_temp.push_str("从A、B、C、D、E、F、G、H中选出一项可以和下列每小题的陈述相配对的最佳答案，每小题只能用一个选项配对，每个选项可以多次使用，也可以不用。请将其选出，并写在答题纸上。");
                }
            }
        }

        QueTypeDescTemp {
            desc_temp,
            answer_desc_temp,
        }
    }
}

impl QueTypeDescUtil for HebeiStheeQtDescUtil {
    fn generate(
        &self,
        que_type_name: &str,
        score_type: i32,
        qt_idx: usize,
        que_count: usize,
        opt_que_infos: Option<&Vec<(usize, f64)>>,
        opt_note: Option<String>,
    ) -> QueTypeDescTemp {
        let qt_full_mark_str = get_qt_full_mark_str(opt_que_infos);
        let q_sc_to_idxes_map = get_q_sc_to_idxes_map(opt_que_infos);

        let mut desc_temp = "".to_string();
        let mut answer_desc_temp = "".to_string();
        let chn_num = usize_to_chinese(qt_idx + 1);
        desc_temp.push_str(&chn_num);
        answer_desc_temp.push_str(&chn_num);
        desc_temp.push_str("、");
        answer_desc_temp.push_str("、");
        desc_temp.push_str(que_type_name);
        answer_desc_temp.push_str(que_type_name);
        desc_temp.push_str("(");
        answer_desc_temp.push_str("(");
        if que_count <= 1 {
            desc_temp.push_str(&format!("本大题共1小题，共{}分)", qt_full_mark_str));
            answer_desc_temp.push_str(&format!("本大题共1小题，共{}分)", qt_full_mark_str));
        } else {
            if score_type == 1 {
                desc_temp.push_str(&format!("本大题共10空，每空1分,共{}分)", qt_full_mark_str));
                answer_desc_temp.push_str(&format!("本大题共10空，每空1分,共{}分)", qt_full_mark_str));
            } else {
                if q_sc_to_idxes_map.is_empty() || q_sc_to_idxes_map.len() == 1 {
                    let que_score = if q_sc_to_idxes_map.is_empty() {
                        "1"
                    } else {
                        q_sc_to_idxes_map.iter().next().unwrap().0
                    };
                    desc_temp.push_str(&format!("本大题共{}小题，每小题{}分,共{}分)", que_count, que_score, qt_full_mark_str));
                    answer_desc_temp.push_str(&format!("本大题共{}小题，每小题{}分,共{}分)", que_count, que_score, qt_full_mark_str));
                } else {
                    let que_score_info = q_sc_to_idxes_map.iter()
                        .map(|(sc, idxes)| format!(
                            "第{}小题{}分",
                            idxes.iter().map(|i| (i + 1).to_string()).collect::<Vec<String>>().join("、"),
                            sc
                        ))
                        .collect::<Vec<String>>()
                        .join(",");
                    desc_temp.push_str(&format!("本大题共{}小题，{}，共{}分)", que_count, que_score_info, qt_full_mark_str));
                    answer_desc_temp.push_str(&format!("本大题共{}小题，{}，共{}分)", que_count, que_score_info, qt_full_mark_str));
                }

                if que_type_name.contains("单项选择") || que_type_name.contains("单选") {
                    desc_temp.push_str("\n在每小题列出的四个备选项中只有一个是符合题目要求的，请选出并将答题卡上对应题号的相应代码涂黑。错涂、\n多涂或未涂均无分。");
                } else if que_type_name.contains("多项选择") || que_type_name.contains("多选")
                {
                    desc_temp.push_str("\n在每小题列出的五个\n备选项中至少有两个是符合题目要求的，请选出并将答题卡上对应题号的相应代码涂黑。错涂、\n多涂、少涂或未涂均无分。");
                } else if que_type_name.contains("双项选择")
                    || que_type_name.contains("双选")
                    || que_type_name.contains("二项选择")
                    || que_type_name.contains("二选")
                {
                    desc_temp.push_str("\n在每小题列出的五个\n备选项中只有两个是符合题目要求的，请选出并将答题卡上对应题号的相应代码涂黑。错涂、\n多涂、少涂或未涂均无分。");
                } else if que_type_name == "选择题" {
                    desc_temp.push_str("\n在每小题列出的四个备选项中只有一个是符合题目要求的，请选出并将答题卡上对应题号的相应代码涂黑。错涂、\n多涂或未涂均无分。");
                } else if que_type_name == "配伍选择题" {
                    desc_temp.push_str("\n备选答案在前，试题在后。每组5小题。每组均对应同一组备选答案，每小题只有一个正确答案，每个备选答案可重复选用，也可不选用。请选出并将答题卡上对应题号的相应代码涂黑。错涂、\n多涂、少涂或未涂均无分。");
                } else if que_type_name.contains("判断改错题") {
                    desc_temp.push_str(
                        "\n判断下列每小题的正误。正确的打“√”；错误的打“×”，并改正划线部分。",
                    );
                } else if que_type_name == "判断题"
                    || que_type_name == "判断说明题"
                    || que_type_name == "判断说明理由题"
                {
                    if let Some(note) = opt_note {
                        let mut opt_pdzw_score = None;
                        let mut opt_smly_score = None;
                        if !note.trim().is_empty() {
                            let pdzw_p = Regex::new("判断正[误确][ ]*([0-9]+)[ ]*分").unwrap();
                            let smly_p = Regex::new("说明理由[ ]*([0-9]+)[ ]*分").unwrap();
                            if let Some(caps) = pdzw_p.captures(note.as_str()) {
                                opt_pdzw_score = Some(caps.get(1).unwrap().as_str().to_string());
                            }
                            if let Some(caps) = smly_p.captures(note.as_str()) {
                                opt_smly_score = Some(caps.get(1).unwrap().as_str().to_string());
                            }
                        }
                        desc_temp.push_str("\n正确的划“√”,错误的划“×”");
                        if opt_pdzw_score.is_some() || opt_smly_score.is_some() {
                            desc_temp.push_str(",并说明理由");
                        }
                        if let Some(pdzw_score) = opt_pdzw_score {
                            desc_temp.push_str(format!(",判断正误{}分", pdzw_score).as_str());
                        }
                        if let Some(smly_score) = opt_smly_score {
                            desc_temp.push_str(format!(",说明理由{}分", smly_score).as_str());
                        }
                        desc_temp.push_str("。");
                    } else {
                        desc_temp.push_str("\n正确的划“√”,错误的划“×”。");
                    }
                } else if que_type_name == "选择填空题" {
                    desc_temp.push_str("\n从A、B、C、D、E、F、G、H、I、J中选出一项是最符合题目要求的，每空只能选择一个选项，每个选项只能用一次。请将其选出，并写在答题纸上。");
                } else if que_type_name == "配伍题" {
                    desc_temp.push_str("\n从A、B、C、D、E、F、G、H中选出一项可以和下列每小题的陈述相配对的最佳答案，每小题只能用一个选项配对，每个选项可以多次使用，也可以不用。请将其选出，并写在答题纸上。");
                }
            }
        }

        QueTypeDescTemp {
            desc_temp,
            answer_desc_temp,
        }
    }
}

impl QueTypeDescUtil for FujianStheeQtDescUtil {
    fn generate(
        &self,
        que_type_name: &str,
        score_type: i32,
        qt_idx: usize,
        que_count: usize,
        _opt_que_infos: Option<&Vec<(usize, f64)>>,
        _opt_note: Option<String>,
    ) -> QueTypeDescTemp {
        let mut desc_temp = "".to_string();
        let mut answer_desc_temp = "".to_string();
        let chn_num = usize_to_chinese(qt_idx + 1);
        desc_temp.push_str(&chn_num);
        answer_desc_temp.push_str(&chn_num);
        desc_temp.push_str("、");
        answer_desc_temp.push_str("、");
        desc_temp.push_str(que_type_name);
        answer_desc_temp.push_str(que_type_name);
        desc_temp.push_str("：");
        answer_desc_temp.push_str("（");
        if que_count <= 1 {
            desc_temp.push_str("本大题共1小题，共2分。");
            answer_desc_temp.push_str("本大题共1小题，共2分）");
        } else {
            if score_type == 1 {
                desc_temp.push_str("本大题共10空，每空1分，共10分。");
                answer_desc_temp.push_str("本大题共10空，每空1分，共10分）");
            } else {
                desc_temp.push_str("本大题共20小题，每小题1分，共20分。");
                answer_desc_temp.push_str("本大题共20小题，每小题1分，共20分）");
                if que_type_name.contains("单项选择") || que_type_name.contains("单选") {
                    desc_temp
                        .push_str("在每小题列出的备选项中只有一项是最符合题目要求的，请将其选出。");
                } else if que_type_name.contains("多项选择") || que_type_name.contains("多选")
                {
                    desc_temp.push_str("在每小题列出的备选项中至少有两项是符合题目要求的，请将其选出，错选、多选或少选均无分。");
                } else if que_type_name.contains("双项选择")
                    || que_type_name.contains("双选")
                    || que_type_name.contains("二项选择")
                    || que_type_name.contains("二选")
                {
                    desc_temp.push_str("在每小题列出的备选项中只有两项是符合题目要求的，请将其选出，错选、多选或少选均无分。");
                } else if que_type_name == "判断选择题" {
                    desc_temp.push_str("判断下列每小题的正误，正确的将答题卡上该小题的“[A]”涂黑，错误的将“[B]”涂黑。");
                } else if que_type_name == "选择题" {
                    desc_temp.push_str("在每小题列出的备选项中有一个或一个以上是符合题目要求的，请将其选出，错选、多选或少选均无分。");
                } else if que_type_name == "配伍选择题" {
                    desc_temp.push_str("备选答案在前，试题在后。每组5小题。每组均对应同一组备选答案，每小题只有一个正确答案，每个备选答案可重复选用，也可不选用。请将其选出，并写在答题纸上。");
                } else if que_type_name.contains("判断改错题") {
                    desc_temp.push_str(
                        "判断下列每小题的正误。正确的打“√”；错误的打“×”，并改正划线部分。",
                    );
                } else if que_type_name == "判断题" {
                    desc_temp.push_str("判断下列每小题的正误。正确的打“√”，错误的打“×”，如全部“√”或“×”的，本大题不给分。");
                } else if que_type_name == "判断说明题" {
                    desc_temp.push_str("判断下列每小题的正误，并简要说明理由。");
                } else if que_type_name == "选择填空题" {
                    desc_temp.push_str("从A、B、C、D、E、F、G、H、I、J中选出一项是最符合题目要求的，每空只能选择一个选项，每个选项只能用一次。请将其选出，并写在答题纸上。");
                } else if que_type_name == "配伍题" {
                    desc_temp.push_str("从A、B、C、D、E、F、G、H中选出一项可以和下列每小题的陈述相配对的最佳答案，每小题只能用一个选项配对，每个选项可以多次使用，也可以不用。请将其选出，并写在答题纸上。");
                }
            }
        }

        QueTypeDescTemp {
            desc_temp,
            answer_desc_temp,
        }
    }
}


pub fn generate_qt_desc_temp(
    que_type_name: &str,
    score_type: i32,
    qt_idx: usize,
    que_count: usize,
    opt_que_infos: Option<&Vec<(usize, f64)>>,
    schema_name: &str,
    opt_note: Option<String>,
) -> Result<QueTypeDescTemp, Box<dyn std::error::Error>> {
    let qt_desc: Box<dyn QueTypeDescUtil> = match schema_name {
        "sthee_hebei" => Box::from(HebeiStheeQtDescUtil {}),
        "sthee_fujian" => Box::from(FujianStheeQtDescUtil {}),
        _ => Box::from(DefaultQtDescUtil {}),
    };
    Ok(qt_desc.generate(que_type_name, score_type, qt_idx, que_count, opt_que_infos, opt_note))
}

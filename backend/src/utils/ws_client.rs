use std::time::Duration;
use futures_util::{SinkExt, StreamExt};
use serde_json::Value;
use tauri::Emitter;
use tokio::time::sleep;
use tokio_tungstenite::connect_async;
use tokio_tungstenite::tungstenite::Message;
use crate::services::system_feature::{SystemFeature, UsageMode};
use crate::utils::app_handle::get_app_handle;


pub async fn startup_websocket_client() {
    let usage_mode: UsageMode = SystemFeature::get_sys_feat_owned()
        .unwrap_or(UsageMode::default());
    if !usage_mode.is_client() {
        return;
    }

    let result = get_app_handle();
    if result.is_err() {
        return;
    }
    let app_handle = result.unwrap();

    tokio::spawn(async move {
        loop {
            let url_prefix = usage_mode.url.replace("http://", "ws://")
                .replace("https://", "wss://");
            let url = format!("{}/ws-broadcast", url_prefix);
            // let url = format!("ws://127.0.0.1:2698/ws-broadcast");
            let result = connect_async(url).await;
            if result.is_err() {
                sleep(Duration::from_secs(5)).await;
                continue;
            }

            println!("Websocket client connected!");

            let (ws_stream, _) = result.unwrap();

            let (mut writer, mut read) = ws_stream.split();

            let _ = writer.send(Message::Ping(Default::default())).await;

            while let Some(Ok(message)) = read.next().await {
                match message {
                    Message::Text(text) => {
                        println!("Received text message: {}", text);
                        let content = text.as_str();
                        let result = serde_json::from_str::<Value>(content);
                        if result.is_err() { break; }
                        let val = result.unwrap();
                        let opt_obj = val.as_object();
                        if opt_obj.is_none() { break; }
                        let obj = opt_obj.unwrap();
                        let opt_event_val = obj.get("event");
                        if opt_event_val.is_none() { break; }
                        let event_val = opt_event_val.unwrap();
                        let event = event_val.as_str().unwrap_or("");
                        if event.is_empty() { break; }
                        let opt_payload_val = obj.get("payload");
                        if opt_payload_val.is_none() { break; }
                        let payload_val = opt_payload_val.unwrap();
                        let result = serde_json::to_string(payload_val);
                        if result.is_err() { break; }
                        let payload = result.unwrap();

                        let _ = app_handle.emit(event, payload);
                    }
                    Message::Binary(_bin) => {

                    }
                    Message::Ping(_) => {
                        let _ = writer.send(Message::Pong(Default::default())).await;
                    }
                    Message::Pong(_) => {
                        println!("Received Pong");
                    }
                    Message::Close(_) => {
                        println!("Connection closed by server");
                        break; // 退出循环
                    }
                    _ => {}
                }
            }

            sleep(Duration::from_secs(5)).await;
        }
    });
}
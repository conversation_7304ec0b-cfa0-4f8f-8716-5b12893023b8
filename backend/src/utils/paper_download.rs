use crate::utils::path::{get_paper_download_jar_path, handle_windows_path};
use lazy_static::lazy_static;
#[cfg(target_os = "windows")]
use std::os::windows::process::CommandExt;
use std::path::Path;
use std::process::{Child, Command, Stdio};

struct SubProcess {
    child: Child,
}

impl Drop for SubProcess {
    fn drop(&mut self) {
        if let Err(e) = self.child.kill() {
            eprintln!("Failed to kill the sub process: {}", e);
        }
    }
}

lazy_static! {
    static ref PAPER_DOWNLOAD_PROC: std::sync::Mutex<Option<SubProcess>> =
        std::sync::Mutex::new(None);
}

pub struct PaperDownload {}

impl PaperDownload {
    /// 启动paper-download守护进程
    pub fn startup_daemon(resources_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
        // 获取 MutexGuard
        let mut proc_guard = PAPER_DOWNLOAD_PROC.lock()?;
        if proc_guard.is_some() {
            return Err(Box::from(anyhow::Error::msg(
                "paper-download守护进程已启动，无需重复启动",
            )));
        }

        let paper_download_jar_path = get_paper_download_jar_path(resources_path)?;
        let paper_download_dir_str =
            handle_windows_path(paper_download_jar_path.parent().unwrap())?;
        // 以下2个路径动态获取
        let java_exe_path_str = r"java";
        let paper_download_jar_path_str = handle_windows_path(paper_download_jar_path.as_path())?;

        // 创建一个 Command 实例来执行命令，设置工作目录为当前目录的上一级
        let mut child = Command::new(java_exe_path_str);
        #[cfg(target_os = "windows")]
        let child = child.creation_flags(0x08000000);
        let child = child
            .current_dir(paper_download_dir_str)
            .arg("-jar") // 添加参数
            .arg(paper_download_jar_path_str)
            .stdin(Stdio::null())
            .stdout(Stdio::null())
            .stderr(Stdio::null())
            .spawn()
            .expect("failed to startup paper-download process");

        // 将 Child 对象保存到全局变量中
        *proc_guard = Some(SubProcess { child });

        Ok(())
    }

    pub fn shutdown_daemon() -> Result<(), Box<dyn std::error::Error>> {
        // 获取 MutexGuard
        let mut proc_guard = PAPER_DOWNLOAD_PROC.lock()?;
        *proc_guard = None;
        Ok(())
    }
}

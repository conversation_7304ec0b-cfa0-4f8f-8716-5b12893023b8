use std::collections::{HashMap, HashSet};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub struct Item {
    val: String,
    serial: usize,
}

impl Item {
    pub fn new(val: &str, serial: usize) -> Self {
        Item {
            val: val.to_string(),
            serial,
        }
    }

    pub fn build(list: &[String]) -> Vec<Self> {
        let mut items = Vec::new();
        let mut ser_map = HashMap::new();
        for val in list {
            let ser = ser_map.entry(val.clone()).or_insert(0);
            *ser += 1;
            items.push(Item::new(val, *ser));
        }
        items
    }
}

#[derive(Debug)]
pub struct DiffResult {
    pub leak_info: Vec<(usize, usize)>,
    pub sup_info: Vec<(usize, usize)>,
    pub dup_info: Vec<(usize, usize)>,
    pub seq_err_info: Vec<(usize, usize)>,
    pub present_info: Vec<(usize, usize, usize)>,
}

pub fn diff_two_list(pri_list: &[String], ref_list: &[String]) -> DiffResult {
    let pri_items = Item::build(pri_list);
    let ref_items = Item::build(ref_list);
    let pri_items_set: HashSet<_> = pri_items.iter().cloned().collect();
    let ref_items_set: HashSet<_> = ref_items.iter().cloned().collect();

    let leak_items_set: HashSet<_> = pri_items_set.difference(&ref_items_set).cloned().collect();
    let superfluous_items_set: HashSet<_> =
        ref_items_set.difference(&pri_items_set).cloned().collect();
    let present_items_set: HashSet<_> = pri_items_set
        .intersection(&ref_items_set)
        .cloned()
        .collect();

    let mut leak_info: Vec<_> = leak_items_set
        .iter()
        .map(|item| {
            (
                pri_items.iter().position(|x| x == item).unwrap(),
                item.serial,
            )
        })
        .collect();
    leak_info.sort_by_key(|x| x.0);

    let mut superfluous_info = Vec::new();
    let mut dup_info = Vec::new();
    for item in superfluous_items_set.iter() {
        if item.serial > 1 && pri_list.contains(&item.val) {
            dup_info.push((
                ref_items.iter().position(|x| x == item).unwrap(),
                item.serial,
            ));
        } else {
            superfluous_info.push((
                ref_items.iter().position(|x| x == item).unwrap(),
                item.serial,
            ));
        }
    }
    dup_info.sort_by_key(|x| x.0);
    superfluous_info.sort_by_key(|x| x.0);

    let mut seq_err_info = Vec::new();
    let filtered_pri_items: Vec<_> = pri_items
        .iter()
        .filter(|x| present_items_set.contains(x))
        .cloned()
        .collect();
    let filtered_ref_items: Vec<_> = ref_items
        .iter()
        .filter(|x| present_items_set.contains(x))
        .cloned()
        .collect();
    for (idx, item) in filtered_ref_items.iter().enumerate() {
        if *item != filtered_pri_items[idx] {
            seq_err_info.push((
                ref_items.iter().position(|x| x == item).unwrap(),
                item.serial,
            ));
        }
    }
    seq_err_info.sort_by_key(|x| x.0);

    let mut present_info: Vec<_> = present_items_set
        .iter()
        .map(|item| {
            (
                pri_items.iter().position(|x| x == item).unwrap(),
                ref_items.iter().position(|x| x == item).unwrap(),
                item.serial,
            )
        })
        .collect();
    present_info.sort_by_key(|x| x.0);

    DiffResult {
        leak_info,
        sup_info: superfluous_info,
        dup_info,
        seq_err_info,
        present_info,
    }
}

#[test]
fn test01() {
    let pri_list = vec!["1", "2", "2", "4", "5", "6", "7", "8"]
        .into_iter()
        .map(String::from)
        .collect::<Vec<_>>();
    let ref_list = vec!["1", "3", "2", "2", "8", "3", "6", "5", "8", "9", "10"]
        .into_iter()
        .map(String::from)
        .collect::<Vec<_>>();

    let result = diff_two_list(&pri_list, &ref_list);
    println!("Result: {:?}", result);
}

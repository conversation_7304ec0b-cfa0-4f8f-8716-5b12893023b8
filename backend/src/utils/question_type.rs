pub fn get_default_qt_tuples() -> Vec<(u64, String)> {
    vec![
        (0, "B1型题".to_string()),
        (0, "业务处理题".to_string()),
        (0, "业务核算题".to_string()),
        (0, "业务题".to_string()),
        (0, "作品赏析题".to_string()),
        (0, "作图题".to_string()),
        (0, "作文题".to_string()),
        (0, "写作主题".to_string()),
        (0, "写作辅题".to_string()),
        (0, "分析计算题".to_string()),
        (0, "分析论述题".to_string()),
        (0, "分析说明题".to_string()),
        (0, "分析题".to_string()),
        (0, "判断分析题".to_string()),
        (0, "判断改错题".to_string()),
        (0, "判断正误题".to_string()),
        (0, "判断说明题".to_string()),
        (0, "判断说明理由题".to_string()),
        (0, "判断改正题".to_string()),
        (0, "判断选择题".to_string()),
        (0, "判断题".to_string()),
        (0, "材料选择题".to_string()),
        (0, "单项选择题".to_string()),
        (0, "单项选择题（A型题）".to_string()),
        (0, "单项选择题(A型题)".to_string()),
        (0, "单项选择题（B型题）".to_string()),
        (0, "单项选择题(B型题)".to_string()),
        (0, "双项选择题".to_string()),
        (0, "古文标点题".to_string()),
        (0, "古文翻译题".to_string()),
        (0, "古文背诵题".to_string()),
        (0, "古文阅读题".to_string()),
        (0, "名词解释题".to_string()),
        (0, "图表题".to_string()),
        (0, "填句补文".to_string()),
        (0, "填空题".to_string()),
        (0, "填词补文".to_string()),
        (0, "多选题".to_string()),
        (0, "多项选择题".to_string()),
        (0, "大作文题".to_string()),
        (0, "完形补文".to_string()),
        (0, "完成反应".to_string()),
        (0, "实际应用题".to_string()),
        (0, "小作文题".to_string()),
        (0, "应用题".to_string()),
        (0, "情景分析题".to_string()),
        (0, "改错题".to_string()),
        (0, "术语解释题".to_string()),
        (0, "材料分析题".to_string()),
        (0, "材料解析题".to_string()),
        (0, "材料题".to_string()),
        (0, "核算题".to_string()),
        (0, "案例分析题".to_string()),
        (0, "案例分析题（一）".to_string()),
        (0, "案例分析题（二）".to_string()),
        (0, "案例题".to_string()),
        (0, "概括段落大意和补全句子".to_string()),
        (0, "汉译英".to_string()),
        (0, "病例串选择题".to_string()),
        (0, "病例分析题".to_string()),
        (0, "短文写作".to_string()),
        (0, "程序填空题".to_string()),
        (0, "程序设计题".to_string()),
        (0, "程序阅读题".to_string()),
        (0, "简单计算题".to_string()),
        (0, "简析题".to_string()),
        (0, "简答题".to_string()),
        (0, "简述题".to_string()),
        (0, "绘图题".to_string()),
        (0, "综合分析题".to_string()),
        (0, "综合应用题".to_string()),
        (0, "综合题".to_string()),
        (0, "计算分析题".to_string()),
        (0, "计算题".to_string()),
        (0, "计算题Ⅰ".to_string()),
        (0, "计算题Ⅱ".to_string()),
        (0, "计算题Ⅲ".to_string()),
        (0, "论述题".to_string()),
        (0, "设计题".to_string()),
        (0, "证明题".to_string()),
        (0, "词语解释题".to_string()),
        (0, "语音题".to_string()),
        (0, "财务处理题".to_string()),
        (0, "辨析题".to_string()),
        (0, "释词题".to_string()),
        (0, "问答题".to_string()),
        (0, "阅读判断题".to_string()),
        (0, "阅读理解题".to_string()),
        (0, "阅读选择".to_string()),
        (0, "完成下列反应（写出主要产物的构造式）".to_string()),
        (0, "用系统命名法命名或写出下列化合物的结构".to_string()),
        (0, "100米".to_string()),
        (0, "一、다음대화를완성하십시오.(각2점,총20점).".to_string()),
        (0, "三、다음()중알맞은것을고르십시오.(각2점,총20점)".to_string()),
        (0, "临摹".to_string()),
        (0, "二、다음()중알맞은것을고르십시오.(각2점,총40점)".to_string()),
        (0, "五、다음문장을중국어로번역하십시오.(각5점,총50점)".to_string()),
        (0, "以速写为表现形式".to_string()),
        (0, "会話応答".to_string()),
        (0, "作文".to_string()),
        (0, "六、다음문장을한국어로번역하십시오.(각6점,총30점)".to_string()),
        (0, "写作".to_string()),
        (0, "写作题".to_string()),
        (0, "写作题或论述题".to_string()),
        (0, "写出下列反应方程式或离子反应式".to_string()),
        (0, "写出下列各调式音阶".to_string()),
        (0, "写出下列有机反应的生产物".to_string()),
        (0, "分析下列乐曲调式".to_string()),
        (0, "分析与设计题".to_string()),
        (0, "分析判断题".to_string()),
        (0, "分析应用题".to_string()),
        (0, "分析简答题".to_string()),
        (0, "创作".to_string()),
        (0, "半音阶及移调转调".to_string()),
        (0, "单选".to_string()),
        (0, "单选题".to_string()),
        (0, "单项选择".to_string()),
        (0, "合成题".to_string()),
        (0, "名词及符号解释".to_string()),
        (0, "名词解释".to_string()),
        (0, "命名或写出化合物的结构式".to_string()),
        (0, "命名或写出结构式".to_string()),
        (0, "命名或写结构式".to_string()),
        (0, "完成反应式，写出反应主要产物".to_string()),
        (0, "命题创作".to_string()),
        (0, "命题设计".to_string()),
        (0, "和声写作题".to_string()),
        (0, "和声分析题".to_string()),
        (0, "和弦写作".to_string()),
        (0, "和弦写作内容".to_string()),
        (0, "問題一、次の文の線を引いた漢字はどう読みますか。ABCD の中から、もっとも適切なものを一つ選び なさい。".to_string()),
        (0, "問題二、次の文の線を引いた仮名はどの漢字ですか。ABCD の中から、もっとも適切なものを一つ選 びなさい。".to_string()),
        (0, "四、다음문장을읽고질문에답하십시오.(각2점,총40점)".to_string()),
        (0, "填空".to_string()),
        (0, "完型填空".to_string()),
        (0, "完形填空".to_string()),
        (0, "完成下列反应(写出主要产物的构造式)".to_string()),
        (0, "完成反应方程式".to_string()),
        (0, "完成反应题".to_string()),
        (0, "实例分析题".to_string()),
        (0, "实践题".to_string()),
        (0, "情景对话".to_string()),
        (0, "情景对话选择".to_string()),
        (0, "按要求分析和弦性质和构成和弦".to_string()),
        (0, "按要求构成和弦".to_string()),
        (0, "按要求构成音程".to_string()),
        (0, "按要求构成音程与和弦".to_string()),
        (0, "推导结构题".to_string()),
        (0, "推测结构".to_string()),
        (0, "是非判断题".to_string()),
        (0, "朝汉翻译".to_string()),
        (0, "案例分析".to_string()),
        (0, "次の文の_____に ABCD の中から、もっとも適切なものを一つ選びなさい。".to_string()),
        (0, "次の文の（ ）に ABCD の中から、もっとも適切なものを一つ選びなさい。".to_string()),
        (0, "次の文章を読んで、質問に答えなさい。答えは ABCD から一番いいものを一つ選びなさい。".to_string()),
        (0, "汉朝翻译".to_string()),
        (0, "理论应用题".to_string()),
        (0, "用化学方法鉴别下列产物".to_string()),
        (0, "用化学方法鉴别下列化合物".to_string()),
        (0, "用化学方法鉴别下列各组物质".to_string()),
        (0, "用正确的记谱方法重写下例".to_string()),
        (0, "由指定原料合成".to_string()),
        (0, "由指定原料合成(无机试剂任选)".to_string()),
        (0, "由指定原料合成下列各化合物".to_string()),
        (0, "画图题".to_string()),
        (0, "立定三级跳".to_string()),
        (0, "推断题".to_string()),
        (0, "结构推断题".to_string()),
        (0, "综合论述题".to_string()),
        (0, "编程题".to_string()),
        (0, "翻译".to_string()),
        (0, "翻译题".to_string()),
        (0, "色彩".to_string()),
        (0, "节奏节拍写作".to_string()),
        (0, "解释现象".to_string()),
        (0, "解释概念题".to_string()),
        (0, "识图题".to_string()),
        (0, "词汇语法".to_string()),
        (0, "语法与词汇".to_string()),
        (0, "语音知识".to_string()),
        (0, "请改正下列音节的拼写错误".to_string()),
        (0, "读图分析题".to_string()),
        (0, "读图题".to_string()),
        (0, "调式写作及分析".to_string()),
        (0, "选择题".to_string()),
        (0, "选词填空".to_string()),
        (0, "配伍选择题".to_string()),
        (0, "鉴别题".to_string()),
        (0, "铅球（男5kg，女4kg）".to_string()),
        (0, "阅读理解".to_string()),
        (0, "阅读理解（1）".to_string()),
        (0, "阅读理解（2）".to_string()),
        (0, "音乐基本符号及表情、速度、力度记号写作判断".to_string()),
        (0, "音程写作".to_string()),
        (0, "音程写作内容".to_string()),
        (0, "（一）次の日本語を中国語に訳しなさい。".to_string()),
        (0, "（二）次の中国語を日本語に訳しなさい。".to_string()),
    ]
}

use snowflaker::generator::{Generator, SnowflakeGenerator};
use std::sync::{Arc, Mutex};

struct SnowflakeGeneratorContext {
    generator: SnowflakeGenerator,
}

impl SnowflakeGeneratorContext {
    fn new() -> Self {
        Self {
            generator: SnowflakeGenerator::new(31, 31).unwrap(),
        }
    }
}

lazy_static::lazy_static! {
    static ref ID_GENERATOR: Arc<Mutex<SnowflakeGeneratorContext>> = Arc::new(Mutex::new(SnowflakeGeneratorContext::new()));
}

pub struct SnowflakeGeneratorUtil {}

impl SnowflakeGeneratorUtil {
    pub fn next() -> u64 {
        let id_gen_ctx = ID_GENERATOR.lock().unwrap();
        id_gen_ctx.generator.next_id().unwrap()
    }
}

// #[test]
// fn test01() {
//     let mut id_set: HashSet<u64> = HashSet::new();
//     for i in 0..100 {
//         let id = SnowflakeGeneratorUtil::next();
//         if i % 2 == 0 {
//             println!("次数：{}，ID: {}", i, id);
//         }
//         if !id_set.insert(id) {
//             panic!("次数：{}，重复ID: {}", i, id);
//         }
//     }
// }

use std::env;
use std::process::Command;

pub fn open_word(path: &str) -> Result<(), String> {
    if env::consts::OS != "windows" {
        return Err("非Windows系统".to_string());
    }

    let output = Command::new("cmd")
        .args(&["/C", "start", "winword.exe", path])
        .output()
        .expect("无法执行命令");

    if !output.status.success() {
        return Err("无法打开Word文件".to_string());
    }

    Ok(())
}

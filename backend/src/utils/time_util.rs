use chrono::{DateTime, Local, NaiveDate, NaiveDateTime, TimeZone};

pub fn datetime_str_to_datetime_local(
    datetime_str: &str, opt_format: Option<&str>,
) -> Result<DateTime<Local>, Box<dyn std::error::Error>> {
    let format = if let Some(temp) = opt_format { temp } else { "%Y-%m-%d %H:%M:%S" };

    // 尝试解析字符串为NaiveDateTime
    let naive = NaiveDateTime::parse_from_str(datetime_str, format)?;

    // 将NaiveDateTime转换为Local DateTime
    let datetime_local: DateTime<Local> = Local.from_local_datetime(&naive)
        .single()
        .ok_or(anyhow::Error::msg("无法获取本地时间变量"))?;
    Ok(datetime_local)
}

pub fn date_str_to_datetime_local(
    date_str: &str, opt_format: Option<&str>,
) -> Result<DateTime<Local>, Box<dyn std::error::Error>> {
    let format = if let Some(temp) = opt_format { temp } else { "%Y-%m-%d" };

    // 尝试解析字符串为NaiveDateTime
    let naive_date = NaiveDate::parse_from_str(date_str, format)?;

    // 将NaiveDate转换为NaiveDateTime，设置时间为午夜 (00:00:00)
    let naive_datetime = naive_date.and_hms_opt(0, 0, 0)
        .ok_or(anyhow::Error::msg("无法转换本地日期变量"))?;

    // 将NaiveDateTime转换为Local DateTime
    let datetime_local: DateTime<Local> = Local.from_local_datetime(&naive_datetime)
        .single()
        .ok_or(anyhow::Error::msg("无法获取本地时间变量"))?;
    Ok(datetime_local)
}

pub fn now_datetime_local() -> DateTime<Local> {
    Local::now()
}

pub fn datetime_local_to_string(datetime: DateTime<Local>) -> String {
    // 格式化为字符串（示例：2023_10_05_15_30_45）
    datetime.format("%Y_%m_%d_%H_%M_%S").to_string()
}


#[test]
fn test01() {
    let ds = "2024-06-01";
    let dtl = date_str_to_datetime_local(ds, Some("%Y-%m-%d")).unwrap();
    println!("ts: {}", dtl.timestamp_millis());
}

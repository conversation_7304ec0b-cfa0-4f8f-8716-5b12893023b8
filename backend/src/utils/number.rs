use rand::Rng;


/// 随机分配整数使得它们的和为 m
///
/// # 参数
/// * `n`: 整数的数量
/// * `m`: 整数的总和
///
/// # 返回
/// * 一个 Vec<i32>，包含随机分配的整数值
pub fn random_allocation(n: usize, m: usize) -> Vec<usize> {
    assert!(m >= n, "m 必须至少等于 n");

    let mut rng = rand::thread_rng();
    let mut numbers: Vec<usize> = vec![1; n]; // 初始化所有整数为 1
    let remaining_sum = m - n;

    // 随机分配剩余的值
    for _ in 0..remaining_sum {
        let index = rng.gen_range(0..n); // 生成随机索引
        numbers[index] += 1;
    }

    numbers
}

pub fn usize_to_chinese(num: usize) -> String {
    // 定义中文数字
    let digits = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
    let units = ["", "十", "百", "千"];
    let mut result = String::new();
    let mut num = num;

    if num == 0 {
        return digits[0].to_string();
    }

    // 处理每个四位段（万、亿等）
    let big_units = ["", "万", "亿"];
    let mut big_unit_index = 0;

    while num > 0 {
        let mut chunk = num % 10_000;
        num /= 10_000;

        if chunk != 0 {
            let mut chunk_str = String::new();
            let mut zero_flag = false; // 用于标记“零”的插入

            for i in 0..4 {
                let digit = chunk % 10;
                if digit != 0 {
                    if zero_flag {
                        chunk_str = format!("{}{}", digits[0], chunk_str);
                        zero_flag = false;
                    }

                    // 判断是否需要移除“一十”的前导“一”
                    if i == 1 && digit == 1 && chunk / 10 == 0 {
                        chunk_str = format!("{}{}", units[i], chunk_str);
                    } else {
                        chunk_str = format!("{}{}{}", digits[digit], units[i], chunk_str);
                    }
                } else {
                    zero_flag = true;
                }
                chunk /= 10;
            }

            chunk_str = chunk_str.trim_end_matches('零').to_string(); // 移除尾部的“零”
            result = format!("{}{}{}", chunk_str, big_units[big_unit_index], result);
        }

        big_unit_index += 1;
    }

    result.trim_end_matches('零').to_string() // 移除最终结果尾部的“零”
}

pub fn to_num_str_float(num: f64) -> String {
    if num.fract() == 0.0 {
        // 如果小数部分为0，则输出整数部分
        format!("{}", num.trunc() as i64)
    } else {
        // 计算小数部分的位数
        let decimal_places = num.log10().trunc() as isize - num.fract().log10().trunc() as isize;
        let format_str = format!("{:.*}", decimal_places as usize, num);
        format_str
    }
}

#[test]
fn test01() {
    for i in 0usize..1345 {
        println!("usize to chinese map: {} -> {}", i, usize_to_chinese(i));
    }
}
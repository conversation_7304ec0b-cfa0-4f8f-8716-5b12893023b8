use std::fs;
use std::path::{Path, PathBuf};
use std::sync::RwLock;
use std::env;
use lazy_static::lazy_static;
use serde_derive::{Deserialize, Serialize};

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct UserDataDirCustomConfig {
    pub enable: bool,
    pub path: String,
}

const USER_DATA_DIR_CUSTOM_CONFIG_JSON_NAME: &str = ".app_data_dir_custom_config.json";
lazy_static! {
    static ref G_USER_DATA_DIR_CUSTOM_CONFIG: RwLock<Option<UserDataDirCustomConfig >> =
        RwLock::new(None);
}

pub fn get_app_data_dir()-> Result<PathBuf, Box<dyn std::error::Error>> {
    // Use environment variable or default to current directory + data
    let app_data_dir = env::var("APP_DATA_DIR")
        .unwrap_or_else(|_| "./data".to_string());
    Ok(PathBuf::from(app_data_dir))
}

/// 检测app_data_dir是否存在，如果不存在就创建
pub fn exists_create_app_data_dir()-> Result<(), Box<dyn std::error::Error>> {
    let app_data_dir = get_app_data_dir()?;
    if !app_data_dir.exists() {
        fs::create_dir_all(&app_data_dir)?;
    }
    Ok(())
}

pub fn get_resources_dir() -> Result<PathBuf, Box<dyn std::error::Error>> {
    // Use environment variable or default to current directory + resources
    let resources_dir = env::var("RESOURCES_DIR")
        .unwrap_or_else(|_| "./resources".to_string());
    Ok(PathBuf::from(resources_dir))
}

pub fn get_licence_path() -> Result<PathBuf, Box<dyn std::error::Error>> {
    let app_data_dir = get_app_data_dir()?;
    Ok(app_data_dir.join(".env.json"))
}

pub fn set_user_data_dir_custom_config(
    config: UserDataDirCustomConfig,
) -> Result<(), Box<dyn std::error::Error>> {
    let app_data_dir = get_app_data_dir()?;

    if config.enable {
        let tp = Path::new(&config.path);
        if !tp.exists() || !tp.is_dir() {
            return Err(Box::from(anyhow::Error::msg("自定义文件夹不存在或不合法")));
        }
    }
    let json_content = serde_json::to_string_pretty(&config)?;
    let cfg_json_path = app_data_dir.join(USER_DATA_DIR_CUSTOM_CONFIG_JSON_NAME);
    fs::write(cfg_json_path, json_content)?;

    let mut opt_config = G_USER_DATA_DIR_CUSTOM_CONFIG.write()?;
    *opt_config = Some(config);

    Ok(())
}

pub fn get_app_data_dir_custom_config() -> Result<UserDataDirCustomConfig, Box<dyn std::error::Error>> {
    let app_data_dir = get_app_data_dir()?;

    let opt_config = {
        G_USER_DATA_DIR_CUSTOM_CONFIG.read()?.clone()
    };
    let config = if opt_config.is_none() {
        let cfg_json_path = app_data_dir.join(USER_DATA_DIR_CUSTOM_CONFIG_JSON_NAME);
        if cfg_json_path.exists() {
            let json_content = fs::read_to_string(cfg_json_path)?;
            let config_from_file: UserDataDirCustomConfig = serde_json::from_str(&json_content)?;
            {
                let mut temp_opt_config = G_USER_DATA_DIR_CUSTOM_CONFIG.write()?;
                *temp_opt_config = Some(config_from_file.clone())
            }
            config_from_file
        } else {
            let temp_config = UserDataDirCustomConfig {
                enable: false,
                path: get_app_data_dir()?.display().to_string(),
            };
            set_user_data_dir_custom_config(temp_config.clone())?;
            temp_config
        }
    } else {
        opt_config.as_ref().cloned().unwrap()
    };
    Ok(config)
}

/// 如果用户未自定义或未启用应用数据文件夹，或者虽然定义且启用，但文件夹路径不合法，则返回tauri内置的app_data_dir。
/// 只有当用户启用且自定义文件夹合法时，才会返回该自定义文件夹。
pub fn get_app_data_dir_if_custom() -> Result<PathBuf, Box<dyn std::error::Error>> {
    let app_data_dir = get_app_data_dir()?;
    let config = get_app_data_dir_custom_config()?;
    let app_data_dir = if config.enable {
        let temp_path = PathBuf::from(&config.path);
        if temp_path.exists() && temp_path.is_dir() {
            temp_path
        } else {
            app_data_dir.to_path_buf()
        }
    } else {
        app_data_dir.to_path_buf()
    };
    Ok(app_data_dir)
}

pub fn get_surreal_db_dir() -> Result<PathBuf, Box<dyn std::error::Error>> {
    let custom_dir = get_app_data_dir_if_custom()?;
    Ok(custom_dir.join("surrealDB"))
}

pub fn get_static_dir() -> Result<PathBuf, Box<dyn std::error::Error>> {
    let custom_dir = get_app_data_dir_if_custom()?;
    Ok(custom_dir.join("static"))
}

pub fn get_retrieval_dir() -> Result<PathBuf, Box<dyn std::error::Error>> {
    let custom_dir = get_app_data_dir_if_custom()?;
    Ok(custom_dir.join("retrieval"))
}

pub fn get_upload_dir() -> Result<PathBuf, Box<dyn std::error::Error>> {
    let custom_dir = get_app_data_dir_if_custom()?;
    Ok(custom_dir.join("upload"))
}

pub fn get_book_summary_file_path() -> Result<PathBuf, Box<dyn std::error::Error>> {
    let custom_dir = get_app_data_dir_if_custom()?;
    Ok(custom_dir.join("book_summary_set.bin"))
}
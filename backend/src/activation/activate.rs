use crate::models::surreal::{get_db, Record};
use chrono::{Duration, Local, NaiveDate};
use lazy_static::lazy_static;
use rand::rngs::StdRng;
use rand::Rng;
use rand::SeedableRng;
use serde_derive::{Deserialize, Serialize};
// use std::io::Read;
use tokio::sync::RwLock;
#[derive(Serialize, Deserialize, Debug, Clone)]
pub(crate) struct Activation {
    rnd: u32,
    key: String,
    expiration: NaiveDate,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct License {
    cd_key: Option<String>,
    activation: Option<String>,
}

impl License {
    fn new(cd_key: String, activation: String) -> Self {
        License {
            cd_key: Some(cd_key),
            activation: Some(activation),
        }
    }

    /// 从数据库加载License的数据
    async fn load_from_db() -> Self {
        let db = get_db().await;
        let license_result: Result<Option<License>, surrealdb::Error> =
            db.select(("config", "activation".to_string())).await;
        // 如果查询失败，返回默认的 License
        let license = match license_result {
            Ok(Some(license)) => license,
            Ok(None) => License {
                cd_key: None,
                activation: None,
            },
            Err(_) => License {
                cd_key: None,
                activation: None,
            },
        };
        license
    }

    /// 将License的数据储存，保存到数据库
    pub async fn save(&self) -> Result<(), Box<dyn std::error::Error>> {
        let db = get_db().await;
        let existing_record: Option<Record> =
            db.select(("config", "activation".to_string())).await?;
        if existing_record.is_none() {
            // 如果数据不存在，则创建新记录
            let _: Option<Record> = db
                .create(("config", "activation".to_string()))
                .content(self)
                .await?;
        } else {
            // 如果数据已存在，执行更新操作
            let _: Option<Record> = db
                .update(("config", "activation".to_string()))
                .content(self) // 将当前对象的内容用于更新
                .await?;
        }
        Ok(())
    }
}

impl Activation {
    /// 通过全局的License生成Activation
    pub async fn new() -> Self {
        let mut rng = StdRng::from_entropy(); // 生成一个线程安全的随机数生成器
        let rnd = rng.gen_range(0..10001); // 生成 0 到 10000 之间的随机数
        let key = get_activation_key().await;
        let expiration = get_deadline().await;

        let mut activation = Activation {
            rnd,
            key,
            expiration,
        };
        // Get machine-uid
        let machine_uid = match machine_uid::get() {
            Ok(uid) => uid,
            _ => String::new(),
        };

        if !machine_uid.is_empty() {
            let new_key = activation.machine_uid_to_key(&machine_uid);
            if new_key != activation.key {
                // 本机ID不一致，重置激活码
                activation.key = new_key;
                activation.expiration = Local::now().date_naive();
            }
        }
        activation
    }

    // fn disable(&mut self) {
    //     self.expiration = DateTime::from(Utc::now());
    // }

    // fn address2key(&self, address: &str) -> u32 {
    //     let mut tmp = [0u32, 0u32];
    //     for (i, byte) in address.bytes().enumerate() {
    //         let index = i % 2;
    //         tmp[index] += u32::from(byte) * (u32::from(i as u32 % 2) + 1);
    //     }
    //     (tmp[0] + 1) * (tmp[1] + 1)
    // }

    fn machine_uid_to_key(&self, machine_uid: &str) -> String {
        let mut tmp = [0u32, 0u32];
        for (i, byte) in machine_uid.bytes().enumerate() {
            let index = i % 2;
            tmp[index] += u32::from(byte) * (u32::from(i as u32 % 2) + 1);
        }
        format!("{:06}", ((tmp[0] + 1) * (tmp[1] + 1)) % 1_000_000)
    }

    fn mask(&self, code: u32, encode: bool) -> i32 {
        let mask = [3, 2, 5, 7, 9, 1];
        let mut ans = 0;
        let mut code = code;
        for i in 0..6 {
            let v: i32 = if encode { mask[i] } else { -mask[i] };
            let tmp = ((code as i32 % 10) + v + 10) % 10;
            ans += tmp * 10i32.pow(i as u32);
            code /= 10;
        }
        ans
    }

    /// 生成机器码
    pub fn get_key(&self) -> u32 {
        self.key.parse().unwrap_or(0) + self.rnd
    }

    pub fn is_same_key(&self, key: String) -> bool {
        self.key == key
    }

    /// 是否合法时间
    fn valid(&self) -> bool {
        Local::now().date_naive() < self.expiration
    }

    /// danger！！！ 生成一个本机可用的激活码
    // pub fn gen_code(&self, key: u32, days: u32) -> String {
    //     let v = (key / 1000 + key % 1000) % 1000;
    //     let raw_code = v + days * 1000;
    //     let code = self.mask(raw_code, true);
    //     let mut rng = rand::thread_rng();
    //     let pre = rng.gen_range(0..101);
    //     let tail = 100 - pre;
    //     format!("{:02}{:06}{:02}", pre, code, tail)
    // }

    /// 解析，激活
    async fn parse_code(&mut self, code: &str, save: bool) -> bool {
        let v = (self.get_key() / 1000 + self.get_key() % 1000) % 1000;
        if code.len() != 10 {
            return false;
        }
        let pre: u32 = code[..2].parse().unwrap_or(0);
        let tail: u32 = code[8..].parse().unwrap_or(0);
        if pre + tail != 100 {
            return false;
        }
        if let Ok(code) = code[2..8].parse::<u32>() {
            let raw_code = self.mask(code, false);
            if v != <i32 as TryInto<u32>>::try_into(raw_code % 1000).unwrap() {
                return false;
            }
            let days = raw_code / 1000;
            self.expiration = Local::now().date_naive() + Duration::days(days as i64);
            if save {
                self.save().await;
            }
            true
        } else {
            false
        }
    }

    /// 储存，生成License，储存入数据库
    async fn save(&self) {
        let license = License::new(
            self.key.to_string(),
            self.expiration.format("%Y-%m-%d").to_string(),
        );
        let _ = license.save().await;
    }
}

pub async fn get_activation_key() -> String {
    let license = get_license().await;
    match license.cd_key {
        Some(cd_key) if !cd_key.is_empty() => cd_key,
        _ => rand::thread_rng().gen_range(0..10000).to_string(),
    }
}

pub(crate) async fn get_deadline() -> NaiveDate {
    // get activation from embed db
    let license = get_license().await;
    match license.activation {
        Some(exp) if !exp.is_empty() => {
            NaiveDate::parse_from_str(exp.as_str(), "%Y-%m-%d").unwrap_or(Local::now().date_naive())
        }
        _ => Local::now().date_naive(),
    }
}

lazy_static! {
    static ref LICENSE: RwLock<Option<License>> = RwLock::new(None);
    static ref ACTIVATION: RwLock<Option<Activation>> = RwLock::new(None);
}

pub async fn get_license() -> License {
    let l = LICENSE.read().await;
    l.clone().unwrap()
}

pub async fn get_activation() -> Activation {
    let a = ACTIVATION.read().await;
    a.clone().unwrap()
}

pub async fn init_license_on_launch() -> Result<(), String> {
    // 首先从数据库加载 License
    let l = License::load_from_db().await;

    // 然后加锁并更新 LICENSE
    {
        let mut license_option = LICENSE.write().await;
        *license_option = Some(l);
    } // 锁在这里被释放

    Ok(())
}

pub async fn init_activation_on_launch() -> Result<(), String> {
    let a = Activation::new().await;

    // 加锁并更新 ACTIVATION
    {
        let mut activation_option = ACTIVATION.write().await;
        *activation_option = Some(a);
    } // 锁在这里被释放

    Ok(())
}

/// 判断现在静态变量中的activation 时间是否合法
pub async fn get_is_activated() -> bool {
    is_same_cdk().await && in_valid_time().await
}
pub async fn is_same_cdk() -> bool {
    let cdk_o = get_license().await.cd_key;
    if cdk_o.is_none() {
        return false;
    }
    let cdk = cdk_o.unwrap();
    get_activation().await.is_same_key(cdk.parse().unwrap())
}

pub async fn in_valid_time() -> bool {
    get_activation().await.valid()
}

/// 输入一个code，判断是否合法激活，返回激活状态
pub async fn try_parse_code(code: &str) -> bool {
    let mut activation = get_activation().await;
    let value = activation.parse_code(code, true).await;
    value
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_activation() {
        let activation = Activation::new().await;
        let key = activation.get_key();
        println!("key: {}", key);

        // let code = activation.gen_code(key, 180);
        // println!("code: {}", code);

        // assert!(activation.parse_code(code.as_str(), true).await);
    }
}

use std::collections::HashSet;
use std::sync::RwLock;
use lazy_static::lazy_static;
use regex::Regex;
use serde_derive::{Deserialize, Serialize};
use tantivy::{DocAddress, Order, Score, Searcher};
use tantivy::collector::{Count, TopDocs};
use tantivy::fastfield::FastValue;
use tantivy::query::Query;
use tantivy::tokenizer::{TokenStream, Tokenizer, Token};
use tantivy_jieba::JiebaTokenizer;

pub mod card;
pub mod book;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PredictCardOrBookParams {
    #[serde(rename = "searchText")]
    pub search_text: String,
    #[serde(rename = "pageNo")]
    pub page_no: Option<usize>,
    #[serde(rename = "pageSize")]
    pub page_size: Option<usize>,
    #[serde(rename = "subjectCode")]
    pub subject_code: Option<String>,
    #[serde(rename = "type")]
    pub t_type: Option<String>,
    #[serde(rename = "bookId")]
    pub book_id: Option<u64>,
    pub author: Option<String>,
    pub series: Option<String>,
    pub version: Option<String>,
    pub edition: Option<String>,
    pub brand: Option<String>,
    pub period: Option<i32>,
    pub grade: Option<String>,
    // 出版年份
    // 如果此参数存在，则earliestPublishDate和recentPublishDate不再起作用
    #[serde(rename = "publishYear")]
    pub publish_year: Option<u16>,
    // 格式为: "2025-01-14"
    #[serde(rename = "earliestPublishDate")]
    pub earliest_publish_date: Option<String>,
    // 格式为: "2025-01-14"
    #[serde(rename = "recentPublishDate")]
    pub recent_publish_date: Option<String>,
    #[serde(rename = "assembleId")]
    pub assemble_id: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PredictResult<T> {
    totals: usize,
    #[serde(rename = "pageNo")]
    page_no: usize,
    #[serde(rename = "pageCount")]
    page_count: usize,
    #[serde(rename = "pageSize")]
    page_size: usize,
    pub list: Vec<T>,
    highlights: Vec<String>,
    // 搜索耗时，单位：毫秒
    retrieval_duration: u128,
    // 总耗时，单位：毫秒
    total_duration: u128,
}

fn get_stop_word_set() -> HashSet<&'static str> {
    HashSet::from([
        "@","γ","μ","φ","φ．", "，", "。", ".", "(", ")", "\"", "_", "-", ",", ";", "“", "”", "、", "<", ">", "．", "/", "\\", ":", "：", "{", "}", "[", "]", "【", "】",
        "×", "Δ","■","▲","＃","％","＆","＇","＋","＋ξ","＋＋","－","－β","＜","＜±","＜Δ","＜λ","＜φ","＜＜","=","＝","＝☆","＝－","＞","＞λ","＿","〜±","〜＋","［⑤ｆ］","［⑤ｄ］",
        "［②ｉ］","≈","［②Ｇ］","［①ｆ］","ＬＩ","㈧","［－","......","〉","［③⑩］","一直","一些","许多","种","有的是","也就是说","末##末","啊","阿","哎","哎呀","哎哟","唉","俺","俺们","按","吧","吧哒","把","罢了",
        "被","本","本着","比","比方","比如","鄙人","彼","彼此","边","别","别的","别说","并","并且","不比","不成","不单","不但","不独","不管","不光","不过","不仅","不拘","不论","不怕","不然","不如","不特",
        "不惟","不问","不只","朝","朝着","趁","趁着","乘","冲","除","除此之外","除非","除了","此","此间","此外","从","从而","打","待","但","但是","当","当着","到","得","的","的话","等","等等",
        "地","第","叮咚","对","对于","多","多少","而","而况","而且","而是","而外","而言","而已","尔后","反过来","反过来说","反之","非但","非徒","否则","嘎","嘎登","该","赶","个","各","各个","各位","各种",
        "各自","给","根据","跟","故","故此","固然","关于","管","归","果然","果真","过","哈","哈哈","呵","和","何","何处","何况","何时","嘿","哼","哼唷","呼哧","乎","哗","还是","还有","换句话说",
        "换言之","或","或是","或者","极了","及","及其","及至","即","即便","即或","即令","即若","即使","几","几时","己","既","既然","既是","继而","加之","假如","假若","假使","鉴于","将","较","较之","叫",
        "接着","结果","借","紧接着","进而","尽","尽管","经","经过","就","就是","就是说","据","具体地说","具体说来","开始","开外","靠","咳","可","可见","可是","可以","况且","啦","来","来着","离","例如","哩",
        "连","连同","两者","了","临","另","另外","另一方面","论","嘛","吗","慢说","漫说","冒","么","每","每当","们","莫若","某","某个","某些","拿","哪","哪边","哪儿","哪个","哪里","哪年","哪怕",
        "哪天","哪些","哪样","那","那边","那儿","那个","那会儿","那里","那么","那么些","那么样","那时","那些","那样","乃","乃至","呢","能","你","你们","您","宁","宁可","宁肯","宁愿","哦","呕","啪达","旁人",
        "呸","凭","凭借","其","其次","其二","其他","其它","其一","其余","其中","起","起见","起见","岂但","恰恰相反","前后","前者","且","然而","然后","然则","让","人家","任","任何","任凭","如","如此","如果",
        "如何","如其","如若","如上所述","若","若非","若是","啥","上下","尚且","设若","设使","甚而","甚么","甚至","省得","时候","什么","什么样","使得","是","是的","首先","谁","谁知","顺","顺着","似的","虽","虽然",
        "虽说","虽则","随","随着","所","所以","他","他们","他人","它","它们","她","她们","倘","倘或","倘然","倘若","倘使","腾","替","通过","同","同时","哇","万一","往","望","为","为何","为了",
        "为什么","为着","喂","嗡嗡","我","我们","呜","呜呼","乌乎","无论","无宁","毋宁","嘻","吓","相对而言","像","向","向着","嘘","呀","焉","沿","沿着","要","要不","要不然","要不是","要么","要是","也",
        "也罢","也好","一","一般","一旦","一方面","一来","一切","一样","一则","依","依照","矣","以","以便","以及","以免","以至","以至于","以致","抑或","因","因此","因而","因为","哟","用","由","由此可见","由于",
        "有","有的","有关","有些","又","于","于是","于是乎","与","与此同时","与否","与其","越是","云云","哉","再说","再者","在","在下","咱","咱们","则","怎","怎么","怎么办","怎么样","怎样","咋","照","照着",
        "者","这","这边","这儿","这个","这会儿","这就是说","这里","这么","这么点儿","这么些","这么样","这时","这些","这样","正如","吱","之","之类","之所以","之一","只是","只限","只要","只有","至","至于","诸位","着","着呢",
        "自","自从","自个儿","自各儿","自己","自家","自身","总的来看","总的来说","总的说来","总而言之","总之","纵","纵令","纵然","纵使","兮","呃","呗","咚","咦","喏","啐","喔唷","嗬","嗯","嗳",
    ])
}

lazy_static! {
    static ref IMG_TAG_P: Regex = Regex::new("<img[ ]+src[ ]*=(.+?)/[ ]*>").unwrap();
    static ref STOP_WORDS_SET: RwLock<HashSet<&'static str>> = RwLock::new(get_stop_word_set());
}

// 定义自定义的 TokenStream
pub struct JiebaTokenizerNSWsStream {
    tokens: Vec<Token>,
    index: usize,
}

impl TokenStream for JiebaTokenizerNSWsStream {
    fn advance(&mut self) -> bool {
        if self.index < self.tokens.len() {
            self.index += 1;
            true
        } else {
            false
        }
    }

    fn token(&self) -> &Token {
        &self.tokens[self.index - 1]
    }

    fn token_mut(&mut self) -> &mut Token {
        &mut self.tokens[self.index - 1]
    }
}

/// 支持过滤停用词的jieba分词器
#[derive(Clone)]
pub struct JiebaTokenizerNSWs {
    tokenizer: JiebaTokenizer,
}

impl JiebaTokenizerNSWs {
    pub fn new() -> Self {
        Self {
            tokenizer: JiebaTokenizer {},
        }
    }
}

impl Tokenizer for JiebaTokenizerNSWs {
    type TokenStream<'a> = JiebaTokenizerNSWsStream;

    fn token_stream(&mut self, text: &str) -> JiebaTokenizerNSWsStream {
        let text = IMG_TAG_P.replace_all(text, "").into_owned();
        let jieba_tokenizer = &mut self.tokenizer;
        // 对关键词进行分词处理
        let mut token_stream = jieba_tokenizer.token_stream(&text);
        let mut tokens = Vec::new();
        let stop_words_set = STOP_WORDS_SET.read().unwrap();
        while let Some(token) = token_stream.next() {
            let ts = token.text.clone();
            let t = ts.trim();
            if t.is_empty() {
                continue;
            }
            if stop_words_set.contains(t) {
                continue;
            }
            tokens.push(token.clone());
        }

        JiebaTokenizerNSWsStream{ tokens, index: 0 }
    }
}


pub fn get_tokens_by_jieba(content: &str) -> Vec<String> {
    // 配置 Jieba 分词器
    let mut jieba_tokenizer = JiebaTokenizerNSWs::new();
    // 对关键词进行分词处理
    let mut token_stream = jieba_tokenizer.token_stream(content);
    let mut token_texts = Vec::new();
    while let Some(token) = token_stream.next() {
        token_texts.push(token.text.clone());
    }
    token_texts
}

// 搜索文档，如果search_text不为空串，则按照关键词相关度倒序排列，否则按照sort_field_name指定sort_rule排列
pub fn search_documents_or_sort_by_field<TFastValue>(
    searcher: &Searcher, query: &Box<dyn Query>,
    search_text: &str, page_size: usize, offset: usize,
    sort_field_name: &str, sort_rule: Order,
) -> Result<(Vec<(Score, DocAddress)>, usize), Box<dyn std::error::Error>>
where
    TFastValue: FastValue,
{
    // 搜索结果总数
    let totals = searcher.search(query, &Count)?;
    // 如果关键词不为空，则按照搜索结果相关度排序，否则按照集合id正序排列
    let top_docs = if !search_text.trim().is_empty() {
        searcher.search(
            query,
            &TopDocs::with_limit(page_size).and_offset(offset),
        )?
    } else {
        searcher.search(
            query,
            &TopDocs::with_limit(page_size).and_offset(offset)
                .order_by_fast_field::<TFastValue>(sort_field_name, sort_rule),
        )?
            .into_iter()
            .map(|x| (100f32 as Score, x.1))
            .collect::<Vec<_>>()
    };
    Ok((top_docs, totals))
}

#[test]
fn test01() {
    use std::time::Instant;
    let content = "郭沫若的诗集《女神》是中国新诗的代表性作品,它以崭新的内容和形式,表达了“五四”时期狂飙突进的时代精神。《立在地球边上放号》是《女神》中富有代表性的一首诗。诗人设想站在地球“边上”全方位俯瞰地球,放声呼唤,纵情高歌,想象着那怒涌的白云、壮丽的北冰洋和狂暴的太平洋,意在赞美摧毁旧世界、创造新生活的雄强之力,体现了“五四”所焕发的自由宏阔、雄奇奔放的气概。阅读时要注意联系“五四”特定的时代氛围来理解这首诗的内涵与形式特征。";
    let start = Instant::now();
    let tokens = get_tokens_by_jieba(content);
    let duration = start.elapsed();
    println!("Time elapsed in expensive_function() is: {:?}", duration);
    println!("{:#?}", tokens);
}

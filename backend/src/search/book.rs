use crate::search::{
    get_tokens_by_jieba, search_documents_or_sort_by_field,
    PredictCardOrBookParams, PredictResult
};
use serde_derive::{Deserialize, Serialize};
use std::collections::{Bound, HashMap};
use std::{fs, thread};
use std::num::NonZero;
use std::time::Instant;
use tantivy::collector::TopDocs;
use tantivy::directory::MmapDirectory;
use tantivy::query::{AllQuery, BooleanQuery, Occur, Query, QueryParser, RangeQuery, TermQuery};
use tantivy::schema::{Field, IndexRecordOption, Schema, TextFieldIndexing, TextOptions, Value, FAST, INDEXED, STORED, STRING};
use tantivy::{doc, Index, IndexWriter, Order, ReloadPolicy, TantivyDocument, Term};
use crate::database::book::BookRepository;
use crate::models::assemble::Assemble;
use crate::models::book::Book;
use crate::utils::path::get_retrieval_dir;
use crate::utils::time_util::{date_str_to_datetime_local, datetime_str_to_datetime_local, now_datetime_local};

struct BookFields {
    id: Field,
    subject_code: Field,
    t_type: Field,
    author: Field,
    series: Field,
    version: Field,
    edition: Field,
    brand: Field,
    period: Field,
    grade: Field,
    publish_date: Field,
    assemble_id: Field,
    book_name: Field,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BookSearchResult {
    pub id: String,
    pub score: f32,
    pub batch: Option<i32>,
    pub author: Option<String>,
    #[serde(rename = "bookName")]
    pub book_name: String,
    pub isbn: String,
    pub publisher: Option<String>,
    pub series: Option<String>,
    #[serde(rename = "coverImageUrl")]
    pub cover_image_url: Option<String>,
    #[serde(rename = "subjectCode")]
    pub subject_code: String,
    #[serde(rename = "subjectName")]
    pub subject_name: String,
    #[serde(rename = "type")]
    pub r#type: String,
    #[serde(rename = "updateTime")]
    pub update_time: Option<String>,
    pub version: Option<String>,
    pub edition: Option<String>,
    #[serde(rename = "publishDate")]
    pub publish_date: Option<String>,
    pub brand: Option<String>,
    pub period: Option<i32>,
    pub grade: Option<String>,
}

/// 打开或创建知识卡片索引
async fn open_or_create_book_index() -> Result<(BookFields, Schema, Index), Box<dyn std::error::Error>> {
    let text_field_indexing = TextFieldIndexing::default()
        .set_tokenizer("jieba")
        .set_index_option(IndexRecordOption::WithFreqsAndPositions);
    let text_options = TextOptions::default().set_indexing_options(text_field_indexing);

    let mut schema_builder = Schema::builder();
    let id = schema_builder.add_u64_field("id", INDEXED | FAST | STORED);
    let subject_code = schema_builder.add_text_field("subject_code", STRING | FAST);
    let t_type = schema_builder.add_text_field("t_type", STRING | FAST);
    let author = schema_builder.add_text_field("author", STRING | FAST);
    let series = schema_builder.add_text_field("series", STRING | FAST);
    let version = schema_builder.add_text_field("version", STRING | FAST);
    let edition = schema_builder.add_text_field("edition", STRING | FAST);
    let brand = schema_builder.add_text_field("brand", STRING | FAST);
    let period = schema_builder.add_i64_field("period", INDEXED | FAST);
    let grade = schema_builder.add_text_field("grade", STRING | FAST);
    let publish_date = schema_builder.add_i64_field("publish_date", INDEXED | FAST);
    let assemble_id = schema_builder.add_u64_field("assemble_id", INDEXED | FAST);
    let book_name = schema_builder.add_text_field("book_name", text_options);

    let schema = schema_builder.build();

    let retrieval_dir = get_retrieval_dir()?;
    let index_path = retrieval_dir.join("book");
    if !index_path.as_path().exists() {
        fs::create_dir_all(index_path.as_path())?;
    }

    let dir = MmapDirectory::open(index_path)?;
    let index = Index::open_or_create(dir, schema.clone())?;

    let tokenizer = tantivy_jieba::JiebaTokenizer {};
    index.tokenizers().register("jieba", tokenizer);
    let fields = BookFields {
        id,
        subject_code,
        t_type,
        author,
        series,
        version,
        edition,
        brand,
        period,
        grade,
        publish_date,
        assemble_id,
        book_name,
    };
    Ok((fields, schema, index))
}

async fn bulk_predict_book_results(
    raw_rst: Vec<(u64, f32)>,
) -> Result<Vec<BookSearchResult>, Box<dyn std::error::Error>> {
    let book_ids: Vec<u64> = raw_rst.iter().map(|rt| rt.0).collect();
    let books: Vec<Book> = BookRepository::find_all_by_ids(&book_ids).await?;
    let mut tb_map: HashMap<u64, Book> = HashMap::new();
    for tb in books {
        tb_map.insert(tb.book_id, tb);
    }

    let mut book_search_results: Vec<BookSearchResult> = vec![];
    for (cid, score) in raw_rst {
        let result = tb_map.get(&cid);
        if result.is_none() {
            continue;
        }
        let book = result.unwrap();

        book_search_results.push(BookSearchResult {
            id: book.book_id.to_string(),
            score,
            batch: book.batch,
            author: book.author.clone(),
            book_name: book.book_name.clone(),
            isbn: book.isbn.clone(),
            publisher: book.publisher.clone(),
            series: book.series.clone(),
            cover_image_url: book.snapshots.first().map(|s| s.path.clone()),
            subject_code: book.subject_code.clone(),
            subject_name: book.subject_name.clone(),
            r#type: book.r#type.clone(),
            update_time: book.update_time.clone(),
            version: book.version.clone(),
            edition: book.edition.clone(),
            publish_date: book.publish_date.clone(),
            brand: book.brand.clone(),
            period: book.period,
            grade: book.grade.clone(),
        });
    }

    Ok(book_search_results)
}

/// 创建或更新书本的索引，如果文档id相同，会覆盖旧的索引
/// 在导入教材数据时，调用此方法
pub async fn training_book(
    books: &Vec<Book>,
    book_id_assembles_map: &HashMap<u64, Vec<&Assemble>>,
) -> Result<(), Box<dyn std::error::Error>> {
    if books.is_empty() {
        return Ok(());
    }

    let (fields, _, index) = open_or_create_book_index().await?;

    let reader = index.reader()?;
    let binding = reader.searcher();
    let searcher = &binding;

    // 获取CPU核心数
    let result = thread::available_parallelism();
    let mut core_num = result.unwrap_or(NonZero::<usize>::new(4).unwrap()).get();
    if core_num > 8 {
        core_num = 8;
    }
    let memory_size = core_num * 512_000_000;

    let mut index_writer: IndexWriter = index.writer_with_num_threads(
        core_num, memory_size
    )?;

    for book in books.iter() {
        let opt_assembles = book_id_assembles_map.get(&book.book_id);
        let empty_assembles = Vec::new();
        let assembles = opt_assembles.unwrap_or(&empty_assembles);

        let term = Term::from_field_u64(fields.id, book.book_id);
        // Search for the document by its ID
        let query = TermQuery::new(term.clone(), IndexRecordOption::Basic);
        let top_docs = searcher.search(&query, &TopDocs::with_limit(1))?;
        // If document exists, delete it
        if !top_docs.is_empty() {
            index_writer.delete_term(term);
        }

        let mut doc = doc!(
            fields.id => book.book_id,
            fields.subject_code => book.subject_code.as_str(),
            fields.t_type => book.r#type.as_str(),
            fields.book_name => book.book_name.as_str(),
        );
        if let Some(ref author) = book.author {
            doc.add_text(fields.author, author.as_str());
        }
        if let Some(ref series) = book.series {
            doc.add_text(fields.series, series.as_str());
        }
        if let Some(ref version) = book.version {
            doc.add_text(fields.version, version.as_str());
        }
        if let Some(ref edition) = book.edition {
            doc.add_text(fields.edition, edition.as_str());
        }
        if let Some(ref brand) = book.brand {
            doc.add_text(fields.brand, brand.as_str());
        }
        if let Some(period) = book.period {
            doc.add_i64(fields.period, period as i64);
        }
        if let Some(ref grade) = book.grade {
            doc.add_text(fields.grade, grade.as_str());
        }
        if let Some(ref publish_date) = book.publish_date {
            let result = datetime_str_to_datetime_local(publish_date.as_str(), None);
            if let Ok(datetime_local) = result {
                doc.add_i64(fields.publish_date, datetime_local.timestamp_millis());
            }
        }
        for asm in assembles {
            doc.add_u64(fields.assemble_id, (**asm).assemble_id);
        }
        index_writer.add_document(doc)?;
    }

    index_writer.commit()?;
    index_writer.wait_merging_threads()?;

    Ok(())
}

/// 书本搜索
pub async fn predict_book(
    params: PredictCardOrBookParams,
) -> Result<PredictResult<BookSearchResult>, Box<dyn std::error::Error>> {
    let start = Instant::now();

    let (fields, _, index) = open_or_create_book_index().await?;

    let reader = index
        .reader_builder()
        .reload_policy(ReloadPolicy::OnCommitWithDelay)
        .doc_store_cache_num_blocks(10000)
        .try_into()?;

    let searcher = reader.searcher();

    let mut subqueries: Vec<(Occur, Box<dyn Query>)> = vec![];

    let mut token_texts = vec![];
    let search_text_trim = params.search_text.trim();
    if !search_text_trim.is_empty() {
        // 对关键词进行分词处理
        token_texts = get_tokens_by_jieba(&params.search_text);
        // 使用分词后的关键词进行查询
        let tokens_text = token_texts.join(" ");

        // 搜索content字段
        let query_parser = QueryParser::for_index(&index, vec![fields.book_name]);
        let (content_query, errors)
            = query_parser.parse_query_lenient(tokens_text.as_str());
        if !errors.is_empty() {
            println!("Error occurred when parse_query for content: {}, errors: {:#?}", params.search_text, errors);
        }
        subqueries.push((Occur::Must, content_query));
    }

    if let Some(book_id) = params.book_id {
        let sub_query = TermQuery::new(
            Term::from_field_u64(fields.id, book_id),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref subject_code) = params.subject_code {
        let sub_query = TermQuery::new(
            Term::from_field_text(fields.subject_code, subject_code),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref t_type) = params.t_type {
        let sub_query = TermQuery::new(
            Term::from_field_text(fields.t_type, t_type),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref author) = params.author {
        let sub_query = TermQuery::new(
            Term::from_field_text(fields.author, author),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref series) = params.series {
        let sub_query = TermQuery::new(
            Term::from_field_text(fields.series, series),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref version) = params.version {
        let sub_query = TermQuery::new(
            Term::from_field_text(fields.version, version),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref edition) = params.edition {
        let sub_query = TermQuery::new(
            Term::from_field_text(fields.edition, edition),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref brand) = params.brand {
        let sub_query = TermQuery::new(
            Term::from_field_text(fields.brand, brand),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(period) = params.period {
        let sub_query = TermQuery::new(
            Term::from_field_i64(fields.period, period as i64),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref grade) = params.grade {
        let sub_query = TermQuery::new(
            Term::from_field_text(fields.grade, grade),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }
    if let Some(ref publish_year) = params.publish_year {
        let (earliest_publish_date, recent_publish_date)
            = (format!("{}-01-01", publish_year), format!("{}-12-31", publish_year));
        let (ear_ts, recent_ts) = (
            date_str_to_datetime_local(&earliest_publish_date, None)
                .map(|t| t.timestamp_millis())
                .unwrap_or(0i64),
            date_str_to_datetime_local(&recent_publish_date, None)
                .map(|t| t.timestamp_millis())
                .unwrap_or(now_datetime_local().timestamp_millis()),
        );
        // 构建范围查询
        let range_query = RangeQuery::new(
            Bound::Included(Term::from_field_i64(fields.publish_date, ear_ts)),
            Bound::Included(Term::from_field_i64(fields.publish_date, recent_ts)),
        );
        subqueries.push((Occur::Must, Box::new(range_query)));
    }
    if params.publish_year.is_none() && (params.earliest_publish_date.is_some() || params.recent_publish_date.is_some()) {
        let mut ear_ts = 0i64;
        if let Some(ref t_str) = params.earliest_publish_date {
            if let Ok(tl) = date_str_to_datetime_local(t_str, None) {
                ear_ts = tl.timestamp_millis();
            }
        }
        let mut recent_ts = now_datetime_local().timestamp_millis();
        if let Some(ref t_str) = params.recent_publish_date {
            if let Ok(tl) = date_str_to_datetime_local(t_str, None) {
                recent_ts = tl.timestamp_millis();
            }
        }
        // 构建范围查询
        let range_query = RangeQuery::new(
            Bound::Included(Term::from_field_i64(fields.publish_date, ear_ts)),
            Bound::Included(Term::from_field_i64(fields.publish_date, recent_ts)),
        );
        subqueries.push((Occur::Must, Box::new(range_query)));
    }
    if let Some(assemble_id) = params.assemble_id {
        let assemble_id = assemble_id.parse()?;
        let sub_query = TermQuery::new(
            Term::from_field_u64(fields.assemble_id, assemble_id),
            IndexRecordOption::Basic,
        );
        subqueries.push((Occur::Must, Box::new(sub_query)));
    }

    // 将查询结合在一起
    let combined_query: Box<dyn Query> = if !subqueries.is_empty() {
        Box::from(BooleanQuery::new(subqueries))
    } else {
        Box::from(AllQuery{})
    };

    let page_no = params.page_no.unwrap_or(0);
    let page_size = params.page_size.unwrap_or(10);
    let offset = page_no * page_size;

    let (top_docs, totals) = search_documents_or_sort_by_field::<u64>(
        &searcher, &combined_query, &params.search_text,
        page_size, offset, "assemble_id", Order::Asc
    )?;

    let mut raw_rsts: Vec<(u64, f32)> = vec![];
    for (_score, doc_address) in top_docs {
        // Retrieve the actual content of documents given its `doc_address`.
        let retrieved_doc = searcher.doc::<TantivyDocument>(doc_address)?;
        let temp_list: Vec<(u64, f32)> = retrieved_doc
            .get_all(fields.id)
            .map(|i| i.as_u64())
            .filter(|i| i.is_some())
            .map(|i| (i.unwrap(), _score))
            .collect();
        raw_rsts.extend(temp_list);
    }

    let page_count = if totals % page_size != 0 {
        totals / page_size + 1
    } else {
        totals / page_size
    };

    let duration = start.elapsed();
    let retrieval_duration = duration.as_millis();
    println!("retrieval searching time taken: {} ms", retrieval_duration);

    let result_list = bulk_predict_book_results(raw_rsts).await?;

    let duration = start.elapsed();
    let total_duration = duration.as_millis();
    println!("Predict book time taken: {} ms", total_duration);

    let result = PredictResult {
        totals,
        page_no,
        page_count,
        page_size,
        highlights: token_texts,
        retrieval_duration,
        total_duration,
        list: result_list,
    };

    Ok(result)
}

// #[test]
// fn test01() -> Result<(), Box<dyn std::error::Error>> {
//     let rt = Runtime::new()?;
//     let result: Result<PredictResult<CardSearchResult>, Box<dyn std::error::Error>> =
//         rt.block_on(async {
//             init_db_for_test().await.expect("TODO: panic message");
//             let db = get_db().await;
//             let root_dir =
//                 PathBuf::from(r"C:\Users\<USER>\AppData\Roaming\com.qctchina.proposition-assistant");
//             let params = PredictCardParams {
//                 page_no: None,
//                 page_size: None,
//                 subject_code: "00060",
//                 textbook_id: Some(1657),
//                 search_text: "财政",
//             };
//             Ok(predict_card(&root_dir, &db, params).await?)
//         });
//
//
//     Ok(())
// }

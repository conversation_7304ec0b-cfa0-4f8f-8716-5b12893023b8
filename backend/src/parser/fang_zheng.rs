use encoding_rs::GB18030;
use regex::{Captures, Regex};
use std::fs::File;
use std::io::Read;
use std::path::Path;

pub fn parse_fbd(fbd_path: &Path) -> Result<String, Box<dyn std::error::Error>> {
    let content = read_text_content(fbd_path)?;
    let content = handle_fbd_content(content.as_str());
    Ok(content)
}

pub fn parse_txt(txt_path: &Path) -> Result<String, Box<dyn std::error::Error>> {
    let content = read_text_content(txt_path)?;
    Ok(content)
}

fn read_text_content(fbd_path: &Path) -> Result<String, Box<dyn std::error::Error>> {
    let mut file = File::open(fbd_path)?;
    let mut buffer = Vec::new();
    file.read_to_end(&mut buffer)?;

    let content = GB18030.decode(&buffer).0.into_owned();
    Ok(content)
}

/// 根据给定的起始位置数组将字符串拆分成片段
fn split_at_positions<'a>(text: &'a str, positions: &[usize]) -> Vec<&'a str> {
    if text.is_empty() || positions.is_empty() {
        return vec![text];
    }
    let mut splits = Vec::new();
    let mut current_end = 0;

    let mut char_indices = text.char_indices();
    let mut next_char_index = char_indices.next();

    for &position in positions.iter() {
        while let Some((byte_index, _)) = next_char_index {
            if byte_index >= position {
                break;
            }
            next_char_index = char_indices.next();
        }

        if let Some((byte_index, chr)) = next_char_index {
            if byte_index == position {
                if position > current_end {
                    splits.push(&text[current_end..position]);
                }
                current_end = position + chr.len_utf8();
            } else {
                // 如果位置不在字符边界上，则移动到下一个字符的开始位置
                current_end = byte_index;
            }
        } else {
            // 如果位置超出了字符串范围，则直接跳到字符串末尾
            current_end = text.len();
        }
    }

    if current_end < text.len() {
        // 添加最后一个片段，如果匹配的位置没有到达字符串末尾
        splits.push(&text[current_end..]);
    }

    splits
}

fn handle_fbd_content(content: &str) -> String {
    let anno_bracket_pair_re = Regex::new(r"[〖\[]([a-zａ-ｚA-ZＡ-Ｚ0-9０-９`~｀～!！.．#＃()（）,，@＠。$＄￥^＾&＆/\-－_+=|＿＋＝｜ＸＹ*”;%<>{}＊；％＜＞｛｝\[\]［］]+?)[〗\]]").unwrap();
    let under_dot_re = Regex::new(r"〖ZZ[(]〗(.+?)〖ZZ[)]〗").unwrap();
    let underline_re = Regex::new(r"〖CD#([0-9]+)〗").unwrap();
    let underline1_re = Regex::new(r"\[ZZ\([A-Z]]([ 〓]+?)\[ZZ\)]").unwrap();
    let que_number_line_start_re =
        Regex::new(r"^[ ]*([〖\[][A-Z0-9*/\-()（）”“{}]+[〗\]])*[〓 ]*([〖\[][A-Z0-9*/\-()（）”“{}]+[〗\]])*[ ]*([0-9０-９]+)[ ]*([〖\[](WB|DW)[〗\]])*[ ]*[.、]").unwrap();
    let que_number_line_in_re =
        Regex::new(r"[〓 ]*([0-9０-９]+)[ ]*[〖\[](WB|DW)[〗\]][ ]*[.、]").unwrap();
    let paper_page_count_re = Regex::new(r"\(共\s*[0-9]*\s*页\)").unwrap();

    // Handle question numbers in line
    let processed_content = content
        .lines()
        .map(|line| {
            // 查找所有匹配项的起始位置
            let positions: Vec<_> = que_number_line_in_re
                .find_iter(line)
                .map(|m| m.start())
                .filter(|idx| *idx > 0usize)
                .collect();
            // 根据起始位置拆分字符串
            let split_texts: Vec<String> = split_at_positions(&line, &positions)
                .into_iter()
                .map(|slice| slice.to_string())
                .collect();
            split_texts.join("\n")
        })
        .collect::<Vec<String>>()
        .join("\n");

    // Handle question numbers starting line
    let processed_content = processed_content
        .lines()
        .map(|line| {
            que_number_line_start_re
                .replace(line, |caps: &Captures| format!("{}. ", &caps[3]))
                .into_owned()
        })
        .collect::<Vec<String>>()
        .join("\n");

    // handle under dot
    let processed_content = under_dot_re
        .replace_all(&processed_content, |caps: &Captures| {
            let temp_text = caps.get(1).unwrap().as_str();
            let result = temp_text
                .char_indices()
                .map(|(_idx, c)| format!(r"\(\mathop{{{}}}\limits_{{\tiny \bullet}}\)", c))
                .collect::<Vec<String>>()
                .join("");
            result
        })
        .into_owned();

    // Handle underline
    let processed_content = underline_re
        .replace_all(&processed_content, |caps: &Captures| {
            let count: usize = caps.get(1).unwrap().as_str().parse().unwrap_or(5);
            "_".repeat(count)
        })
        .into_owned();
    // Handle underline1
    let processed_content = underline1_re
        .replace_all(&processed_content, |caps: &Captures| {
            let count: usize = caps.get(1).unwrap().as_str().len();
            "_".repeat(count)
        })
        .into_owned();

    // Handle annotation bracket pairs
    let processed_content = anno_bracket_pair_re
        .replace_all(&processed_content, "")
        .into_owned();

    // Handle various special characters and patterns
    let processed_content = processed_content
        .replace("", "")
        .replace("", "")
        .replace("", "")
        .replace("", "")
        .replace("", "")
        .replace("\n", "\n")
        .replace("", "\n")
        .replace("\n", "\n")
        .replace("", "\n")
        .replace("\n", "")
        .replace("", "")
        .replace("〓", "  ")
        .replace("", "√")
        .replace("", "×");

    // Handle total page count text
    let processed_content = paper_page_count_re
        .replace_all(&processed_content, "")
        .into_owned();

    processed_content
}

#[test]
fn test01() {
    // let que_number_re = Regex::new(r"^[〓 ]+([0-9０-９]+)[.、]").unwrap();
    // let content = "〓1.[ZK(]城市维护建设税的纳税人所在地为市区的，税率为[ZK)]";
    // let processed_content = que_number_re.replace(content, |caps: &regex::Captures| format!("{}. ", &caps[1])).into_owned();
    let fbd_path = Path::new(r"C:\Users\<USER>\Desktop\00457240603.fbd");
    let content = parse_fbd(fbd_path).unwrap();
    println!("content: {}", content);
}

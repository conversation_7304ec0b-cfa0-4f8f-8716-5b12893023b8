use std::path::{Path, PathBuf};
use anyhow::Error;
use crate::parser::fang_zheng::{parse_fbd, parse_txt};
use crate::utils::d2t::d2t;

pub mod fang_zheng;
pub mod paper;

pub fn parse_paper_content(
    file_path: &Path,
    resource_path: &Path,
    app_data_dir: &PathBuf,
) -> Result<String, Box<dyn std::error::Error>> {
    let file_path_str = file_path
        .file_name()
        .unwrap_or_default()
        .to_str()
        .unwrap_or_default();
    let content;
    if file_path_str.ends_with(".docx") || file_path_str.ends_with(".DOCX") {
        content = d2t(
            resource_path,
            file_path,
            Some(app_data_dir.join("userUpload").as_path()),
            Some("userUpload"),
        )?;
    } else if file_path_str.ends_with(".fbd") || file_path_str.ends_with(".FBD") {
        content = parse_fbd(file_path)?;
    } else if file_path_str.ends_with(".txt") {
        content = parse_txt(file_path)?;
    } else {
        return Err(Box::from(Error::msg("仅支持docx、fbd和txt格式的试卷")));
    }
    return Ok(content)
}
use lazy_static::lazy_static;
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs::File;
use std::io::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};
use std::path::Path;

lazy_static! {
    static ref position_map: HashMap<&'static str, &'static str> = HashMap::from([
        ("centering", "center"),
        ("raggedleft", "right"),
        ("raggedright", "left"),
    ]);
}

lazy_static! {
    static ref pinyin_replacement: HashMap<&'static str, &'static str> = HashMap::from([
        (r"\={a}", r"ā"),
        (r"\'{a}", r"á"),
        (r"\v{a}", r"ǎ"),
        (r"\`{a}", r"à"),
        (r"\={e}", r"ē"),
        (r"\'{e}", r"é"),
        (r"\v{e}", r"ě"),
        (r"\`{e}", r"è"),
        (r"\={i}", r"ī"),
        (r"\'{i}", r"í"),
        (r"\v{i}", r"ǐ"),
        (r"\`{i}", r"ì"),
        (r"\={o}", r"ō"),
        (r"\'{o}", r"ó"),
        (r"\v{o}", r"ǒ"),
        (r"\`{o}", r"ò"),
        (r"\={u}", r"ū"),
        (r"\'{u}", r"ú"),
        (r"\v{u}", r"ǔ"),
        (r"\`{u}", r"ù"),
        (r"\={ü}", r"ǖ"),
        (r"\'{ü}", r"ǘ"),
        (r"\v{ü}", r"ǚ"),
        (r"\`{ü}", r"ǜ"),
    ]);
}

lazy_static! {
    static ref rome_replacement: HashMap<&'static str, &'static str> = HashMap::from([
        (r"Ⅰ", "1"),
        (r"Ⅱ", "2"),
        (r"Ⅲ", "3"),
        (r"Ⅳ", "4"),
        (r"Ⅴ", "5"),
        (r"Ⅵ", "6"),
        (r"Ⅶ", "7"),
        (r"Ⅷ", "8"),
        (r"Ⅸ", "9"),
        (r"Ⅹ", "10"),
    ]);
}

lazy_static! {
    static ref german_replacement: HashMap<&'static str, &'static str> = HashMap::from([
        (r#"\"{A}"#, "Ä"),
        (r#"\"{a}"#, "ä"),
        (r#"\"{O}"#, "Ö"),
        (r#"\"{o}"#, "ö"),
        (r#"\"{U}"#, "Ü"),
        (r#"\"{u}"#, "ü"),
    ]);
}

lazy_static! {
    static ref ex_german_replacement: HashMap<&'static str, &'static str> = HashMap::from([
        (r#"\"{Z}"#, "ẞ"),
        (r#"\"{z}"#, "ß"),
        (r#""Z"#, "ẞ"),
        (r#""z"#, "ß"),
    ]);
}

lazy_static! {
    static ref french_replacement: HashMap<&'static str, &'static str> = HashMap::from([
        (r"\c{c}", "ç"),
        (r"\oe{}", "œ"),
        (r#"~"<"#, "《"),
        (r#"~">"#, "》"),
        (r#""<~"#, "《"),
        (r#"">~"#, "》"),
        (r#""<"#, "《"),
        (r#"">"#, "》"),
        (r"\`{a}", "à"),
        (r"\^{a}", "â"),
        (r#"\"{a}"#, "ä"),
        (r"\'{a}", "á"),
        (r"\`{e}", "è"),
        (r"\^{e}", "ê"),
        (r#"\"{e}"#, "ë"),
        (r"\'{e}", "é"),
        (r"\`{i}", "ì"),
        (r"\^{i}", "î"),
        (r#"\"{i}"#, "ï"),
        (r"\'{i}", "í"),
        (r"\`{o}", "ò"),
        (r"\^{o}", "ô"),
        (r#"\"{o}"#, "ö"),
        (r"\'{o}", "ó"),
        (r"\`{u}", "ù"),
        (r"\^{u}", "û"),
        (r#"\"{u}"#, "ü"),
        (r"\'{u}", "ú"),
        (r"\`{A}", "À"),
        (r"\^{A}", "Â"),
        (r#"\"{A}"#, "Ä"),
        (r"\'{A}", "Á"),
        (r"\`{E}", "È"),
        (r"\^{E}", "Ê"),
        (r#"\"{E}"#, "Ë"),
        (r"\'{E}", "É"),
        (r"\`{I}", "Ì"),
        (r"\^{I}", "Î"),
        (r#"\"{I}"#, "Ï"),
        (r"\'{I}", "Í"),
        (r"\`{O}", "Ò"),
        (r"\^{O}", "Ô"),
        (r#"\"{O}"#, "Ö"),
        (r"\'{O}", "Ó"),
        (r"\`{U}", "Ù"),
        (r"\^{U}", "Û"),
        (r#"\"{U}"#, "Ü"),
        (r"\'{U}", "Ú"),
    ]);
}

lazy_static! {
    static ref spanish_replacement: HashMap<&'static str, &'static str> = HashMap::from([
        (r"\`{A}", "À"),
        (r"\^{A}", "Â"),
        (r#"\"{A}"#, "Ä"),
        (r"\'{A}", "Á"),
        (r"\`{E}", "È"),
        (r"\^{E}", "Ê"),
        (r#"\"{E}"#, "Ë"),
        (r"\'{E}", "É"),
        (r"\`{I}", "Ì"),
        (r"\^{I}", "Î"),
        (r#"\"{I}"#, "Ï"),
        (r"\'{I}", "Í"),
        (r"\`{O}", "Ò"),
        (r"\^{O}", "Ô"),
        (r#"\"{O}"#, "Ö"),
        (r"\'{O}", "Ó"),
        (r"\`{U}", "Ù"),
        (r"\^{U}", "Û"),
        (r#"\"{U}"#, "Ü"),
        (r"\'{U}", "Ú"),
        (r"\~{n}", "ñ"),
        (r"\~{N}", "Ñ"),
    ]);
}

lazy_static! {
    static ref ascii_p: Regex = Regex::new(r"[\x00-\xff]+").unwrap();
    static ref zh_hans_p: Regex = Regex::new(r"([\u4e00-\u9fa5])").unwrap();
    static ref zh_hans_p2: Regex =
        Regex::new(r"^[A-Za-z0-9 .,&‘（(-]+[\u4e00-\u9fa5]+[）)]+").unwrap();
    static ref zh_notes_p: Regex = Regex::new(r"[（(][\u4e00-\u9fa5]{1,10}[）)]").unwrap();
    static ref charset_p: Regex =
        Regex::new(r"\\(?:[宋黑楷]体|TimesNewRoman|CambriaMath|Arial|SimSun)\{([^}]*?)\}").unwrap();
    static ref document_begin_end_p: Regex =
        Regex::new(r"\\begin\{document\}([\s\S]+?)\\end\{document\}").unwrap();
    static ref table_p: Regex = Regex::new(r"\\begin\{table\}[\s\S]*?\\end\{table\}").unwrap();
    static ref td_span_p: Regex = Regex::new(r#"(?:col|row)span="\d+""#).unwrap();
    static ref examine_p: Regex =
        Regex::new(r"%[\s\S]*?20\d\d-\d\d-\d\dT\d\d:\d\d:\d\d[\s\S]*$").unwrap();
    static ref page_foot_p: Regex = Regex::new(r"第 *\d+ *页 *[(（] *共 *\d+ *页 *[）)]").unwrap();
    static ref full_width_character: Regex = Regex::new(r"[\uFF00-\uFFEF\u4E00-\u9FA5]").unwrap();
    static ref number_p: Regex = Regex::new(r"(\d+)").unwrap();
    static ref pure_number_p: Regex = Regex::new(r"^(\d+)$").unwrap();
    static ref in_brace_p: Regex = Regex::new(r"\{([\s\S]*?)\}").unwrap();
    static ref pure_option_p: Regex = Regex::new(r"^([A-G])$").unwrap();
    static ref all_caption_p: Regex = Regex::new(r"^[ \t~]*[(（]([\s\S]*?)[）)][ \t~]*$").unwrap();
    static ref arraybackslash_p: Regex =
        Regex::new(r"\\(centering|raggedright)\\arraybackslash\{([\s\S]*?)\}").unwrap();
    static ref arraybackslash_p2: Regex = Regex::new(r"\\(centering|raggedright)\{\}").unwrap();
    static ref includegraphics_p: Regex =
        Regex::new(r"\\includegraphics\[([^]]*?)]\{([^}]*?)\}").unwrap();
    static ref includegraphics_greedy_p: Regex =
        Regex::new(r"\\includegraphics((?:\[[^]]*?])?)\{([^}]*)\}").unwrap();
    static ref image_before_p: Regex =
        Regex::new(r#"(<img[^>]*?src=[\"\']?.*?[\"\']?[^>]*?/?>) *(\d+[.．、])"#).unwrap();
    static ref double_quotation_marks_p: Regex = Regex::new(r"``([^`\']*?)\'\'").unwrap();
    static ref double_subscripts_p: Regex =
        Regex::new(r"_\{([^\{\}]*?)\} *_\{([^\{\}]*?)\}").unwrap();
    static ref float_number_p: Regex = Regex::new(r"(\d+)\.(\d+)").unwrap();
    static ref latex_p: Regex =
        Regex::new(r"\\[a-zA-Z]+?(?:\{[\s\S]*?\})?|[\^_]\{[\s\S]+?\}| & |^}|\{$").unwrap();
    static ref latex_end_p: Regex = Regex::new(r"\\end\{(.*?)\}").unwrap();
    static ref tensor_latex_p: Regex = Regex::new(r"\\tensor\*?\[(.*?)]").unwrap();
    static ref align_p: Regex = Regex::new(r#"<p +align=\"(.*?)\">"#).unwrap();
    static ref align_half_p: Regex = Regex::new(r#"</?p( +align=\"(.*?)\")?>"#).unwrap();
    static ref align_full_p: Regex =
        Regex::new(r#" *<p +align=\"(.*?)\">([\s\S]*?)< */p *> *"#).unwrap();
    static ref align_in_latex_p: Regex =
        Regex::new(r#"\$ *(<p +align=\".*?\">)([\s\S]*?)(< */p *>) *\$"#).unwrap();
    static ref null_p: Regex = Regex::new(r"^ *$").unwrap();
}

lazy_static! {
    static ref ding_p: Regex = Regex::new(r"\\ding\{(\d+)\}").unwrap();
    static ref item_p: Regex = Regex::new(r"\\item\[(.*?)]").unwrap();
    static ref poem_space_p: Regex = Regex::new(r"`+([^\'`]+?)\'+").unwrap();
    static ref long_space_p: Regex = Regex::new(r" {4,}").unwrap();
    static ref short_left_right_p: Regex = Regex::new(r"\\left(\\?[\{\[(|])([^\\]*?)\\right(\\?[\}\])|])").unwrap();
    static ref left_right_point_p: Regex = Regex::new(r"\\left\.(.+?)\\right\.").unwrap();
    static ref begin_end_minipage_p: Regex = Regex::new(r"\\begin\{minipage\}(?:\[[\s\S]*?])?(?:\{[\s\S]*?\})?([\s\S]*?)\\end\{minipage\}").unwrap();
    static ref begin_end_description_p: Regex = Regex::new(r"\\begin\{description\}(?:\[[\s\S]*?])?(?:\{[\s\S]*?\})?([\s\S]*?)\\end\{description\}").unwrap();
    static ref fbox_p: Regex = Regex::new(r"\\fbox\{(.*?)\}").unwrap();
    static ref bold_p: Regex = Regex::new(r"\\textbf\{(.*?)\}").unwrap();
    static ref so_p: Regex = Regex::new(r"\\so\{(.*?)\}").unwrap();
    static ref text_p: Regex = Regex::new(r"\\text\{(.*?)\}").unwrap();
    static ref text_format_p: Regex = Regex::new(r"\\(?:textup|textsc|textit|textnormal|mathrm)\{(.*?)\}").unwrap();
    static ref textemdash_p: Regex = Regex::new(r"\\textemdash\{(.*?)\}").unwrap();
    static ref textcolor_p: Regex = Regex::new(r"\\textcolor\{.*?\}\{(.*?)\}").unwrap();
    static ref textcolor_p2: Regex = Regex::new(r"\\textcolor\{[^\{\}]+?\{(.*?)\}\}").unwrap();
    static ref textcolor_greedy_p: Regex = Regex::new(r"\\textcolor\{.*?\}\{(.*)\}").unwrap();
    static ref raisebox_p: Regex = Regex::new(r"\\raisebox\{.*?\}\{(.*?)\}").unwrap();
    static ref raisebox_greedy_p: Regex = Regex::new(r"\\raisebox\{.*?\}\{(.*?)\}").unwrap();
    static ref parbox_p: Regex = Regex::new(r"\\parbox\{(?:\\textwidth)?\}\{([\s\S]*?)\}").unwrap();
    static ref section_p: Regex = Regex::new(r"\\section\{([\s\S]*?)\}").unwrap();
    static ref subsection_p: Regex = Regex::new(r"\\subsection\{([\s\S]*?)\}").unwrap();
    static ref href_p: Regex = Regex::new(r"\\href\{.*?\}\{([\s\S]*?)\}").unwrap();
    static ref office_layout_p: Regex = Regex::new(r"^ *\\(centering|raggedright|raggedleft)\{\}([\s\S]*)$").unwrap();
    static ref copyright_p: Regex = Regex::new(r"(此资料来源于[\s\S]+谢绝转载。?|\[来源:.*?]|学科网高考一轮复习微课视频手机观看地址：http://xkw\.so/wksp|drawingml2svg|\\pagebreak|&nbsp;|菁优网版权所有|学科网版权所有|版权所有|钱老师[\s\S]+曹老师[\s\S]+\d)").unwrap();
    static ref selectlanguage_p: Regex = Regex::new(r"\\selectlanguage\{.*?\}").unwrap();
    static ref foreignlanguage_p: Regex = Regex::new(r"\\foreignlanguage\{(.*?)\}\{(.*?)\}").unwrap();
    static ref ul_p: Regex = Regex::new(r"\\ul\{([\s\S]*?)\}").unwrap();
    static ref ul2_p: Regex = Regex::new(r"(\\_[ ]?){2,}").unwrap();
    static ref ul3_p: Regex = Regex::new(r"_{2,}").unwrap();
    static ref uline_p: Regex = Regex::new(r"\\uline\{([\s\S]*?)\}").unwrap();
    static ref underline_p: Regex = Regex::new(r"\\underline\{([\s\S]+?)\}").unwrap();
    static ref underline_p2: Regex = Regex::new(r"_(\d+)_").unwrap();
    static ref underline_p3: Regex = Regex::new(r"[（_ ](\d+)(?:_|[）.]? *(?:_{2,}|\$\\underline\{ +))").unwrap();
    static ref underline_p4: Regex = Regex::new(r"\$+((\\_)+)\$+").unwrap();
    static ref error_dollar_p: Regex = Regex::new(r"\$\{(\\[^}]+?)\$\}").unwrap();
    static ref error_underline_p: Regex = Regex::new(r"\$*\\underline\{((\\?_)+.*?(\\?_)+)\}\$*").unwrap();
    static ref error_underline_p2: Regex = Regex::new(r"\$*\\underline\{(.*?(\\?_)+)\}\$*").unwrap();
    static ref error_underline_p3: Regex = Regex::new(r"\$*\\underline\{((\\?_)+.*?)\}\$*").unwrap();
    static ref error_underline_p4: Regex = Regex::new(r"\$\\underline\{\\textcolor\{color-[\dA-F]+\}\$\{([\s\S]+?)\}\}").unwrap();
    static ref error_double_quotation_p: Regex = Regex::new(r"`+([\s\S]+?)(\\?\')+").unwrap();
    static ref error_option_dot_p: Regex = Regex::new(r"([\s\S]*[A-N])· *([\u4e00-\u9fa5A-Za-z]+[\s\S]*)").unwrap();
    static ref single_dollar_include_p: Regex = Regex::new(r"\$([\s\S]+?)\$").unwrap();
    static ref space_in_brackets_p: Regex = Regex::new(r"[(（]([ ~]*)[)）]").unwrap();
    static ref setcounter_p: Regex = Regex::new(r"\\setcounter\{enumi+\}\{(\d+)\}").unwrap();
    static ref caption_before_question_number_p: Regex = Regex::new(r"^[ \t~]*([(（][单多]选[)）])[ \t~]*([1-9１２３４５６７８９][\d１２３４５６７８９]{0,2})(?:[(（] *\d+ *分 *[）)])?[.．~]+([\s\S]*)").unwrap();
    static ref td_p: Regex = Regex::new(r"<td[^>]*?>([\s\S]+?)</td>").unwrap();
    static ref is_latex_p: Regex = Regex::new(r"\\[a-zA-Z]+?(?:\{[\s\S]*?\})?|[\^_]\{[\s\S]+?}| & |^\}|\{$").unwrap();
    static ref no_align_lr_p: Regex = Regex::new(r"\\(left|right)[a-z]").unwrap();
    static ref left_mark_p: Regex = Regex::new(r"\\left\\?(\(|\[|\{|.|\|)").unwrap();
    static ref right_mark_p: Regex = Regex::new(r"\\right\\?(\)|]|}|.|\|)").unwrap();
    static ref latex_in_text_p: Regex = Regex::new(r"(?:(?:\\[a-z]+)?(?:\[[\s\S]+])?[\^_\\]?\{[\s\S]+?\})+").unwrap();
    static ref latex_begin_end_p: Regex = Regex::new(r"\\begin\{.*?\}.*?\\end\{.*?\}").unwrap();
    static ref space_after_double_p: Regex = Regex::new(r"\$ +").unwrap();
    static ref strip_latex_p: Regex = Regex::new(r"\{([-:]+)\}").unwrap();
    static ref item_in_enumerate_p: Regex = Regex::new(r"\\begin\{enumerate\}([\s\S]*?\\item[\s\S]*?)\\end\{enumerate\}").unwrap();
    static ref begin_end_array_p: Regex = Regex::new(r"\\begin\{array\}([\s\S]*?)\\end\{array\}").unwrap();
    static ref begin_end_table_p: Regex = Regex::new(r"\\begin\{table\}([\s\S]*?)\\end\{table\}").unwrap();
    static ref begin_enumerate_p: Regex = Regex::new(r"\\begin\{enumerate\} *(\[|(\ etcounter\{(?:enumii?\}\{)?))([\s\S]*?)[]\}]+").unwrap();
    static ref end_enumerate_p: Regex = Regex::new(r"\\end\{enumerate\}").unwrap();
    static ref begin_end_enumerate_p: Regex = Regex::new(r"\\begin\{enumerate\} *(\[|(\ etcounter\{(?:enumi+}\{)?))([\s\S]*?)[]\}]+([\s\S]*?)\\end\{enumerate\}").unwrap();
    static ref item_in_description_or_enumerate_p: Regex = Regex::new(r"\\begin\{(enumerate|description)\}([\s\S]*?\\item[\s\S]*?)\\end\{(?:enumerate|description)\}").unwrap();
    static ref greedy_item_in_description_or_enumerate_p: Regex = Regex::new(r"\\begin\{(enumerate|description)\}([\s\S]*\\item[\s\S]*)\\end\{(?:enumerate|description)\}").unwrap();
}

lazy_static! {
    static ref colorbox_p: Regex = Regex::new(r"\\colorbox\{.*?\}\{(.*?)\}").unwrap();
    static ref colorbox_greedy_p: Regex = Regex::new(r"\\colorbox\{.*?\}\{(.*)\}").unwrap();
}

lazy_static! {
    static ref ignore_p: Regex = Regex::new(r"第.+?部分.+?题").unwrap();
    static ref start_number_p: Regex = Regex::new(r"^(\d+)").unwrap();
    static ref start_option_p: Regex = Regex::new(r"^([A-G])").unwrap();
    static ref simple_item_p: Regex = Regex::new(r"\\item\[?(.*?)]?").unwrap();
    static ref question_line_p: Regex = Regex::new(r"\n\d+[．.、]").unwrap();
    static ref question_number_inline_p: Regex = Regex::new(r"(\d+)[.．、~]").unwrap();
    static ref multi_answer_p: Regex = Regex::new(r"([A-F]+)").unwrap();
    static ref mult_line_answer_p: Regex = Regex::new(r"(\d+)\s*[-~—]\s*(\d+)([A-G\s]+)").unwrap();
    static ref multi_line_answer_p: Regex = Regex::new(r"(\d+)[-~]+(\d+)[:：.．、\s]*([A-N\s.]+[A-N])[\s.]*").unwrap();
    static ref multi_question_answer_p: Regex = Regex::new(r"(\d+)(?:[.．、]|[.．、 ]*【解答】)([A-N ]*[A-N])").unwrap();
    static ref multi_question_long_answer_p: Regex = Regex::new(r"(\d+)(?:[.．、]|[.．、 ]*【解答】) *( +)(?: |$)").unwrap();
    static ref multi_option_answer_p: Regex = Regex::new(r"([A-K ]*[A-K])").unwrap();
    static ref table_option_p: Regex = Regex::new(r"([A-G]) *&").unwrap();
    static ref option_dot_f: String = String::from(r"[．.、\]]");
    static ref material_title_p: Regex = Regex::new(r"^[ \n]*(（|材料)?[一二三四五六七八九十~]").unwrap();
    static ref single_material_p: Regex = Regex::new(r"[左右上下][图表][^现]").unwrap();
    static ref common_material_p: Regex = Regex::new(r"共用.*?(?:选项|备选|题干)").unwrap();
    static ref not_material_p: Regex = Regex::new(r"[(（] *[）)]").unwrap();
    static ref english_material_title_p: Regex = Regex::new(r"^[ \n]*[(（]?[一二三四五六七八九十A-Z][A-Za-z0-9 ]{0,3}[)）]?[ \n]*$").unwrap();
    static ref chinese_material_title_p: Regex = Regex::new(r"^[ \n]*[\u4e00-\u9fa5A-Za-z0-9].*?[\u4e00-\u9fa5A-Za-z0-9,，。:：][ \n]*$").unwrap();
    static ref chinese_material_title_p2: Regex = Regex::new(r"^[ \n]*[\u4e00-\u9fa5A-Za-z0-9].*?[\u4e00-\u9fa5A-Za-z0-9,，。:：][ \n]*$").unwrap();
    static ref question_number_p: Regex = Regex::new(r"^[ \t~]*(\d+)").unwrap();
    static ref except_option_p: Regex = Regex::new(&(r"^ *A *([\s\S]*B[．.、\]][\s\S]*C[．.、\]][\s\S]*D[．.、\]][\s\S]*)$")).unwrap();
    static ref question_number_split_p: Regex = Regex::new(r"^[ \t~]*([1-9１２３４５６７８９][\d１２３４５６７８９]{0,2})(?:[(（][\d.．]+ *分[)）])?[.．~]+([\s\S]*)").unwrap();
    static ref not_option_p: Regex = Regex::new(r"(?:故?选择?)?[A-H](?:正确|错误)").unwrap();
    static ref no_sub_question_qt_p: Regex = Regex::new(r"默写").unwrap();
    static ref sub_question_span_p: Regex = Regex::new(r"(\d+)[-~～〜至到、](\d+)").unwrap();
    static ref sub_question_span_p2: Regex = Regex::new(r"(\d+)[-~～〜至到、](\d+) *[两三四五六七八九十]*个?小?[问题]").unwrap();
    static ref sub_question_span_in_addition_p: Regex = Regex::new(r"【(\d+)题(详解|答案|解答)】").unwrap();
    static ref has_sub_question_p: Regex = Regex::new(r"第? *(\d+) *[-、~～〜至到]第? *(\d+) *[两三四五六七八九十]*个?小?[问题]").unwrap();
    static ref sub_question_number_p: Regex = Regex::new(r"^[ \t~]*[(（](\d+)[)）]").unwrap();
    static ref image_analysis_p: Regex = Regex::new(r"(【解答】[ABCDＡＢＣＤАВС]\n*)(<img.*?)").unwrap();
    static ref addition_p2: Regex = Regex::new(r"^[\s\S]{0,2}(解答?|答案|详解|解析|分析|知识点|考点|点评|点睛|专题)[:：　]").unwrap();
    static ref has_options_p: Regex = Regex::new(r"(^(选择|[单多双][选项])|项选择|阅读|完[型形]|听力|语言知识|运用)").unwrap();
    static ref option_head_p: Regex = Regex::new(r"[A-D]").unwrap();
    static ref option_line_p: Regex = Regex::new(r"A +B +C +").unwrap();
    static ref option_addition_p: Regex = Regex::new(r"^ *([A-Z]+) *$").unwrap();
    static ref option_addition_p2: Regex = Regex::new(r"^ *([A-Z]+) *(答案|解析|解答)").unwrap();
    static ref option_addition_p3: Regex = Regex::new(r"^ *([A-Z]+)").unwrap();
    static ref option_split_p: Regex = Regex::new(&(r"[ABCDＡＢＣＤАВС][．.、\]]".to_owned())).unwrap();
    static ref image_p: Regex = Regex::new(r#"<img[^>]*?src=["\'](.*?)["\'][^>]*?/?>"#).unwrap();
    static ref image_p2: Regex = Regex::new(r#"\\u003cimg *?src\\u003d\\"(.*?)\\"[^>]*?/?\\u003e"#).unwrap();
    static ref start_image_p: Regex = Regex::new(r#"^ *<img[^>]*?src=[\"\']?(.*?)[\"\']?[^>]*?/?>"#).unwrap();
    static ref end_image_p: Regex = Regex::new(r#"<img[^>]*?src=[\"\']?(.*?)[\"\']?[^>]*?/?> *$"#).unwrap();
    static ref need_image_p: Regex = Regex::new(r"([如下据右上][图表]|[图表][中示甲乙AB])").unwrap();
    static ref tag_p: Regex = Regex::new(r"^<[^>]+?>").unwrap();
    static ref start_tag_p: Regex = Regex::new(r"< *[^/]*?>").unwrap();
    static ref question_number_maybe_reset_p: Regex = Regex::new(r"[ABCD]组 *?(综合提升)|建议用时[:：] *\d+分钟|[:：] *$").unwrap();
    static ref question_number_maybe_not_reset_p: Regex = Regex::new(r"^\d+[.．]\d+ +|[:：；] *$").unwrap();
    static ref question_in_align_p: Regex = Regex::new(&(r#"<p +align=\".*?\">([1-9]\d{0,2} *[．.、\]])"#.to_owned())).unwrap();
    static ref end_with_next_p: Regex = Regex::new(r"[:：] *$|下表|如表|如下|如图|根据").unwrap();
    static ref begin_brackets_p: Regex = Regex::new(r"^[(（]([ ]*?)[)）]").unwrap();
    static ref score_header_p: Regex = Regex::new(r"共 *\d+ *分").unwrap();
    static ref exercises_p: Regex = Regex::new(r"\d+\.\d+ *习题").unwrap();
    static ref line_answer_p: Regex = Regex::new(r"(\d+)\s*[.．、]\s*([A-G]+)").unwrap();
    static ref english_listen_p: Regex = Regex::new(r"[读听][一两三]遍|听.*?(对话|录音|独白)|听下面一段较?长?对话").unwrap();
    static ref english_sub_question_in_material_p: Regex = Regex::new(r"\\underline\{(?:\\quad| |\\text\{)*(\d+)(?:\\quad| |})*\}|_+ *(\d+) *_+").unwrap();
    static ref begin_caption_p: Regex = Regex::new(r"^[ \t~]*[(（]([^  \t\b)）]{2,10}?)[)）]").unwrap();
    static ref end_caption_p: Regex = Regex::new(r"[(（]([^  \t\b)）]{2,10}?)[)）]$").unwrap();
    static ref score_caption_p: Regex = Regex::new(r"^\d+[.．、] *[(（]([\d.．]+ *分)[)）]").unwrap();
}

lazy_static! {
    static ref pick_up_selection: Vec<Regex> = vec![
        Regex::new(r"故选[:：] *([A-G]+)").unwrap(),
        Regex::new(r"故本?题?选 *([A-G]+)").unwrap(),
        Regex::new(r"所以选 *([A-G]+)").unwrap(),
        Regex::new(r"答案[是为选][:：]? *([A-G]+)").unwrap(),
        Regex::new(r"正确的[是为][:：]? *([A-G]+)").unwrap(),
        Regex::new(r"^ *([A-G]+) *【[答解]").unwrap(),
        Regex::new(r"排除[A-G、，]+[,，。 ]+故?选择?([A-G]+)").unwrap(),
        Regex::new(r"[A-G、，]+(?:错误|正确)[,，。 ]+故?选择?([A-G]+)").unwrap(),
    ];
}

lazy_static! {
    static ref info_p1: Regex = Regex::new(r"(答题[卡纸])|(色字迹.*?笔)").unwrap();
    static ref info_p2: Regex = Regex::new(r"注意事项[：:]|注意[：:]回答第|考试时间|考试说明|考试结束|座位号|不准使用涂改液|检查条形码粘贴|准考证|本试卷|密封线内|用到的相对原子质量|色字迹.*?笔|试题?卷上作?答题?无效|全卷满分|考生须知|试卷和答题卡一并[交收]回|试卷第\d+-\d+题为.*?部分|满分.*?\d+分.*?\d+分钟|\d+分钟.*?满分.*?\d+分|注意保存试卷类型|在答题[纸卡]相应的位置|答题前，请认真阅读答题纸上的《注意事项》|不得传抄|写在答题卡|考试形式|本试题分选择题和非选择题两部分|请务必在答题卡规定的答题区域内作答|请将试题卷和答题卡都交给监考老师|请(?:考生)?将答案[填写答]+在答题卡上|[写答]在试题卷上的答案无效|[写答]在“?试题?卷”?是?上无效|答题区域[\s\S]*作答|不能答在试题?卷上|不按(以上)?要求作答(的答案)?无效|按要求用笔|学生可登陆|涂写考试号|严禁使用涂改|严禁在答题卡上做任何标记|第I卷每小题选出答案|作答无效|全卷共.道大题，总分\d+分|不要直接在试卷上答题|本试题分第Ⅰ卷和第Ⅱ卷两部分|以下数据可供解题时参考|草稿纸[\s\S]{0,10}答题[\s\S]{0,10}无效|用\s*(?:2B)?\s*铅笔涂?[把将在].*(?:答题[卡卷纸]上|涂黑)|可能用到的相关公式或参数|在.*答[题案].*作答|写到答题卡题号所指示的答题区域|填写好?自己的(姓名|班级|考号|、)+|试卷中自由落体加速度取|选涂其[他它]答案|填[写涂]在(?:试卷和)?答题卡|试卷整洁，字迹清晰|答题卡上准确填写|本?试[卷题]和答(题[卡卷]|案)一并交回|保持[答题卡面的]+[整清]洁|用黑色签字笔直接答在答题卡上对应的答题区域内|(本卷)?命题范[围国]|答案用钢笔或圆珠笔写在试卷上|笔将答案填?写在答题卡上|本卷共\d+[题分]|全部答案在答题卡上完成|签字笔写在答题卡上|请将答案正确填写在|在答题纸上填写姓名和考号|答题纸与试卷在试题编号上是一一对应的|(?:务必)?[先在将把]+(自己的)?.*?(姓名|考号)|必须使?用[\s\S]{0,20}黑色.*签[字宇]笔)|按要求答卷|将答案书写在专设答题页规定的位置上|超出答题区域|监考人员|未按要求作答|折叠答题卡|请[\S]{0,4}答题之前|用黑色字迹.*?笔|本卷.*?考查内容|一律(不得分|无效)|不按.*?要求.*?作答的答案无效|第\s*\d+\s*页.*?共\s*\d+\s*页|满分\d+分\s*，\s*共\d+分|本卷共\d+小?题|姓名.*?考号|[请用].*铅笔填涂|作答参考时限|考试时限为|姓名和准考证|任选一题作答|影响评分.*后果自负|全卷取g=|考试范围[:：]|答题[\s\S]*?字[迹体][\s\S]*?工整|考试内容[\s\S]*?新课标|答题[\s\S]+签字[\s\S]*笔|请[\s\S]*核对[\s\S]*答题卡|测试范围：[\s\S]*(?:高考|[选必]修)|[填涂]在答题[卡纸卷]|答题卡").unwrap();
    static ref paper_name_p1: Regex = Regex::new(r"(\d+)学?[年届].{1,30}(卷|考试|质量监测|月考|[一二三四五六七八九十]模|模拟|统[考测]|学业水平|检测|适应性测试|调研测试)").unwrap();
    static ref paper_name_p2: Regex = Regex::new(r"(\d+)?学?年?(普通高等学校招生全国统一考试|第[一二三]学期|全国[甲乙丙丁]卷|高[一二三中考][\S]*?[语文数学英物理化生地政治历史]{2,2}[\S]*?汇编)").unwrap();
    static ref paper_name_p3: Regex = Regex::new(r"^\\s*专题[\(（]?[\d一二三四五六七八九十]+[\)）]?|湘豫名校|[文理]综[语文数学英物理化生地政治历史]{2,2}试[题卷]").unwrap();
    static ref header_p: Regex = Regex::new(r"^\\(?:textbf|subsection)\{(.*)\}?$/").unwrap();
    static ref other_header_p1: Regex = Regex::new(r"^(<img src=[^>]*>)?\\textbf\{(.*?)\}$|^((\\textbf\{)|^(<img src=[^>]*>))?[一二三四五六七八九十]+[．、.]|本大?题共\d+分|本大?[题卷]共\d+小?题|符合题目要求的|非?选择题|^\s*?（[一二三四五六七八九十]+）[\s\S]{2,10}题|^\[?选修\d+|全部选对得满分|语言(基础|知识)运用|(选择|填空|解答)题|[必选]考题|卷[\s\S]主观题|^((\\textbf\{)|^(<img src=[^>]*>))?[on]+[．、.][\s\S]+题").unwrap();
    static ref other_header_p2: Regex = Regex::new(r"^（[一二三四五六七八九十]+）").unwrap();
    static ref other_header_p3: Regex = Regex::new(r"(每段)?(对话|独白)(或独白)?[仅只]?读.遍|Section [A-H]|^[ABCD][)）]|^（?第[一二三四五六七八九十q]+节[:：（）\s(]+|^第.{1,3}(卷|部分)|七选五|单词拼写|书面表达").unwrap();
    static ref other_header_p4: Regex = Regex::new(r"^高频考点|循环定向集中练|难点强化针对练|小题组合短平快|【.*?卷】").unwrap();
    static ref other_header_p5: Regex = Regex::new(r"^（?第[一二三四五六七八九十]+节[:：（）\s]+|^[一二三四五六七八九十]+[．、.]|^第.{1,3}(卷|部分)|\d+[-﹣~～〜至到和第、\s]+\d+\s*([两三四五六七八九十]+个)?小?\s*题为\S+部分").unwrap();
    static ref other_header_p6: Regex = Regex::new(r"^[\u{2160}-\u{217F}IV]+[．、.]").unwrap(); // 使用Unicode范围代替罗马数字
    static ref question_p1: Regex = Regex::new(r"^[\s\t~]*([1-9１２３４５６７８９][\d１２３４５６７８９]{0,2})\s*[.．]~*([\s\S]+)").unwrap();
    static ref question_p2: Regex = Regex::new(r"^[\s\t~]*([(（]?[1-9１２３４５６７８９][\d１２３４５６７８９]{0,2}[.．）)]+)([\s\S]+)").unwrap();
    static ref question_p3: Regex = Regex::new(r"^[\s\t~]*([（(]\d+[）)])([\s\S]+)").unwrap();
    static ref question_p3_2: Regex = Regex::new(r"^[\s\t~]*([（]\d+[）])([\s\S]+)").unwrap();
    static ref question_p4: Regex = Regex::new(r"^[\s\t~]*([1-9１２３４５６７８９][\d１２３４５６７８９]{0,2})\s*[.．、]([\s\S]+)").unwrap();
    static ref question_p5: Regex = Regex::new(r"^[\s\t~]*([(（]?[1-9１２３４５６７８９][\d１２３４５６７８９]{0,2}\s*[.．、）)]+)([\s\S]+)").unwrap();
    static ref question_p6: Regex = Regex::new(r"^(\\item\s)([\s\S]+)").unwrap();
    static ref question_p7: Regex = Regex::new(r"^(.*?)([\s\S]+、作答要求)").unwrap();
    static ref question_p8: Regex = Regex::new(r"^问题(\d+)\s*[.．]~*([\s\S]*)").unwrap();
    static ref single_choice_question_p: Regex = Regex::new(r"(最符合题目要求的一项|只有一(个选)?[个项][是最]*(正确|符合题))").unwrap();
    static ref material_p1: Regex = Regex::new(r"(回答|完成|下列|下面)\s*第?\s*(\d+)[-﹣~～〜至到和第、\s]+(\d+)\s*([两三四五六七八九十]+个)?小?\s*题|据此完\s*成(下面)?[小问]题|读下面的.*?(回答|完成|判断)[问两小各]题|(回答|完成|判断)(以下|下列|[下后]面|下|各|小)[问两小各]*题|回答下面小\s*题|阅读(下面|以下|下列)").unwrap();
    static ref material_p2: Regex = Regex::new(r"^(材料[一二三四五六七八九十~]+)[\s:：]*\s+").unwrap();
    static ref material_p3: Regex = Regex::new(r".*?(\\underline\{\_+\d+\_+\}).*?|[读听][一两三]遍|阅读短文|读下面短文|将文中画线部分译成|短文填空|完成句子|每个空只写").unwrap();
    static ref material_p4: Regex = Regex::new(r"^【(.*?)】").unwrap();
    static ref material_p5: Regex = Regex::new(r"^（[一二三四五六七八九十]+）").unwrap();
    static ref material_p6: Regex = Regex::new(r"非连续性文本阅读|阅读文言文").unwrap();
    static ref qno_p: Regex = Regex::new(r"^[\s\t~]*(\d+)[.．、~〜]").unwrap();
    static ref qno_span_p: Regex = Regex::new(r"\s*(\d+)\s*[-﹣~～〜至到和、]\s*(\d+)\s*[小)）]?题").unwrap();
    static ref option_p: Regex = Regex::new(r"^[\s]*\[?[ABCDEFGＡＢＣＤАВС][．.、\]](.*)").unwrap();
    static ref answer_split_p: Regex = Regex::new(r"([1-9１２３４５６７８９][\d１２３４５６７８９]{0,2})[．.、\]]\s*([ABCDＡＢＣＤАВС]+)").unwrap();
    static ref question_answer_p: Regex = Regex::new(r"(\d+)[．.、\]]([ABCDＡＢＣＤАВС\s]+)").unwrap();
    static ref addition_p1: Regex = Regex::new(r"^(?:\s*\d+[.．]\s*[ABCDEFGＡＢＣＤАВС]+)?\s*【(解答|[\d~题问]*答案|[\d~题问]*详解|解析|分析|小问\d+详解|\d+题详解|知识点|考点|考点定位|点评|点睛|专题|参考译文)】").unwrap();
    static ref addition_p3: Regex = Regex::new(r"^\s*(\d+[.．])\s*[ABCDEFGＡＢＣＤАВС]*?\s*【(解答|[\d~题问]*答案|[\d~题问]*详解|解析|分析|小问\d+详解|\d+题详解|知识点|考点|考点定位|点评|点睛|专题|参考译文)】").unwrap();
    static ref addition_p4: Regex = Regex::new(r"正确答案是\s*([A-D]+)").unwrap();
    static ref addition_p5: Regex = Regex::new(r"[A-D]项，|选[A-D][正确错误]+|选项[A-D]").unwrap();
    static ref addition_p6: Regex = Regex::new(r"审题的?关键|解题的?步骤").unwrap();
    static ref answer_paper_p: Regex = Regex::new(r"参考答案(</p>)?$|(^答案[和与]解析$)|答案版|答案与卡片|[卷（]解析部分|试题解析|评[分卷](细则|参考|建议)|^(参考)?答案[以及解析]+[:：]?$|答案\s*</p>$").unwrap();
    static ref answer_paper_p2: Regex = Regex::new(r"参考答案(</p>)?$|(^答案[和与]解析$)|答案版|答案与卡片|[卷（]解析部分|试题解析|评[分卷](细则|参考|建议)|^(参考)?答案[以及解析]+[:：]?$|答案\s*</p>$|[语文数学英物理化生地政治历史]{2,2}答案\s*$").unwrap();
}

lazy_static! {
    static ref pattern: [(&'static str, Vec<Regex>); 11] = [
        ("option", vec![
            Regex::new(r"^[\s]*\[?[ABCDEFGＡＢＣＤАВС][．.、\]]([\s\S]*)").unwrap()
        ]),
        ("addition", vec![
            Regex::new(r"^(?:\s*\d+[.．]\s*[ABCDEFGＡＢＣＤАВС]+)?\s*【(解答|[\d~题问]*答案|[\d~题问]*详解|解析|分析|小问\d+详解|\d+题详解|知识点|考点|考点定位|点评|点睛|专题|参考译文)】").unwrap(),
            Regex::new(r"^[\s\S]{0,2}(解答?|答案|详解|解析|分析|知识点|考点|点评|点睛|专题)[:：　]").unwrap()
        ]),
        ("info", vec![
            Regex::new(r"(注意事项[：:]|注意[：:]回答第|考试时间|考试说明|考试结束|座位号|不准使用涂改液|检查条形码粘贴|准考证|本试卷|密封线内|用到的相对原子质量|色字迹.*?笔|试题?卷上作?答题?无效|全卷满分|考生须知|试卷和答题卡一并[交收]回|试卷第\d+-\d+题为.*?部分|满分.*?\d+分.*?\d+分钟|\d+分钟.*?满分.*?\d+分|注意保存试卷类型|在答题[纸卡]相应的位置|答题前，请认真阅读答题纸上的《注意事项》|不得传抄|写在答题卡|考试形式|本试题分选择题和非选择题两部分|请务必在答题卡规定的答题区域内作答|请将试题卷和答题卡都交给监考老师|请(?:考生)?将答案[填写答]+在答题卡上|[写答]在试题卷上的答案无效|[写答]在“?试题?卷”?是?上无效|答题区域[\s\S]*作答|不能答在试题?卷上|不按(以上)?要求作答(的答案)?无效|按要求用笔|学生可登陆|涂写考试号|严禁使用涂改|严禁在答题卡上做任何标记|第I卷每小题选出答案|作答无效|全卷共.道大题，总分\d+分|不要直接在试卷上答题|本试题分第Ⅰ卷和第Ⅱ卷两部分|以下数据可供解题时参考|草稿纸[\s\S]{0,10}答题[\s\S]{0,10}无效|用\s*(?:2B)?\s*铅笔涂?[把将在].*(?:答题[卡卷纸]上|涂黑)|可能用到的相关公式或参数|在.*答[题案].*作答|写到答题卡题号所指示的答题区域|填写好?自己的(姓名|班级|考号|、)+|试卷中自由落体加速度取|选涂其[他它]答案|填[写涂]在(?:试卷和)?答题卡|试卷整洁，字迹清晰|答题卡上准确填写|本?试[卷题]和答(题[卡卷]|案)一并交回|保持[答题卡面的]+[整清]洁|用黑色签字笔直接答在答题卡上对应的答题区域内|(本卷)?命题范[围国]|答案用钢笔或圆珠笔写在试卷上|笔将答案填?写在答题卡上|本卷共\d+[题分]|全部答案在答题卡上完成|签字笔写在答题卡上|请将答案正确填写在|在答题纸上填写姓名和考号|答题纸与试卷在试题编号上是一一对应的|(?:务必)?[先在将把]+(自己的)?.*?(姓名|考号)|必须使?用[\s\S]{0,20}黑色.*签[字宇]笔)|按要求答卷|将答案书写在专设答题页规定的位置上|超出答题区域|监考人员|未按要求作答|折叠答题卡|请[\S]{0,4}答题之前|用黑色字迹.*?笔|本卷.*?考查内容|一律(不得分|无效)|不按.*?要求.*?作答的答案无效|第\s*\d+\s*页.*?共\s*\d+\s*页|满分\d+分\s*，\s*共\d+分|本卷共\d+小?题|姓名.*?考号|[请用].*铅笔填涂|作答参考时限|考试时限为|姓名和准考证|任选一题作答|影响评分.*后果自负|全卷取g=|考试范围[:：]|答题[\s\S]*?字[迹体][\s\S]*?工整|考试内容[\s\S]*?新课标|答题[\s\S]+签字[\s\S]*笔|请[\s\S]*核对[\s\S]*答题卡|测试范围：[\s\S]*(?:高考|[选必]修)|[填涂]在答题[卡纸卷]|答题卡").unwrap()
        ]),
        ("header", vec![
            Regex::new(r"^\\(?:textbf|subsection)\{(.*)\}?$/").unwrap(),
            Regex::new(r"^(<img src=[^>]*>)?\\textbf\{(.*?)\}$|^((\\textbf\{)|^(<img src=[^>]*>))?[一二三四五六七八九十a-z]+[．、.]|本大?题共\d+分|本大?[题卷]共\d+小?题|符合题目要求的|非?选择题|^\s*?（[一二三四五六七八九十]+）[\s\S]{2,10}题|^\[?选修\d+|全部选对得满分|语言(基础|知识)运用|(选择|填空|解答)题|[必选]考题|卷[\s\S]主观题|^((\\textbf\{)|^(<img src=[^>]*>))?[on]+[．、.][\s\S]+题").unwrap(),
            Regex::new(r"(每段)?(对话|独白)(或独白)?[仅只]?读.遍|Section [A-H]|^[ABCD][)）]|^（?第[一二三四五六七八九十a-z]+节[:：（）\s(]+|^第.{1,3}(卷|部分)|七选五|单词拼写|书面表达").unwrap()
        ]),
        ("question", vec![
            Regex::new(r"^[\s\t~]*([1-9１２３４５６７８９][\d１２３４５６７８９]{0,2})\s*[.．、]~*([\s\S]*)").unwrap(),
            Regex::new(r"^(.*?)([\s\S]+、作答要求)").unwrap()
        ]),
        ("sub_question", vec![
            Regex::new(r"^[\s\t~]*([（(]\d+[）)])([\s\S]+)").unwrap(),
            Regex::new(r"^问题(\d+)\s*[.．]~*([\s\S]*)").unwrap()
        ]),
        ("material", vec![
            Regex::new(r"(回答|完成|下列|下面)\s*第?\s*(\d+)[-﹣~～〜至到和第、\s]+(\d+)\s*([两三四五六七八九十]+个)?小?\s*题|据此完\s*成(下面)?[小问]题|读下面的.*?(回答|完成|判断)[问两小各]题|(回答|完成|判断)(以下|下列|[下后]面|下|各|小)[问两小各]*题|回答下面小\s*题|阅读(下面|以下|下列)").unwrap(),
            Regex::new(r"^(材料[一二三四五六七八九十~]+)[\s:：]*\s+").unwrap(),
            Regex::new(r".*?(\\underline\{\_+\d+\_+\}).*?|[读听][一两三]遍|阅读短文|读下面短文|将文中画线部分译成|短文填空|完成句子|每个空只写").unwrap()
        ]),
        ("paper", vec![
            Regex::new(r"(\d+)学?[年届].{1,30}(卷|考试|质量监测|月考|[一二三四五六七八九十]模|模拟|统[考测]|学业水平|检测|适应性测试|调研测试)").unwrap(),
            Regex::new(r"(\d+)?学?年?(普通高等学校招生全国统一考试|第[一二三]学期|全国[甲乙丙丁]卷|高[一二三中考][\S]*?[语文数学英物理化生地政治历史]{2,2}[\S]*?汇编)").unwrap(),
            Regex::new(r"^\\s*专题[\(（]?[\d一二三四五六七八九十]+[\)）]?|湘豫名校|[文理]综[语文数学英物理化生地政治历史]{2,2}试[题卷]").unwrap(),
            Regex::new(r"绝密.+启用前").unwrap()
        ]),
        ("answer_paper", vec![
            Regex::new(r"参考答案(</p>)?$|(^答案[和与]解析$)|答案版|答案与卡片|[卷（]解析部分|试题解析|评[分卷](标准|细则|参考|建议)|^(参考)?答案[以及解析]+[:：]?$|答案\s*</p>$").unwrap()
        ]),
        ("image", vec![
            Regex::new(r#"<img[^>]*?src=["\'](.*?)["\'][^>]*?/?>"#).unwrap()
        ]),
        ("tag", vec![
            Regex::new(r"^<[^>]+?>").unwrap()
        ])
    ];
}

const KEY_MARK: &[(&str, char)] = &[("\\{", '♏'), ("\\}", '♌')];

// 定义sub_latex函数
fn sub_latex(text: String, match_p: &Regex, sub_f: impl Fn(String) -> String) -> String {
    let last_i = 0;
    let mut spans = Vec::new();
    let mut text = text.clone();
    let pre_text = text.clone();

    // 替换键值
    for &(k, v) in KEY_MARK {
        text = text.replace(k, &v.to_string());
    }

    // 遍历匹配项
    for match_item in match_p.find_iter(&text) {
        let mut span = [match_item.start(), match_item.end()];
        if span[0] < last_i {
            continue;
        }
        let mut latex = match_item.as_str();
        let mut t: String;
        let (mut lc, mut rc) = (latex.matches('{').count(), latex.matches('}').count());
        if lc > rc {
            for (last_i, c) in text[span[1]..].char_indices() {
                let new_c = span[1] + last_i;
                t = format!("{}{}", latex, c);
                latex = &t;
                if c == '}' {
                    rc += 1;
                    if lc == rc {
                        span[1] = new_c + 1;
                        break;
                    }
                } else if c == '{' {
                    lc += 1;
                    if lc == rc {
                        span[1] = new_c + 1;
                        break;
                    }
                }
            }
        }
        if !text.contains(r"\begin") {
            lc = 0;
            let limit = text.chars().count() - 1;
            for (i, c) in latex.char_indices() {
                if i >= limit {
                    break;
                }
                if c == '{' {
                    lc += 1;
                } else if c == '}' {
                    lc -= 1;
                    if lc < 1 {
                        let next_char_is = if latex.chars().nth(i + 1) == Some('$') {
                            1
                        } else {
                            0
                        };
                        if latex.chars().nth(i + 1) != Some('{')
                            && (text[span[0]..span[0] + i].matches('$').count() + next_char_is) % 2
                                == 0
                        {
                            span[1] = span[0] + i + 1;
                            break;
                        }
                    }
                }
            }
        }
        spans.push(span);
    }

    // 反向处理跨度
    for span in spans.into_iter().rev() {
        let mut new_text = String::new();
        new_text.push_str(&text.split_at(span[0]).0);
        new_text.push_str(&sub_f(text[span[0]..span[1]].to_string()));
        new_text.push_str(&text.split_at(span[1]).1);
        text = new_text;
    }

    // 如果文本改变了并且包含\begin，则递归调用sub_latex
    if text != pre_text && match_p.is_match(r"\begin") && text.contains(r"\begin") {
        text = sub_latex(text, match_p, sub_f);
    }

    // 恢复键值
    for &(k, v) in KEY_MARK {
        text = text.replace(v, k);
    }

    text
}

// 将$$改为\(\)
fn trans_dollar(text: String) -> String {
    vec!["\\(", text.trim_matches('$'), "\\)"].join("")
}

fn group1(m: &regex::Captures) -> String {
    m.get(1).map(|m| m.as_str().to_string()).unwrap_or_default()
}

fn to_greedy(p: &Regex) -> Regex {
    let pattern_str = p.as_str();
    let greedy_pattern = pattern_str.replace("*?", "*").replace("+?", "+");
    Regex::new(&greedy_pattern).unwrap()
}

fn fix_underline(m: &regex::Captures) -> String {
    let inner = m.get(1).unwrap().as_str();
    let mut inner = inner.replace("_{", "[sub_script]{");
    inner = inner.replace("\\[sub_script]{", "\\_{");
    inner = inner.replace("\\_", "_");
    inner = inner.replace("__", "\\quad");
    inner = inner.replace("_", "\\quad");
    inner = inner.replace("[sub_script]{", "_{");
    format!(r"\underline{{{}}}", inner)
}

fn fix_latex(text: String) -> String {
    if text.chars().count() < 3 {
        return text;
    }
    let mut splits: Vec<String> = Vec::new();
    let mut temp: String = String::from("");
    let mut in_latex: bool = false;
    let limit: usize = text.chars().count() - 2;
    let complete_limit = limit as isize;
    let mut index: isize = -1;
    while index <= complete_limit {
        index += 1;
        let i: usize = index as usize;
        let c: char = text.chars().nth(i).unwrap();
        if c == '$' && (i == 0 || text.chars().nth(i - 1) != Some('\\')) {
            if in_latex {
                if i < limit && text.chars().nth(i + 1) != Some('$') {
                    if temp == "$$" {
                        temp.clear();
                    } else {
                        in_latex = false;
                        temp.push(c);
                        splits.push(temp.clone());
                        temp.clear();
                        continue;
                    }
                }
            } else {
                in_latex = true;
                if temp.len() > 0 {
                    splits.push(temp.clone());
                    temp.clear();
                }
            }
        }
        temp.push(c);
    }
    if temp.len() > 0 {
        splits.push(temp.clone());
        temp.clear();
    }
    let mut result: Vec<String> = Vec::new();
    for split in splits {
        if split.starts_with('$') && split.ends_with('$') {
            result.push(trans_dollar(split));
        } else {
            result.push(split.replace("\\_", "_").replace("\\%", "%"));
        }
    }
    result.join("").to_string()
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct Cell {
    content: String,
    attrs: Option<std::collections::HashMap<String, String>>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct Row {
    cells: Vec<Cell>,
}

#[derive(Debug, Clone)]
struct TableConverter {
    dest_rows: Vec<Row>,
    rowspan_skip_status: std::collections::HashMap<usize, (usize, usize)>,

    tabular_p: Regex,
    multi_row_p: Regex,
    multi_row_gp: Regex,
    multi_col_p: Regex,
    multi_col_gp: Regex,
    html: String,
}

fn complete(latex: &str, text: &str) -> String {
    let mut latex = latex.to_string();
    let mut lc = latex.matches('{').count();
    let mut rc = latex.matches('}').count();
    let mut i = 0;
    let le = text.chars().count();
    while lc > rc && i < le {
        latex.push(text.chars().nth(i).unwrap());
        if text.chars().nth(i).unwrap() == '{' {
            lc += 1;
        } else if text.chars().nth(i).unwrap() == '}' {
            rc += 1;
        }
        if lc == rc {
            break;
        }
        i += 1;
    }
    latex
}

impl TableConverter {
    fn new() -> Self {
        let tabular_p = Regex::new(r"\\begin\{(tabularx?)}\{.*?}?.*?\\hline(.*?)\\end\{(tabularx?)}").unwrap();
        let multi_row_p = Regex::new(r"\\multirow(?:\[\S+?])?\{(\d*)}\{[\s\S]*}\s*\{([\s\S]*?)}").unwrap();
        let multi_row_gp = Regex::new(r"\\multirow(?:\[\S+?])?\{(\d*)}\{[\s\S]*}\s*\{([\s\S]*)}").unwrap();
        let multi_col_p = Regex::new(r"\\multicolumn\{(\d*)}\{[\s\S]*}\s*\{([\s\S]*?)}\s*([\s\S]*)").unwrap();
        let multi_col_gp = Regex::new(r"\\multicolumn\{(\d*)}\{[\s\S]*}\s*\{([\s\S]*)}([\s\S]*)").unwrap();

        let html = "<style>\
                    table, th, td { border: 1px solid black; border-collapse: collapse; text-align: center; }\
                    </style>";

        TableConverter {
            dest_rows: Vec::new(),
            rowspan_skip_status: std::collections::HashMap::new(),
            tabular_p,
            multi_row_p,
            multi_row_gp,
            multi_col_p,
            multi_col_gp,
            html: html.into(),
        }
    }

    fn handle_row(&mut self, row: &str) -> Row {
        let mut to_row = Row { cells: Vec::new() };
        let invalid_mark: String = "/INVALID-COL/".into();
        let mut cols: Vec<String> = Vec::new();
        for tmp in row.replace("\\&", "<amp>").split('&').collect::<Vec<&str>>() {
            cols.push(tmp.to_string());
        }
        let mut idx = 0;
        let limit = cols.len() - 1;
        while idx < limit {
            let mut diff = 1;
            while cols[idx].contains("\\begin") && cols[idx].matches('{').count() > cols[idx].matches('}').count() {
                cols[idx] = format!("{}{}", cols[idx], cols[idx + diff]);
                cols[idx + diff] = invalid_mark.clone();
                diff += 1;
                if idx + diff > limit {
                    break;
                }
            }
            idx += diff;
        }
        for (idx, col) in cols.iter().enumerate() {
            if *col == invalid_mark {
                continue;
            }
            let mut skip = 0;
            let mut row_span_count = 0;
            if let Some(&(ref s, ref c)) = self.rowspan_skip_status.get(&idx) {
                skip = *s;
                row_span_count = *c;
            } else {
                let last_multirow_cell = self.find_last_row_span_cell(idx);
                if let Some(last_multirow_cell) = last_multirow_cell {
                    row_span_count = last_multirow_cell.attrs.unwrap()["rowspan"].parse::<usize>().unwrap_or(0);
                }
            }
            if skip + 1 == row_span_count {
                self.rowspan_skip_status.remove(&idx);
                skip = 0;
                row_span_count = 0;
            }
            if skip < row_span_count && col.trim().is_empty() {
                skip += 1;
                self.rowspan_skip_status.insert(idx, (skip, row_span_count));
                continue;
            }

            let mut cell = Cell { content: "".into(), attrs: None };
            let multi_row = self.multi_row_p.captures(col);
            let multi_col = self.multi_col_p.captures(col);
            let col = col.replace("\\\\", "<br/>").replace("<amp>", "&");

            if let Some(multi_row) = multi_row {
                let compared: String = complete(multi_row.get(0).unwrap().as_str(), &col);
                let multi_row = self.multi_row_gp.captures(&compared);
                if let Some(multi_row) = multi_row {
                    let row_span = multi_row.get(1).unwrap().as_str();
                    let content = multi_row.get(2).unwrap().as_str();
                    cell.content = content.into();
                    cell.attrs = Some(std::collections::HashMap::from([("rowspan".into(), row_span.into())]));
                    to_row.cells.push(cell);
                }
            } else if let Some(multi_col) = multi_col {
                let compared: String = complete(multi_col.get(0).unwrap().as_str(), &col);
                let multi_col = self.multi_col_gp.captures(&compared);
                if let Some(multi_col) = multi_col {
                    let col_span = multi_col.get(1).unwrap().as_str();
                    let content = format!("{}{}", multi_col.get(2).unwrap().as_str(), multi_col.get(3).unwrap().as_str());
                    cell.content = content.into();
                    cell.attrs = Some(std::collections::HashMap::from([("colspan".into(), col_span.into())]));
                    to_row.cells.push(cell);
                }
            } else {
                cell.content = col.trim().into();
                to_row.cells.push(cell);
            }
        }
        to_row
    }

    fn convert_answer_table(&mut self, latex: &str) -> String {
        let answer_p: Regex = Regex::new(r"(\d+)\s*[.．][~\s]*([A-Z]+)").unwrap();
        let mut answers: Vec<String> = Vec::new();
        let mut min_qno: i32 = 999;
        let mut max_qno: i32 = 0;
        for row in latex.split("\\hline") {
            if row.contains('&') {
                for col in row.split('&') {
                    if let Some(m) = answer_p.captures(col) {
                        let curr_qno: i32 = m.get(1).unwrap().as_str().parse::<i32>().unwrap();
                        if curr_qno > max_qno {
                            max_qno = curr_qno;
                        }
                        if curr_qno < min_qno {
                            min_qno = curr_qno;
                        }
                        answers.push(format!("{}. {}", curr_qno, m.get(2).unwrap().as_str()));
                    }
                }
            }
        }
        if answers.len() > 0 && answers.len() as i32 == max_qno - min_qno + 1 {
            let mut temp: Vec<String> = Vec::new();
            temp.push("\n".to_string());
            for qno in min_qno..max_qno + 1 {
                let mut i: usize = 0;
                for answer in &answers {
                    if answer.starts_with(format!("{}. ", qno).as_str()) {
                        temp.push(answer.clone());
                        answers.remove(i);
                        break;
                    }
                    i += 1;
                }
            }
            temp.join("\n").to_string()
        } else {
            latex.to_string()
        }
    }

    fn convert_table(&mut self, latex: &str) -> String {
        let row_splitter = "[**--paper-mark--**]".to_string();
        let latex = latex.replace("\\raggedright\\arraybackslash{}", "");
        if let Some(search) = self.tabular_p.captures(&latex) {
            let mut table: String = String::from("<table>");
            let rows = search.get(2).unwrap().as_str().split("\\hline").filter_map(|x| x.trim().parse::<String>().ok());
            for h_line in rows {
                if h_line == row_splitter {
                    continue;
                }
                for row in Regex::new(r"\\cline\{.*?}").unwrap().split(&h_line) {
                    let row: Row = self.handle_row(row.trim());
                    self.dest_rows.push(row);
                }
            }
            self.handle_rows();
            for row in &self.dest_rows {
                for cell in &(row.cells) {
                    if cell.content != row_splitter {
                        table.push_str("<tr>");
                        for c in &(row.cells) {
                            if let Some(attrs) = &c.attrs {
                                table.push_str(&self.convert_td(&c.content, attrs));
                            } else {
                                table.push_str(&self.convert_td(&c.content, &std::collections::HashMap::new()));
                            }
                        }
                        table.push_str("</tr>");
                        break;
                    }
                }
            }
            self.dest_rows.clear();
            self.rowspan_skip_status.clear();
            format!("{}{}</table>", self.html, table)
        } else {
            latex
        }
    }

    fn convert_td(&self, col: &str, col_attr: &std::collections::HashMap<String, String>) -> String {
        let latex_search_p: Regex = Regex::new(r"\\[a-zA-Z]+?(?:\{[\s\S]*?\})?|[\^_]\{[\s\S]+?\}| & |^}|\{$").unwrap();
        let attrs = if col_attr.is_empty() {
            "".into()
        } else {
            col_attr.iter().map(|(k, v)| format!(" {}=\"{}\"", k, v)).collect::<Vec<_>>().join(" ")
        };

        let mut col = col.replace("\\centering\\arraybackslash{}", "");
        col = col.replace("\\raggedright\\arraybackslash{}", "");
        if let Some(_) = latex_search_p.find(&col) {
            if !col.contains('$') {
                col = format!("${}$", col);
            }
        }
        format!("<td{}>{}</td>", attrs, col.trim_end_matches('\\'))
    }

    fn handle_rows(&mut self) {
        let row_splitter = "[**--paper-mark--**]".to_string();
        let mut row_spans = Vec::new();
        for (ri, row) in self.dest_rows.iter().enumerate() {
            for (ci, cell) in row.cells.iter().enumerate() {
                if let Some(ref attrs) = cell.attrs {
                    if attrs.contains_key("rowspan") {
                        let span = attrs["rowspan"].parse::<usize>().unwrap_or(0);
                        let mut row_span = std::collections::HashMap::with_capacity(3);
                        row_span.insert("row".to_string(), ri);
                        row_span.insert("col".to_string(), ci);
                        row_span.insert("span".to_string(), span);
                        row_spans.push(row_span);
                        break;
                    }
                }
            }
        }
        for rowspan in &row_spans {
            let (rs, cs) = (rowspan.get("row"), rowspan.get("col"));
            let rs: usize = *rs.unwrap();
            let cs: usize = *cs.unwrap();
            for row in &mut self.dest_rows[rs + 1..rs + rowspan.get("span").unwrap()] {
                if cs >= row.cells.len() {
                    continue;
                }
                if row.cells[cs].content == "" || row.cells[cs].content == "<br/>" || row.cells[cs].content == row_splitter {
                    row.cells.remove(cs);
                }
            }
        }
    }

    fn find_last_row_span_cell(&self, pos: usize) -> Option<Cell> {
        if self.dest_rows.is_empty() {
            return None;
        }
        let mut pos: usize = pos.clone();
        for dest_row in self.dest_rows.iter().rev() {
            let dest_cols = &dest_row.cells;
            for dest_col in dest_cols {
                if let Some(ref attrs) = dest_col.attrs {
                    if attrs.contains_key("colspan") {
                        let sub: usize = attrs["colspan"].parse::<usize>().unwrap_or(1) - 1;
                        if sub > pos {
                            pos = 0;
                        } else {
                            pos -= sub;
                        }
                    }
                }
            }
            if pos >= dest_cols.len() {
                continue;
            } else {
                let rowspan_col = &dest_cols[pos];
                if let Some(ref attrs) = rowspan_col.attrs  {
                    if attrs.contains_key("rowspan") {
                        return Some((*rowspan_col).clone())
                    }
                }
            }
        }
        None
    }
}

fn fix_answer_table(lines: Vec<String>) -> Vec<String> {
    let mut table_converter = TableConverter::new();
    let row_splitter = "[**--paper-mark--**]".to_string();
    let mut content = lines.join(&row_splitter.clone()).clone();
    for table in Regex::new(r"\\begin\{table}[\s\S]*?\\end\{table}").unwrap().find_iter(&content.clone()) {
        let origin_table = table.as_str();
        let table = origin_table.replace("\\$", "＄").replace("$", "");
        let mut nt = table.replace("$$", "$");
        nt = table_converter.convert_answer_table(&nt);
        content = content.replace(&format!("$${}$$", origin_table), &nt);
        content = content.replace(&format!("${}$", origin_table), &nt);
        content = content.replace(origin_table, &nt);
    }
    let mut result: Vec<String> = Vec::new();
    for ct in content.clone().split(&row_splitter) {
        result.push(ct.to_string());
    }
    result
}

fn fix_table(lines: Vec<String>) -> Vec<String> {
    let mut table_converter = TableConverter::new();
    let row_splitter = "[**--paper-mark--**]".to_string();
    let mut content = lines.join(&row_splitter.clone()).clone();
    for table in Regex::new(r"\\begin\{table}[\s\S]*?\\end\{table}").unwrap().find_iter(&content.clone()) {
        let origin_table = table.as_str();
        let table = origin_table.replace("\\$", "＄").replace("$", "");
        let mut nt = table.replace("$$", "$");
        nt = table_converter.convert_table(&nt);
        content = content.replace(&format!("$${}$$", origin_table), &nt);
        content = content.replace(&format!("${}$", origin_table), &nt);
        content = content.replace(origin_table, &nt);
    }
    let mut result: Vec<String> = Vec::new();
    for ct in content.clone().split(&row_splitter) {
        result.push(ct.to_string());
    }
    result
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Charset {
    pub content: String,
}

fn replace_charset(line: String) -> String {
    let mut result = line.clone();
    for match_ in charset_p.find_iter(&line) {
        let group = match_.as_str();
        let mut out = String::new();
        let parts: Vec<&str> = group
            .get(group.find('{').unwrap() + 1..group.find('}').unwrap())
            .unwrap()
            .split(' ')
            .collect();
        for part in parts {
            let padded_part = format!("{:04}", part);
            let decoded = u32::from_str_radix(&padded_part, 16)
                .ok()
                .and_then(|n| std::char::from_u32(n));
            if let Some(c) = decoded {
                let ch = String::from(c);
                out.push_str(&ch);
            }
        }
        result = result.replace(group, &out);
    }
    result
}

fn replace_keyword(lines: Vec<String>) -> Vec<String> {
    let remove_replacement: Vec<Regex> = vec![
        Regex::new(r"\\includegraphics\[([^]]*?)]\{([^}]*?)}").unwrap(),
        Regex::new(r"(此资料来源于[\s\S]+谢绝转载。?|\[来源:.*?]|学科网高考一轮复习微课视频手机观看地址：http://xkw\.so/wksp|drawingml2svg|\\pagebreak|&nbsp;|菁优网版权所有|学科网版权所有|版权所有|钱老师[\s\S]+曹老师[\s\S]+\d)").unwrap(),
        Regex::new(r"\\selectlanguage\{.*?}").unwrap()
    ];
    let group1_replacement: Vec<Regex> = vec![
        Regex::new(r"\\fbox\{(.*?)}").unwrap(),
        Regex::new(r"\\textbf\{(.*?)}").unwrap(),
        Regex::new(r"\\so\{(.*?)}").unwrap(),
        Regex::new(r"\\(?:textup|textsc|textit|textnormal|mathrm)\{(.*?)}").unwrap(),
        Regex::new(r"\$*\\underline\{((\\?_)+.*?(\\?_)+)}\$*").unwrap(),
        Regex::new(r"\\textcolor\{[^{}]+?\{(.*?)}}").unwrap(),
        Regex::new(r"\\colorbox\{.*?}\{(.*?)}").unwrap(),
        Regex::new(r"\\raisebox\{.*?}\{(.*?)}").unwrap(),
        Regex::new(r"\\section\{([\s\S]*?)}").unwrap(),
        Regex::new(r"\\subsection\{([\s\S]*?)}").unwrap(),
        Regex::new(r"\\href\{.*?}\{([\s\S]*?)}").unwrap(),
        Regex::new(r"\\parbox\{(?:\\textwidth)?}\{([\s\S]*?)}").unwrap(),
    ];
    let underline_replacement: Vec<Regex> = vec![
        Regex::new(r"\\ul\{([\s\S]*?)}").unwrap(),
        Regex::new(r"\\uline\{([\s\S]*?)}").unwrap(),
        Regex::new(r"\\underline\{([\s\S]+?)}").unwrap(),
    ];
    let keyword_replacement: HashMap<&'static str, &'static str> = HashMap::from([
        (r"\$", "＄"),
        // (r"\ ", " "),
        (r"imgsrc", "img src"),
        (r"drawingml2svg", ""),
        (r"\backslash", "\\"),
        (r"\vec ", "\\rightharpoonup "),
        (r"\complement", "∁"),
        (r"\nsubset", r"⊄"),
        (r"\female", "♀"),
        (r"\male", "♂"),
        (r"\textbar", r"|"),
        (r"\times", r"×"),
        (r"\textgreater", r"＞"),
        (r"\textless", r"＜"),
        (r"\EuclidMathTwo{F0DC}", "⫋"),
        (r"\hat ", "︿"),
        (r"\textperthousand", r"%"),
        (r"\textquestiondown", r"¿"),
        (r"\textexclamdown", r"¡"),
        (r"\textbullet", r"‧"),
        (r"\ldots", r"..."),
        (r"\infty", "∞"),
        (r"\leavemode", r" "),
        (r"\pounds", r"£"),
        (r"\texteuro", r"€"),
        (r"\textsubscript{", r"_{"),
        (r"\textsuperscript{", r"^{"),
        (r"\partial", "∂"),
        (r"\textemdash{}", r"-"),
        (r"\textendash{}", r"-"),
        (r"\textasciitilde{}", r"~"),
        (r"\textcircled(A)", "Ⓐ"),
        (r"\textcircled(V)", "Ⓥ"),
        (r"\textcircled(G)", "Ⓖ"),
        (r"(\Circle , A)", "Ⓐ"),
        (r"(\Circle ,V)", "Ⓥ"),
        (r"(\Circle ,G)", "Ⓖ"),
        (r" lash", "/"),
        (r"\privateuse{}", "    "),
        (r"\par", "\\\\"),
        (r" etminus", "\\"),
        (r"\u\row", r"\uparrow"),
        (r"\chapter", r"\textbf"),
        (r"\overset{\right\}harpoonup }", r"\overrightarrow"),
        (r"\Diamondblack", r"\blacklozenge"),
        (r"\ding{71}", r"\diamond"),
        (r"\textbackslash", "\\"),
        (r"\textasciicircum{}", "^"),
        (r"\\allel", r"//"),
        (r"\newline", ""),
        (r"\begin{flushleft}", r#"<p align="left">"#),
        (r"\end{flushleft}", r"</p>"),
        (r"\begin{flushright}", r#"<p align="right">"#),
        (r"\end{flushright}", r"</p>"),
        (r"\begin{center}", r#"<p align="center">"#),
        (r"\end{center}", r"</p>"),
        (r"{---}{---}", r"——"),
        (r"\hspace{0pt}", r" "),
        ("选项中有两项为多余选项", "七选五，选项中有两项为多余选项"),
        ("括号内单词的正确形式", "括号内单词的正确形式，语法填空"),
        ("答案与解析", "【解答】"),
        ("参考答案：", "【解答】"),
        ("答案解析", "【解答】"),
        ("答案:", "【解答】"),
        ("答案：", "【解答】"),
        ("【参考答案】", "【解答】"),
        ("【答案示例】", "【解答】"),
        ("【答案】", "【解答】"),
        ("【试题分析】", "【解析】"),
        ("【试题解析】", "【解析】"),
        ("试题分析:", "【解析】"),
        ("试题分析：", "【解析】"),
        ("【分析】", "【解析】"),
        ("【学科网考点定位】", "【考点】"),
        ("知识点的应用及延伸", "考点"),
        ("知识点应用及拓展", "考点"),
        ("重要知识点分析", "考点"),
        ("重要知识点归纳", "考点"),
        ("知识点的认识", "考点"),
        ("知识点归纳", "考点"),
        ("知识点拓展", "考点"),
        ("知识点定位", "考点"),
        ("重要知识点", "考点"),
        ("考点归纳", "考点"),
        ("关键句", "考点"),
        ("知识点", "考点"),
        ("解题方法点拨", "点评"),
        ("点评点拨", "点评"),
        ("名师点睛", "点评"),
        ("点睛", "点评"),
        ("立意", "点评"),
        ("亮点说明", "点评"),
        ("解题思路", "点评"),
        (r"% D2T: Empty equation removed!", ""),
        (r"{在组卷网浏览本卷}", ""),
        (
            r"本试卷的题干、答案和解析均由组卷网（http://zujuan.xkw.com）专业教师团队编校出品。",
            "",
        ),
        (
            r"扫码关注英语学科网服务号，及时获取高考真题、答案、解析",
            "",
        ),
        (
            r"登录组卷网可对本试卷进行单题组卷、细目表分析、布置作业、举一反三等操作。",
            "",
        ),
        (
            r"登录组卷网可对本试卷进行\textbf{单题组卷}、\textbf{细目表分析}、\textbf{布置作业}、\textbf{举一反三}等操作。",
            "",
        ),
        (r"试卷地址：", ""),
        (
            r"组卷网是学科网旗下的在线题库平台，覆盖小初高全学段全学科、超过900万精品解析试题。",
            "",
        ),
        (
            r"组卷网（http://zujuan.xkw.com）是学科网旗下智能题库，拥有小初高全学科超千万精品试题。",
            "",
        ),
        (
            r"关注组卷网服务号，可使用移动教学助手功能（布置作业、线上考试、加入错题本、错题训练）。",
            "",
        ),
        (
            r"学科网长期征集全国最新统考试卷、名校试卷、原创题，赢取丰厚稿酬，欢迎合作。",
            "",
        ),
        (r"钱老师 QQ：537008204    曹老师 QQ：713000635", ""),
        (r"关注组卷网服务号，可使用移动教学助手功能", ""),
        (r"本试卷的题干、答案和解析均由组卷网", ""),
        (r"（http://zujuan.xkw.com）专业教师团队编校出品。", ""),
        (r"（布置作业、线上考试、加入错题本、错题训练）。", ""),
        (r"微信关注组卷网，了解更多组卷技能", ""),
        (r"用于组卷的学考题", ""),
        (r"学科*网", ""),
        (r"：高考资源网(www.ks5u.com)", ""),
        (r"高考资源网(www.ks5u.com)", ""),
        (r"高考资源网", ""),
        (r"%www.ks5u.com", ""),
        (r"www.ks5u.com", ""),
        (r"${\bigstar}$", "★"),
        // (r"绝密★启用前", ""),
    ]);

    let option_in_latex: Regex = Regex::new(
        r"\$([\s\S]*A[.．、][\s\S]+B[.．、][\s\S]+C[.．、][\s\S]+D[.．、][\s\S]+)\$",
    )
    .unwrap();

    let fix_layout = |s: String| -> String {
        if let Some(m) = office_layout_p.captures(&s) {
            let position = m.get(1).unwrap().as_str();
            if position_map.contains_key(position) {
                format!(
                    "<p align='{}'>{}</p>",
                    position_map[position],
                    m.get(2).unwrap().as_str()
                )
            } else {
                s
            }
        } else {
            s
        }
    };

    let remove_redundant_left = |s: String| -> String {
        let left_count = s.matches("\\left").count();
        let right_count = s.matches("\\right").count();
        if left_count > right_count {
            let splits: Vec<&str> = s.split("\\right").collect();
            if splits.len() > 1 {
                let mut result = splits[..splits.len() - 1].join("\\right");
                result.push_str(&splits.last().unwrap().replace("\\left", ""));
                result
            } else {
                s
            }
        } else {
            s
        }
    };

    let mut temp = Vec::new();
    for line in lines {
        let mut ret = line
            .trim_matches('\n')
            .trim_matches('\t')
            .trim_matches(' ')
            .trim_matches('~')
            .to_string();
        ret = replace_charset(ret);
        ret = sub_latex(ret, &ding_p, |s| {
            ding_p
                .replace_all(&s, |caps: &regex::Captures| {
                    let num: u32 = caps.get(1).unwrap().as_str().parse().unwrap();
                    let new_char = std::char::from_u32('①' as u32 + num - 172).unwrap();
                    new_char.to_string()
                })
                .to_string()
        });
        ret = sub_latex(ret, &poem_space_p, |s: String| {
            poem_space_p
                .replace_all(&s, |caps: &regex::Captures| {
                    let inner_text = long_space_p.replace(
                        caps.get(1).unwrap().as_str(),
                        |inner_caps: &regex::Captures| {
                            format!(
                                "{}_{}",
                                "_".repeat(
                                    4 * (inner_caps.get(0).unwrap().len() as f32 / 8.0 + 0.5)
                                        as usize
                                ),
                                "_"
                            )
                        },
                    );
                    format!("“{}”", inner_text)
                })
                .to_string()
        });
        ret = sub_latex(ret, &short_left_right_p, |s: String| {
            short_left_right_p
                .replace_all(&s, |caps: &regex::Captures| {
                    let mut result = String::new();
                    if let Some(g1) = caps.get(1) {
                        result.push_str(g1.as_str());
                    }
                    if let Some(g2) = caps.get(2) {
                        result.push_str(g2.as_str());
                    }
                    if let Some(g3) = caps.get(3) {
                        result.push_str(g3.as_str());
                    }
                    result
                })
                .to_string()
        });
        ret = remove_redundant_left(ret);
        ret = sub_latex(ret, &begin_end_minipage_p, |s: String| {
            begin_end_minipage_p.replace_all(&s, group1).to_string()
        });
        ret = sub_latex(ret, &left_right_point_p, |s: String| {
            left_right_point_p.replace_all(&s, group1).to_string()
        });
        ret = sub_latex(ret, &begin_end_description_p, |s: String| {
            begin_end_description_p.replace_all(&s, group1).to_string()
        });
        ret = sub_latex(ret, &includegraphics_p, |s: String| {
            format!(
                "<img {}/>",
                includegraphics_greedy_p.replace_all(&s, |caps: &regex::Captures| {
                    let src = caps
                        .get(2)
                        .map_or_else(|| "".to_string(), |m| format!("src=\"{}\"", m.as_str()));
                    let style = caps
                        .get(1)
                        .map_or_else(|| "".to_string(), |m| format!("style=\"{}\"", m.as_str()));
                    format!("{} {}", src, style)
                })
            )
        });
        ret = fix_layout(ret);
        for rep in &group1_replacement {
            ret = sub_latex(ret, &rep, |s: String| {
                to_greedy(&rep).replace_all(&s, group1).to_string()
            });
        }
        if ret.contains("selectlanguage") {
            ret = Regex::new(r"\\selectlanguage\{.*?}%").unwrap().replace_all(&ret, "").to_string();
        }
        for rep in &remove_replacement {
            ret = sub_latex(ret, &rep, |_| String::new());
        }
        for rep in &underline_replacement {
            ret = sub_latex(ret, &rep, |s: String| {
                to_greedy(&rep).replace_all(&s, fix_underline).to_string()
            });
        }
        ret = sub_latex(ret, &colorbox_p, |s: String| {
            colorbox_greedy_p.replace_all(&s, group1).to_string()
        });
        ret = sub_latex(ret, &textcolor_p, |s: String| {
            textcolor_greedy_p.replace_all(&s, group1).to_string()
        });
        ret = sub_latex(ret, &raisebox_p, |s: String| {
            raisebox_greedy_p.replace_all(&s, group1).to_string()
        });
        ret = sub_latex(ret.clone(), &arraybackslash_p, |s| {
            to_greedy(&arraybackslash_p)
                .replace(&s, |m: &regex::Captures| {
                    m.get(2).unwrap().as_str().to_string()
                })
                .to_string()
        });
        ret = sub_latex(ret.clone(), &arraybackslash_p2, |s| {
            arraybackslash_p2.replace_all(&s, "").to_string()
        })
        .to_string();
        ret = sub_latex(ret.clone(), &textemdash_p, |s| {
            to_greedy(&textemdash_p)
                .replace(&s, |m: &regex::Captures| {
                    format!("-{}", m.get(1).unwrap().as_str())
                })
                .to_string()
        });
        ret = sub_latex(ret.clone(), &foreignlanguage_p, |s| {
            to_greedy(&foreignlanguage_p)
                .replace(&s, |m: &regex::Captures| {
                    m.get(2).unwrap().as_str().to_string()
                })
                .to_string()
        });
        // ret = sub_latex(ret, &single_dollar_include_p, |s| {
        //     s.replace("\\#", "\\\\#").replace("\\%", "\\\\%")
        // });
        for (key, value) in &keyword_replacement {
            ret = ret.replace(key, value);
        }
        ret = sub_latex(ret.clone(), &align_in_latex_p, |s| {
            align_in_latex_p
                .replace_all(&s, |caps: &regex::Captures| {
                    let mut replacement = caps.get(1).unwrap().as_str().to_string();
                    let inner = caps.get(2).unwrap().as_str();
                    if latex_p.is_match(inner) {
                        replacement.push_str(&format!("${}$", inner));
                    } else {
                        replacement.push_str(inner);
                    }
                    replacement.push_str(caps.get(3).unwrap().as_str());
                    replacement
                })
                .to_string()
        });
        ret = sub_latex(ret.clone(), &error_dollar_p, |s| {
            to_greedy(&error_dollar_p)
                .replace_all(&s, |caps: &regex::Captures| {
                    format!("${{{}}}$", caps.get(1).unwrap().as_str())
                })
                .to_string()
        });
        ret = sub_latex(ret.clone(), &error_double_quotation_p, |s| {
            to_greedy(&error_double_quotation_p)
                .replace_all(&s, |caps: &regex::Captures| {
                    format!("“{}”", caps.get(1).unwrap().as_str())
                })
                .to_string()
        });
        ret = sub_latex(ret.clone(), &error_option_dot_p, |s| {
            error_option_dot_p
                .replace_all(&s, |caps: &regex::Captures| {
                    format!(
                        "{}. {}",
                        caps.get(1).unwrap().as_str(),
                        caps.get(2).unwrap().as_str()
                    )
                })
                .to_string()
        });
        ret = sub_latex(ret, &caption_before_question_number_p, |s| {
            caption_before_question_number_p
                .replace_all(&s, |caps: &regex::Captures| {
                    format!(
                        "{}. {}{}",
                        caps.get(2).unwrap().as_str(),
                        caps.get(1).unwrap().as_str(),
                        caps.get(3).unwrap().as_str()
                    )
                })
                .to_string()
        });
        ret = sub_latex(ret.clone(), &underline_p4, |s| {
            underline_p4
                .replace_all(&s, |caps: &regex::Captures| {
                    caps.get(1)
                        .unwrap()
                        .as_str()
                        .replace("\\_", "_")
                        .to_string()
                })
                .to_string()
        });
        ret = sub_latex(ret.clone(), &space_in_brackets_p, |s| {
            space_in_brackets_p.replace_all(&s, "    ").to_string()
        });
        ret = sub_latex(ret.clone(), &tensor_latex_p, |s| {
            tensor_latex_p
                .replace_all(&s, |caps: &regex::Captures| {
                    format!("{{{}}}", caps.get(1).unwrap().as_str())
                })
                .to_string()
        });
        ret = sub_latex(ret, &item_p, |s| {
            item_p
                .replace_all(&s, |caps: &regex::Captures| {
                    format!("\n{}", caps.get(1).unwrap().as_str())
                })
                .to_string()
        });
        ret = option_in_latex.replace_all(&ret, group1).to_string();
        for spl in ret.split('\n') {
            temp.push(fix_latex(spl.to_string()));
        }
    }
    temp
}

#[allow(dead_code)]
pub(crate) fn read_tex_file(file_path: &Path) -> Result<String, Box<dyn std::error::Error>> {
    let mut contents = String::new();
    let file = File::open(file_path)?;
    let mut reader = BufReader::new(file);
    reader.read_to_string(&mut contents)?;
    Ok(contents)
}

fn collect_document(content: &str) -> Vec<String> {
    let captures = document_begin_end_p.captures(content);
    if captures.is_some() {
        return captures
            .and_then(|cap| cap.get(1).map(|m| m.as_str().to_string()))
            .unwrap()
            .split('\n')
            .into_iter()
            .map(String::from)
            .collect();
    }
    let result: Vec<String> = Vec::from(
        content
            .split('\n')
            .into_iter()
            .map(String::from)
            .collect::<Vec<String>>(),
    );
    result
}

fn remove_irrelevance(lines: Vec<String>) -> Vec<String> {
    let label_p = Regex::new(r"\\label\{mark-[A-Z\d]+.}").unwrap();
    let mut result: Vec<String> = Vec::new();
    for line in &lines {
        let mut tmp = line.replace("\r", "");
        tmp = label_p.replace_all(&tmp, "").to_string();
        if tmp.starts_with("\\par}") {
            tmp = tmp.trim_start_matches("\\par}").to_string();
        }
        if tmp.starts_with("{\\centering") {
            tmp = tmp.trim_start_matches("{\\centering").to_string();
        }
        if tmp == "%" {
            continue;
        }
        tmp = examine_p.replace_all(&tmp, "").to_string();
        result.push(tmp);
    }
    result
}

fn join_p(mut ls: Vec<String>) -> Vec<String> {
    let p_start_p = Regex::new(r#"<p\s*(align=['"]\S+?['"])\s*>"#).unwrap();
    let p_end_p = Regex::new(r"</\s*p\s*>").unwrap();

    let mut temp = Vec::new();
    let mut curr = String::new();

    while let Some(n) = ls.pop() {
        let n = n.replace("</p>", "</p>\n").replace("\\newline", "\n");
        for s in n.split('\n').filter(|s| !s.is_empty()) {
            curr.push_str(s);
            if p_start_p.find_iter(&curr).count() == p_end_p.find_iter(&curr).count() {
                temp.push(curr.clone());
                curr.clear();
            }
        }
    }

    if !curr.is_empty() {
        temp.push(curr);
    }
    temp.reverse();
    temp
}

fn join_align(mut ls: Vec<String>) -> Vec<String> {
    let search_p = Regex::new(r"^\$?\\(?:fbox|begin)\{").unwrap();

    for tn in [
        "table",
        "array",
        "center",
        "description",
        "enumerate",
        "minipage",
        "cases",
        "align",
        "align*",
        "aligned",
        "equation",
        "equation*",
    ]
    .iter()
    {
        let btn = format!("\\begin{{{}}}", tn);
        let etn = format!("\\end{{{}}}", tn);
        let tnf = tn.contains("equation");
        let mut temp = Vec::new();
        let mut curr = String::new();
        for ln in ls.drain(..) {
            let append = if curr.ends_with('\\') {
                " "
            } else {
                if tnf {
                    "\n "
                } else {
                    ""
                }
            };
            curr.push_str(&format!("{}{}", append, ln));
            if curr.matches(&btn).count() == curr.matches(&etn).count() {
                temp.push(curr.clone());
                curr.clear();
            }
        }
        if !curr.is_empty() {
            temp.push(curr);
        }

        ls.clear();
        let tn = tn.replace("*", "\\*");
        let join_v = vec![r"\\begin\{", &tn, r"\}[\\s\\S]*?\\end\{", &tn, r"\}"];
        let tn_p = Regex::new(&join_v.join("")).unwrap();
        for lns in temp.into_iter().flat_map(|lns| {
            tn_p.replace_all(&lns, |caps: &regex::Captures| {
                caps.get(0)
                    .unwrap()
                    .as_str()
                    .replace("\n", r"\\")
                    .replace(r"\$", "")
                    .replace("$", "")
            })
            .split('\n')
            .filter(|s| !s.is_empty())
            .map(String::from)
            .collect::<Vec<String>>()
        }) {
            let mut ln: String = lns.clone();
            if !ln.starts_with(&btn) && ln.contains(&btn) && !search_p.is_match(ln.as_str()) {
                let si = ln.find(&btn).unwrap();
                ls.push(ln[..si].to_string());
                ln = (&ln[si..]).to_string();
            }
            if ln.starts_with(&btn) && ln.ends_with(&etn) {
                ln = format!("${}$", ln.trim());
            }
            ls.push(ln.to_string());
        }
    }
    ls
}

fn join_dollar(mut ls: Vec<String>) -> Vec<String> {
    let mut curr = String::new();
    let mut temp = Vec::new();

    for ln in ls.drain(..) {
        curr.push_str(&ln.replace("\\$", "＄"));
        let stripped_curr = image_p.replace_all(&curr, "");
        if stripped_curr.matches('$').count() % 2 == 0 {
            temp.push(curr.clone());
            curr.clear();
        }
    }
    if !curr.is_empty() {
        temp.push(curr);
    }
    temp
}

fn join_brace(mut ls: Vec<String>) -> Vec<String> {
    let mut curr = String::new();
    let mut temp = Vec::new();

    for ln in ls.drain(..) {
        curr.push_str(&ln);
        let tp = curr
            .replace("\\{", "")
            .replace("\\}", "")
            .replace("\\left{", "")
            .replace("\\right}", "");
        if tp.matches('{').count() <= tp.matches('}').count() {
            temp.push(curr.clone());
            curr.clear();
        }
    }
    if !curr.is_empty() {
        temp.push(curr);
    }
    let mut result: Vec<String> = Vec::new();
    let split_p =
        Regex::new(r"\{.*?第.*?部分.*?题.*?}([一二三四五六七八九十]+、\s*.+?题.*$)").unwrap();
    for ln in temp {
        if let Some(caps) = split_p.captures(&ln.clone()) {
            result.push(caps.get(1).map_or("", |m| m.as_str()).to_string());
        } else {
            result.push(ln);
        }
    }
    result
}

fn reline(lines: Vec<String>) -> Vec<String> {
    join_brace(join_dollar(join_align(join_p(lines))))
}

fn break_inline(lines: Vec<String>) -> Vec<String> {
    let mark_p = Regex::new(r"/--paper-mark--//--\((\d+?)\)--/").unwrap();
    let mark_header_p = Regex::new(r"/--paper-mark--//--\(header\)--/").unwrap();
    let simple_header_p = Regex::new(r"^ *[一二三四五六七八九十a-z]+[．、.].+题").unwrap();
    let question_p = Regex::new(r"^[ \t~]*([1-9]\d{0,2}) *[.．]~*(.+)").unwrap();

    // 附加标头
    let mut temp: Vec<String> = vec![];
    for line in lines {
        if simple_header_p.is_match(&line) {
            temp.push(format!("/--paper-mark--//--(header)--/{}", line));
            continue;
        } else {
            if let Some(matched) = question_p.captures(&line) {
                temp.push(format!(
                    "/--paper-mark--//--({})--/{}",
                    matched.get(1).unwrap().as_str(),
                    line
                ));
                continue;
            }
        }
        temp.push(line);
    }

    // 提取试题
    let mut total = temp.len();
    let mut current_qt = String::new();
    let mut i = 0;
    while i < total {
        let mut line = temp[i].clone();
        if let Some(_matched) = mark_header_p.captures(&line) {
            current_qt = line;
        } else if let Some(matched) = mark_p.captures(&line) {
            let qno: i32 = matched.get(1).unwrap().as_str().parse::<i32>().unwrap();
            let mut next_qno: Option<i32> = None;
            let mut j = i + 1;
            while j < total && next_qno.is_none() {
                if let Some(_m) = mark_header_p.captures(&temp[j]) {
                    if j - i > 1 {
                        break;
                    }
                }
                if let Some(m) = mark_p.captures(&temp[j]) {
                    next_qno = Some(m.get(1).unwrap().as_str().parse::<i32>().unwrap());
                }
                j += 1;
            }
            if (next_qno.is_none() || next_qno.unwrap() <= qno) && (current_qt.contains("判断") || current_qt.contains("填空")) {
                next_qno = Some(qno + 100);
            }
            if let Some(next_qno) = next_qno {
                let mut qn = next_qno;
                while qn > qno {
                    let next_p = Regex::new(&format!("{} *[.．、]~*(.+?)$", qn)).unwrap();
                    if let Some(m) = next_p.captures(&line) {
                        temp.insert(i + 1, m.get(0).unwrap().as_str().to_string());
                        temp[i] = line[..m.get(0).unwrap().start()].to_string();
                        line = temp[i].clone();
                    }
                    qn -= 1;
                }
                total = temp.len();
            }
        }
        i += 1;
    }

    // 去除标头
    for i in 0..temp.len() {
        if let Some(_matched) = mark_p.captures(&temp[i]) {
            temp[i] = mark_p.replace_all(&temp[i], "").to_string();
        }
        if let Some(_matched) = mark_header_p.captures(&temp[i]) {
            temp[i] = mark_header_p.replace_all(&temp[i], "").to_string();
        }
    }
    temp
}

fn pretreatment_compatibility(lines: Vec<String>) -> Vec<String> {  // 提取配伍选项
    let mut temp: Vec<String> = Vec::new();
    let simple_header_p = Regex::new(r"^ *[一二三四五六七八九十a-z]+[．、.].+题").unwrap();
    let question_p: Regex = Regex::new(r"^[ \t~]*([1-9]\d{0,2}) *[.．]~*(.+)").unwrap();
    let span_p: Regex = Regex::new(r"(\d+)-(\d+)").unwrap();
    let mut in_question: bool = false;
    let mut curr_qt: &str = "";
    let mut curr_options: Vec<String> = Vec::new();
    let total: usize = lines.len();
    let mut i: usize = 0;
    
    for line in &lines {
        i += 1;
        if let Some(_m) = simple_header_p.captures(line) {
            if in_question == true {
                temp.extend(curr_options.clone());
            }
            in_question = false;
            curr_qt = line;
        } else if curr_qt.contains("配伍") || curr_qt.contains("比较选择题") {
            if let Some(_m) = option_p.captures(line) {
                if in_question == true {
                    temp.extend(curr_options.clone());
                }
                in_question = false;
                curr_options.push(line.clone());
                continue;
            } else if let Some(_m) = span_p.captures(line) {
                if in_question == true {
                    temp.extend(curr_options.clone());
                }
                in_question = false;
                curr_options.clear();
                continue;
            } else if let Some(_m) = question_p.captures(line) {
                if in_question == true {
                    temp.extend(curr_options.clone());
                }
                in_question = true;
            }
        } else if i == total {
            if in_question == true {
                temp.extend(curr_options.clone());
            }
        }
        temp.push(line.clone());
    }

    temp
}

pub(crate) fn clean_content(content: &str) -> Vec<String> {
    let mut lines: Vec<String> = collect_document(content);
    lines = remove_irrelevance(lines);
    lines = pretreatment_compatibility(lines);
    lines = reline(lines);
    lines = fix_answer_table(lines);
    lines = fix_table(lines);
    lines = replace_keyword(lines);
    lines = break_inline(lines);
    lines
}

// const TAG_MARK: &str = "/--paper-mark--//--(tag)--/";
// const IMAGE_MARK: &str = "/--paper-mark--//--(image)--/";
const NONE_MARK: &str = "/--paper-mark--//--(none)--/";
const INFO_MARK: &str = "/--paper-mark--//--(info)--/";
const HEADER_MARK: &str = "/--paper-mark--//--(header)--/";
const MATERIAL_MARK: &str = "/--paper-mark--//--(material)--/";
const QUESTION_MARK: &str = "/--paper-mark--//--(question)--/";
const OPTION_MARK: &str = "/--paper-mark--//--(option)--/";
const SUB_QUESTION_MARK: &str = "/--paper-mark--//--(sub_question)--/";
const ADDITION_MARK: &str = "/--paper-mark--//--(addition)--/";
const PAPER_MARK: &str = "/--paper-mark--//--(paper)--/";
const ANSWER_PAPER_MARK: &str = "/--paper-mark--//--(answer_paper)--/";

lazy_static! {
    static ref PAPER_MARK_P: Regex = Regex::new(r"^/--paper-mark--//--\((.*?)\)--/$").unwrap();
    static ref PAPER_MARK_P2: Regex = Regex::new(r"/--paper-mark--//--\((.*?)\)--/").unwrap();
    static ref QUESTION_SPAN_MARK_P: Regex =
        Regex::new(r"\(-\((完成(\d+)-(\d+)小题)\)-\)").unwrap();
}

fn mark_content(
    lines: Vec<String>,
    _subject_name: String,
    paper_name: String,
) -> (Vec<String>, String) {
    let mut content: Vec<String> = Vec::new();
    let mut line_index: i32 = -1;
    let mut _last_qno: i32 = -1;
    let mut _last_mark: Option<String>;
    let mut last_line = String::new();
    let mut underline_qno: Vec<i32> = Vec::new();
    let mut and_answer = false;
    let with_answer = false;
    let mut _que_under_addi = true;
    let mut had_question_type = false;
    let mut info_count: i32 = 0;
    let mut _had_question = false;
    let mut had_addition = false;
    let mut had_answer_paper = false;
    let mut _qno_maybe_reset = false;
    let strip_chars: &[_] = &['\n', '\t', ' ', '~', ' ', '\t', '\n'];

    for line in lines.iter() {
        line_index += 1;
        let temp = PAPER_MARK_P.replace_all(&line.trim(), "").to_string();
        if !temp.is_empty() {
            if PAPER_MARK_P.is_match(&last_line)
                || line.is_empty()
                || (latex_begin_end_p.is_match(line) && line.contains("评卷人"))
                || ignore_p.is_match(line)
            {
                continue;
            }
            // _last_qno = -1;
            let mut val = line.clone();
            let mut vv: String;
            let (mut mark, mut match_) = ("none".into(), None::<regex::Match>);
            for (mark_type, regs) in pattern.iter() {
                let mut hit_mark_type: &str = *mark_type;
                if !had_question_type && *mark_type == "question" {
                    continue;
                } else if *mark_type == "info" && (had_question_type || line_index > 10) {
                    continue;
                } else if *mark_type == "header" && (info_count < 2 && line_index < 15 && had_answer_paper == false) {
                    continue;
                }
                for reg in regs {
                    match_ = reg.find(&val);
                    if match_.is_some() && *mark_type == "tag" {
                        vv = val.replace(match_.unwrap().as_str(), "");
                        for (mt, rs) in pattern.iter() {
                            if *mt != "tag" {
                                for r in rs {
                                    let match_t = r.find(&vv);
                                    if match_t.is_some() {
                                        match_ = Some(match_t.unwrap());
                                        hit_mark_type = *mt;
                                        break;
                                    }
                                }
                                if *mark_type != "tag" {
                                    break;
                                }
                            }
                        }
                    }
                    if *mark_type != "question" && match_.is_none() {
                        vv = val.chars().filter(|c| c.is_ascii()).collect::<String>();
                        match_ = reg.find(&vv);
                    }
                    if match_.is_none() {
                        vv = align_half_p.replace_all(&val, "").to_string();
                        match_ = reg.find(&vv);
                    }
                    if let Some(m) = &match_ {
                        if *mark_type == "paper" {
                            if val.len() > 50 {
                                match_ = None;
                            } else {
                                if let Some(m) = pure_number_p.captures(m.as_str()) {
                                    let year: i32 = m.get(0).unwrap().as_str().parse().unwrap();
                                    if (100 < year && year < 2000) || (25 < year && year < 99) {
                                        match_ = None;
                                    }
                                }
                            }
                        }
                    }
                    if match_.is_some() {
                        break;
                    }
                }
                if match_.is_some() {
                    mark = hit_mark_type.into();
                    break;
                } else if *mark_type == "question" {
                    let current_qno_pattern: String = format!("^{}\\.", _last_qno.clone() + 1);
                    let current_question_p: Regex = Regex::new(if _last_qno >= 0 {
                        current_qno_pattern.as_str()
                    } else {
                        "^\\d+\\."
                    })
                    .unwrap();
                    match_ = current_question_p.find(&val);
                    if match_.is_some() {
                        mark = (*mark_type).to_string();
                    }
                }
            }

            if mark == "header" && !had_question_type {
                had_question_type = true;
            }
            if mark == "info" {
                info_count += 1;
            }

            if mark == "question" {
                if addition_p4.is_match(&val) {
                    mark = "addition".to_string();
                } else if other_header_p1.is_match(&val) {
                    mark = "header".to_string();
                }
            }

            if !had_answer_paper && mark == "answer_paper" {
                had_answer_paper = true;
            }
            if !and_answer && val.contains(&paper_name) && paper_name.chars().count() > 8 {
                and_answer = true;
            }

            if mark == "question" {
                _had_question = true;
            } else if ["paper", "answer_paper", "addition", "header", "material"]
                .contains(&mark.as_str())
            {
                _had_question = false;
                had_addition = mark == "addition";
                underline_qno.clear();
            }

            if had_answer_paper && mark == "material" {
                vv = val.clone();
                for mat in mult_line_answer_p.find_iter(&vv) {
                    let q1: isize = mat
                        .as_str()
                        .split_whitespace()
                        .nth(1)
                        .unwrap()
                        .parse()
                        .unwrap();
                    let q2: isize = mat
                        .as_str()
                        .split_whitespace()
                        .nth(2)
                        .unwrap()
                        .parse()
                        .unwrap();
                    let answers = mat
                        .as_str()
                        .split_whitespace()
                        .last()
                        .unwrap()
                        .trim_matches(strip_chars);
                    let answers_vec: Vec<String> = answers
                        .split_whitespace()
                        .filter(|s| !s.is_empty())
                        .map(String::from)
                        .collect();
                    if answers_vec.len() as isize == q2 - q1 + 1 {
                        for i in 0..answers_vec.len() {
                            content.push(QUESTION_MARK.to_string());
                            content.push(format!("{}.", i as isize + q1));
                            content.push(ADDITION_MARK.to_string());
                            content.push(answers_vec[i].clone());
                        }
                        val = val.replace(mat.as_str(), "");
                    }
                }
            }

            if mark == "question" {
                if !had_answer_paper {
                    if let Some(match_) = question_p1.captures(&val) {
                        let qno: isize = match_.get(1).unwrap().as_str().parse().unwrap();
                        let stem = match_.get(2).unwrap().as_str();
                        if qno == 1 && option_addition_p3.is_match(stem) {
                            let total = content.len();
                            if total > 5 {
                                for idx in (total - 5..total - 1).rev() {
                                    if content[idx] == NONE_MARK
                                        && answer_paper_p2.is_match(&content[idx + 1])
                                    {
                                        content[idx] = ANSWER_PAPER_MARK.to_string();
                                        had_answer_paper = true;
                                        and_answer = true;
                                        _que_under_addi = false;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                if exercises_p.is_match(&line) {
                    mark = "none".to_string();
                    val = "".to_string();
                } else if had_answer_paper {
                    vv = val.clone();
                    for mat in line_answer_p.captures_iter(&vv) {
                        val = val.replace(&mat[0], "");
                        _last_qno = (&mat[1]).parse().unwrap();
                        content.push(QUESTION_MARK.to_string());
                        content.push(format!("{}. {}", _last_qno, &mat[2]));
                        if addition_p1.is_match(&val)
                            || addition_p2.is_match(&val)
                            || addition_p5.is_match(&val)
                        {
                            mark = "addition".to_string();
                        }
                    }
                    if let Some(mat) = addition_p3.captures(&val) {
                        content.push(QUESTION_MARK.to_string());
                        content.push(mat.get(1).unwrap().as_str().to_string());
                        mark = "addition".to_string();
                        val = val
                            .replace(mat.get(1).unwrap().as_str(), "")
                            .trim_matches(strip_chars)
                            .to_string();
                    }
                } else {
                    if question_answer_p.find_iter(&val).count() > 4 {
                        let content_size = content.len();
                        if content_size > 50 {
                            for idx in (content_size - 10..content_size - 1).rev() {
                                if content[idx] == PAPER_MARK {
                                    content[idx] = ANSWER_PAPER_MARK.to_string();
                                    let mut final_val = val.clone();
                                    for m in question_answer_p.find_iter(&val) {
                                        _last_qno = m
                                            .as_str()
                                            .split_whitespace()
                                            .next()
                                            .unwrap()
                                            .parse()
                                            .unwrap();
                                        content.push(QUESTION_MARK.to_string());
                                        content.push(format!("{}. ", _last_qno));
                                        content.push(ADDITION_MARK.to_string());
                                        content.push(format!("【解析】{}", m.as_str()));
                                        final_val = final_val.replace(m.as_str(), "");
                                    }
                                    mark = "none".to_string();
                                    val = final_val;
                                    break;
                                }
                            }
                        }
                    } else {
                        vv = val.clone();
                        if let Some(match_) = question_p1.captures(&vv) {
                            let stem = match_.get(2).unwrap().as_str();
                            let addition_m = option_addition_p.captures(stem);
                            if addition_m.is_none() {
                                let addition_m = option_addition_p2.captures(stem);
                                if addition_m.is_some() {
                                    content.push(QUESTION_MARK.to_string());
                                    content.push(format!("{}. ", match_.get(1).unwrap().as_str()));
                                    mark = "addition".to_string();
                                    val = format!("【解答】{}", stem);
                                    let analyse = stem.replace(
                                        addition_m.as_ref().unwrap().get(1).unwrap().as_str(),
                                        "",
                                    );
                                    if analyse.trim_matches(strip_chars).len() > 1 {
                                        content.push(ADDITION_MARK.to_string());
                                        content.push(
                                            addition_m
                                                .unwrap()
                                                .get(1)
                                                .unwrap()
                                                .as_str()
                                                .to_string(),
                                        );
                                        val = format!("【解析】{}", analyse);
                                    }
                                    and_answer = true;
                                }
                            }
                        }
                    }
                }
            }

            if mark == "question" {
                if addition_p1.is_match(&val) || addition_p2.is_match(&val) {
                    if !had_answer_paper {
                        mark = "addition".to_string();
                    }
                } else {
                    if let Some(match_) = qno_p.captures(&val) {
                        let current_qno: i32 = match match_.get(1).unwrap().as_str().parse() {
                            Ok(num) => num,
                            Err(_e) => -1
                        };
                        if current_qno > 0 {
                            if had_addition && !underline_qno.is_empty() {
                                if underline_qno.contains(&current_qno) {
                                    underline_qno.retain(|&x| x != current_qno);
                                    mark = if !underline_qno.is_empty() {
                                        "none".to_string()
                                    } else {
                                        "addition".to_string()
                                    };
                                    had_addition = !underline_qno.is_empty();
                                }
                            }
                            if mark != "none" {
                                if current_qno <= _last_qno {
                                    // if question_number_maybe_not_reset_p.is_match(&last_line) {
                                    //     mark = "none".to_string();
                                    //     _qno_maybe_reset = false;
                                    // }
                                    // if _qno_maybe_reset {
                                    //     if mark != "question" {
                                    //         mark = "none".to_string();
                                    //     }
                                    // } else if question_number_maybe_not_reset_p.is_match(&val) {
                                    //     mark = "none".to_string();
                                    //     _qno_maybe_reset = false;
                                    //     val = val.replace(
                                    //         &format!("{}", current_qno),
                                    //         &format!("{} ", current_qno),
                                    //     );
                                    // } else if current_qno != 1 {
                                    //     mark = "none".to_string();
                                    // }
                                } else {
                                    _qno_maybe_reset = false;
                                    had_addition = false;
                                    let mut sub_mark = None;
                                    let sub_val = val.replacen(match_.get(0).unwrap().as_str(), "", 1);
                                    if question_p2.is_match(&sub_val) {
                                        sub_mark = Some("sub_question".to_string());
                                    } else if addition_p1.is_match(&sub_val) {
                                        sub_mark = Some("addition".to_string());
                                    } else if option_p.is_match(&sub_val) {
                                        sub_mark = Some("option".to_string());
                                    }
                                    if let Some(sub_mark) = sub_mark {
                                        content.push(QUESTION_MARK.to_string());
                                        content.push(val.replace(&sub_val, "").to_string());
                                        mark = sub_mark;
                                        val = sub_val;
                                    } else {
                                        if let Some(_match_) = material_p1.captures(&sub_val) {
                                            if let Some(match_) = qno_span_p.captures(&sub_val) {
                                                content.push(MATERIAL_MARK.to_string());
                                                content.push(format!(
                                                    "(-(完成{}-{}小题)-)",
                                                    match_.get(1).unwrap().as_str(),
                                                    match_.get(2).unwrap().as_str()
                                                ));
                                            }
                                        } else if current_qno != _last_qno + 1 && _last_qno != -1 && current_qno == _last_qno {
                                            let mut match_times = 1;
                                            let mut next_qno_p = Regex::new(&format!(
                                                r"^\\s*{}[．.、\]][\s\S]+",
                                                _last_qno + match_times
                                            ))
                                            .unwrap();
                                            for nl in &lines[(line_index + 1) as usize..] {
                                                if next_qno_p.is_match(nl) {
                                                    match_times += 1;
                                                    next_qno_p = Regex::new(&format!(
                                                        r"^\\s*{}[．.、\]][\s\S]+",
                                                        _last_qno + match_times
                                                    ))
                                                    .unwrap();
                                                }
                                            }
                                        }
                                    }
                                    _last_qno = current_qno;
                                }
                            }    
                        }
                    }
                }
            }

            // 重置题号序列
            if ["header", "paper", "answer_paper"].contains(&mark.as_str()) {
                _last_qno = -1;
            }

            if !val.trim().is_empty() {
                content.push(format!("/--paper-mark--//--({})--/", mark));
                content.push(val);
            }

            if !and_answer && mark == "answer_paper" {
                and_answer = true;
            }

            last_line = (*line).clone();
            _last_mark = Some(mark);
        } else {
            let match_ = PAPER_MARK_P2.captures(line);
            if let Some(caps) = match_ {
                _last_mark = Some(caps.get(1).unwrap().as_str().into());
                if _last_mark.as_deref() == Some("answer_paper") {
                    had_answer_paper = true;
                    and_answer = true;
                }
            }
        }
    }

    let form = if and_answer && with_answer {
        "have_answer".into()
    } else if with_answer {
        "with_answer".into()
    } else if and_answer {
        "and_answer".into()
    } else {
        "no_answer".into()
    };

    (content, form)
}

fn format_mark(lines: Vec<String>, _subject_name: String, form: String) -> (Vec<String>, String) {
    let mut new_content: Vec<String> = Vec::new();
    let total: i32 = lines.len() as i32;
    let mut _last_mark: String = String::new();
    let mut _last_index: i32 = -1;
    let mut current_index: i32 = -1;

    for line in lines.iter() {
        let mut _line = line.clone();
        current_index += 1;
        if let Some(match_) = PAPER_MARK_P.captures(line) {
            let current_mark = match_.get(1).unwrap().as_str();
            if _last_mark == "question" || _last_mark == "addition" {
                if current_mark == "info" {
                    if current_index + 1 < total {
                        let next_line = &lines[(current_index + 1) as usize];
                        let mut flag = has_sub_question_p.is_match(next_line);
                        if !flag {
                            if question_p1.is_match(next_line) {
                                flag = true;
                            }
                        }
                        _line = format!(
                            "/--paper-mark--//--({})--/",
                            if flag { "question" } else { "none" }
                        );
                    }
                }
            }

            if _last_mark == "material" && current_mark.contains("question") {
                if current_index - _last_index < 3 {
                    let new_len = new_content.len();
                    if new_content[new_len - 2] == MATERIAL_MARK {
                        new_content[new_len - 2] = NONE_MARK.to_string();
                    }
                }
            }

            if (_last_mark == "header" && current_mark == "header")
                || (_last_mark == "material" && current_mark == "material")
            {
                println!("lm: {}, cm: {}", &_last_mark, &current_mark);
                if _last_mark == "header" && current_mark == "header" {
                    _line = NONE_MARK.to_string();
                } else {
                    let mut is_new = false;
                    for ln in new_content.iter().rev() {
                        if let Some(match_) = PAPER_MARK_P.captures(ln) {
                            if match_.get(1).unwrap().as_str() == "info" {
                                is_new = true;
                                break;
                            }
                        }
                    }
                    if !is_new && current_mark == "header" {
                        let val = &lines[(current_index + 1) as usize];
                        _line = if qno_span_p.find(val).into_iter().count() == 1
                            && !other_header_p2.is_match(val)
                            && !score_header_p.is_match(val)
                        {
                            MATERIAL_MARK.to_string()
                        } else {
                            NONE_MARK.to_string()
                        };
                    }
                }
            }

            if _last_mark == "addition"
                && (current_mark == "sub_question" || current_mark == "option")
            {
                _line = ADDITION_MARK.to_string().clone();
            }

            if [
                "paper",
                "header",
                "question",
                "addition",
                "answer_paper",
                "material",
            ]
            .contains(&current_mark)
            {
                _last_mark = current_mark.to_string();
                _last_index = current_index;
            }
        }
        new_content.push(_line.clone());
    }

    (new_content, form)
}

fn remark_question_type(content: Vec<String>) -> Vec<String> {
    let mut in_question: bool = false;
    let mut questions: Vec<String> = Vec::new();
    let mut question_types: Vec<String> = Vec::new();
    for line in &content {
        if in_question == false && (line == QUESTION_MARK || line == MATERIAL_MARK || line == SUB_QUESTION_MARK) {
            in_question = true;
        }
        if in_question {
            questions.push(line.clone());
        } else {
            question_types.push(line.clone());
        }
    }
    let mut remark: Vec<String> = Vec::new();
    let split_p: Regex = Regex::new(r"(?:[一二三四五六七八九十a-z]、|共\s*\d+\s*分|判断正误|填写|说明理由|正确的|错误的|[未错多][填涂])(.*?)$").unwrap();
    let mut maybe_material: Vec<String> = Vec::new();
    let mut i: usize = question_types.len();
    let limit: usize = 0;
    while i > limit {
        i -= 1;
        if let Some(_m) = split_p.captures(&question_types[i]) {
            break;
        }
        maybe_material.insert(0, question_types[i].clone());
    }
    let maybe_material_len: usize = maybe_material.len();
    if maybe_material_len > 0 {
        if maybe_material[0].contains("/--paper-mark--/") {
            maybe_material[0] = MATERIAL_MARK.to_string();
        } else {
            maybe_material.insert(0, MATERIAL_MARK.to_string());
        }
        if maybe_material.len() > 1 {
            let mut not_material: bool = false;
            for key in ["相关参数", "Example"] {
                if maybe_material[1].contains(key) {
                    not_material = true;
                    break;
                }
            }
            if !not_material {
                remark.extend(maybe_material);
            }
        }
    }
    if maybe_material_len > 0 && question_types.len() - maybe_material_len > 1  {
        let mut first_sub_question: bool = false;
        for line in &questions {
            if line == SUB_QUESTION_MARK {
                first_sub_question = true;
            } else if line == QUESTION_MARK && first_sub_question == false {
                first_sub_question = true;
                remark.push(SUB_QUESTION_MARK.to_string());
                continue;
            }
            remark.push(line.clone());
        }
    } else {
        remark.extend(questions);
    }
    remark
}

fn paper_chunker(content: Vec<String>) -> Vec<Vec<String>> {
    let mut chunks: Vec<Vec<String>> = Vec::new();
    let mut current_chunk: Vec<String> = Vec::new();
    let mut in_answer_paper: bool = false;

    for line in content.iter() {
        let m: &str = (*line).as_str();
        if current_chunk.len() > 10 && m.contains("答案") && m.contains("评分参考") && !in_answer_paper {
            in_answer_paper = true;
            let last = current_chunk.pop();
            chunks.push(current_chunk.drain(..).collect());
            current_chunk.clear();
            if last != None {
                current_chunk.push(last.unwrap());
            }
        }
        current_chunk.push(m.to_string());
    }
    chunks.push(current_chunk);
    chunks
}

fn question_type_chunker(content: Vec<String>) -> Vec<Vec<String>> {
    let mut chunks: Vec<Vec<String>> = Vec::new();
    let mut current_chunk: Vec<String> = Vec::new();
    let mut chunk_start = -1;

    for line in content.iter() {
        let m: &str = (*line).as_str();
        match m {
            HEADER_MARK => {
                chunk_start = 1;
            }
            PAPER_MARK | ANSWER_PAPER_MARK => {
                chunk_start = -1;
            }
            _ => {
                if chunk_start == 1 {
                    chunk_start = 0;
                }
            }
        }
        if chunk_start != 0 {
            if !current_chunk.is_empty() {
                chunks.push(current_chunk.drain(..).collect());
                current_chunk.clear();
            }
        }
        if chunk_start != -1 && chunk_start == 0 {
            current_chunk.push(m.to_string());
        }
    }
    if !current_chunk.is_empty() {
        chunks.push(current_chunk);
    }
    chunks
}

fn question_chunker(content: Vec<String>) -> Vec<Vec<String>> {
    let mut chunks: Vec<Vec<String>> = Vec::new();
    let mut current_chunk: Vec<String> = Vec::new();
    let mut chunk_start = -1;

    for line in content.iter() {
        let m: &str = (*line).as_str();
        match m {
            QUESTION_MARK | MATERIAL_MARK => {
                chunk_start = 1;
            }
            INFO_MARK | HEADER_MARK | PAPER_MARK | ANSWER_PAPER_MARK => {
                chunk_start = -1;
            }
            _ => {
                if chunk_start == 1 {
                    chunk_start = 0;
                }
            }
        }
        if chunk_start != 0 {
            if !current_chunk.is_empty() {
                chunks.push(current_chunk.drain(..).collect());
                current_chunk.clear();
            }
        }
        if chunk_start != -1 && chunk_start == 0 {
            current_chunk.push(m.to_string());
        }
    }
    if !current_chunk.is_empty() {
        chunks.push(current_chunk);
    }
    chunks
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Question {
    pub number: String,
    pub material: String,
    pub stem: String,
    pub sub_questions: Vec<Question>,
    pub answer: String,
    pub score: Option<f64>,
    pub options: Vec<String>,
    pub content: String,
}

impl Question {
    fn new(content: Vec<String>, question_type_name: Option<String>) -> Self {
        let mut q: Question = Question {
            number: String::new(),
            material: String::new(),
            stem: String::new(),
            sub_questions: Vec::new(),
            answer: String::new(),
            score: None,
            options: Vec::new(),
            content: String::new(),
        };
        let mut with_options = question_type_name.clone().map_or(
            question_type_name == None && content.contains(&(OPTION_MARK.to_string())),
            |name| has_options_p.is_match(&name),
        );
        if !with_options {
            if let Some(qtn) = question_type_name.clone() {
                if qtn != "默认题型" {
                    with_options = has_options_p.is_match(qtn.as_str());
                    if !with_options {
                        if qtn.contains("选择") && !qtn.contains("非选") {
                            with_options = true;
                        }
                    }
                } else {
                    for line in &content {
                        if *line == OPTION_MARK.to_string() {
                            with_options = true;
                            break;
                        }
                    }
                }
            }
        }
        let with_material = question_type_name.map_or(false, |name| name.contains("材料"));

        let mut que_dict: HashMap<String, Vec<String>> = HashMap::new();
        let mut field = "stem".to_string();
        let mut cur_field = "stem".to_string();
        let mut had_addition = false;

        for line in &content {
            let match_ = PAPER_MARK_P.captures(line);
            if let Some(caps) = match_ {
                let new_field = caps.get(1).map_or("", |m| m.as_str());
                field = if new_field == "question"
                    || new_field == "sub_question"
                    || (new_field == "option" && !with_options)
                {
                    "stem".to_string()
                } else if with_material && new_field == "material" {
                    "stem".to_string()
                } else if new_field == "option" && had_addition {
                    "none".to_string()
                } else {
                    new_field.to_string()
                };
                if field == "addition" {
                    had_addition = true;
                }
            } else {
                if !had_addition {
                    q.content = if q.content.len() > 0 {
                        format!("{}\n{}", q.content, line.to_string())
                    } else {
                        line.to_string()
                    }
                }
                if ["none", "image", "tag", "header", "paper", "answer_paper"]
                    .contains(&(field.clone().as_str()))
                {
                    field = cur_field.clone();
                }
                if que_dict.contains_key(&field) {
                    que_dict
                        .entry(field.clone())
                        .or_insert_with(Vec::new)
                        .push(line.to_string());
                } else {
                    que_dict
                        .entry(field.clone())
                        .or_insert_with(Vec::new)
                        .push(line.to_string());
                    cur_field = field.clone();
                }
            }
        }

        for (field, lines) in que_dict {
            match field.as_str() {
                "stem" | "sub_question" => q.set_stem(lines),
                "option" => q.set_options(lines),
                "addition" => q.set_addition(lines),
                "material" => q.set_material(lines),
                _ => (),
            }
        }
        q
    }

    fn set_stem(&mut self, stem: Vec<String>) {
        let content = stem.join("\n");
        let question_p = Regex::new(r"^[\s\t~]*([1-9１２３４５６７８９][\d１２３４５６７８９]{0,2})\s*[.．、]~*([\s\S]*)").unwrap();
        if let Some(match_) = question_p.captures(&content) {
            self.number = match_.get(1).unwrap().as_str().to_string();
            self.stem = match_.get(2).unwrap().as_str().to_string();
        } else {
            let mut new_stem: Vec<String> = Vec::new();
            for line in &stem {
                if self.number.len() == 0 {
                    if let Some(m) = question_p.captures(line) {
                        self.number = m.get(1).unwrap().as_str().to_string();
                        new_stem.push(m.get(2).unwrap().as_str().to_string());
                        continue;
                    }
                }
                new_stem.push(line.clone());
            }
            self.stem = new_stem.join("\n");
        }
        self.stem = self.stem.trim_end_matches('\n').to_string();
    }

    fn set_options(&mut self, options: Vec<String>) {
        let mut max_options_len = 7;
        let pre_len = options.len();
        let mut op = options.join("\n");
        let mut match_found = false;

        while max_options_len > 0 {
            let options_len = max_options_len;
            max_options_len -= 1;
            let mut pattern_fragment = String::new();

            for _delta in 0..options_len {
                pattern_fragment.push_str(&format!(r#"\[?[A-Z][．\\.、\]]\s*([\s\S]+?)"#));
            }

            let p = format!("{}$", pattern_fragment);
            let regex = Regex::new(&p).unwrap();

            for i in (0..pre_len).rev() {
                let mop = options[i..].iter().cloned().collect::<Vec<_>>().join("\n");
                if let Some(matched) = regex.captures(&mop) {
                    match_found = true;
                    for group in matched.iter().skip(1) {
                        let option = group.unwrap().as_str().trim_end_matches('[').trim();
                        self.options.push(option.to_string());
                    }
                    op = op.replace(&mop, "");
                    break;
                }
            }

            if match_found == true {
                break;
            }
        }
    }

    fn set_addition(&mut self, addition: Vec<String>) {
        self.answer = addition.join("\n").trim_end_matches('\n').to_string();
    }

    fn set_material(&mut self, material: Vec<String>) {
        self.material = material.join("\n").trim_end_matches('\n').to_string();
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct QuestionType {
    pub text: String,
    pub code: Option<u64>,
    pub name: Option<String>,
    pub questions: Vec<Question>,
}

impl QuestionType {
    fn new(content: Vec<String>, question_type_list: &[(u64, String)]) -> Self {
        let mut code = None;
        let mut name = None;
        let questions = Vec::new(); // 初始化questions为空向量

        let mut header = String::new();

        for line in content {
            if ![
                QUESTION_MARK,
                SUB_QUESTION_MARK,
                PAPER_MARK,
                ANSWER_PAPER_MARK,
            ]
            .contains(&line.as_str())
            {
                if PAPER_MARK_P.is_match(&line) {
                    continue;
                }
                header.push_str(&line);
                header.push('\n');
            } else {
                break;
            }
        }

        let text: String = header.clone();
        // let p = Regex::new(r"[^(（]+[(（:：]([^）)]*\d+分[^）)]*)").unwrap();
        // if let Some(caps) = p.captures(&header) {
        //     if let Some(m) = caps.get(1) {
        //         text = m.as_str().to_string();
        //         header = header.replace(&text, "");
        //     }
        // }

        for &(ref code_item, ref name_item) in question_type_list {
            if header.contains(name_item) {
                code = Some(code_item.clone());
                name = Some(name_item.clone());
                break;
            }
        }

        if name == None {
            name = Some("默认题型".to_string());
        }

        QuestionType {
            text,
            code,
            name,
            questions,
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Paper {
    pub question_types: Vec<QuestionType>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaperWithAnswer {
    pub paper_opt: Option<Paper>,
    pub answer_paper_opt: Option<Paper>,
}

impl PaperWithAnswer {
    fn new(content: Vec<String>, question_type_list: Vec<(u64, String)>) -> Self {

        // for line in &content {
        //     println!("md: {}", line);
        // }

        let mut paper_index = 0;
        let mut question_types = Vec::new();
        let papers = paper_chunker(content.clone());
        let mut paper:Paper = Paper { question_types: Vec::new() };
        for paper_content in papers.clone() {
            let mut question_count = 0;
            paper_index += 1;
            for mut qt_content in question_type_chunker(paper_content) {
                let mut question_type = QuestionType::new(qt_content.clone(), &question_type_list);
                qt_content = remark_question_type(qt_content);
                for q_content in question_chunker(qt_content) {
                    let question = Question::new(q_content, question_type.name.clone());
                    question_type.questions.push(question);
                    question_count += 1;
                }
                question_types.push(question_type);
            }
            if question_count > 0 {
                paper = Paper { question_types };
                break;
            }
        }
        let mut answer_paper: Paper = Paper {
            question_types: Vec::new(),
        };
        let paper_count = papers.len() - paper_index + 1;
        if paper_count > 1 && paper_index < papers.len() {
            let mut answer_content: Vec<String> = Vec::new();
            for line in papers.get(paper_index).unwrap().clone() {
                if line == ANSWER_PAPER_MARK {
                    answer_content.push(ADDITION_MARK.to_string());
                } else {
                    answer_content.push(line);
                }
            }
            let mut answer_question_types = Vec::new();
            for mut qt_content in question_type_chunker(answer_content) {
                let mut question_type = QuestionType::new(qt_content.clone(), &question_type_list);
                qt_content = remark_question_type(qt_content);
                for q_content in question_chunker(qt_content) {
                    let question = Question::new(q_content, question_type.name.clone());
                    question_type.questions.push(question);
                }
                answer_question_types.push(question_type);
            }
            answer_paper = Paper {
                question_types: answer_question_types,
            };
            answer_paper = trans_to_answer(&mut answer_paper);
            paper = merge_paper(&mut paper, &mut answer_paper);
        } else if paper_count == 1 {
            for line in papers[paper_index - 1].clone().into_iter() {
                if line.contains("答案") && line.contains("评分") {
                    answer_paper = trans_to_answer(&mut paper);
                    paper = Paper {
                        question_types: Vec::new(),
                    };
                    break;
                }
            }
        }
        PaperWithAnswer {
            paper_opt: if paper.question_types.len() > 0 {
                Some(paper)
            } else {
                None
            },
            answer_paper_opt: if answer_paper.question_types.len() > 0 {
                Some(answer_paper)
            } else {
                None
            },
        }
    }
}

pub fn parser2(
    contents: &mut str,
    name: &str,
    subject_name: &str,
    _exam: &str,
    question_type_dict: Vec<(u64, String)>,
) -> PaperWithAnswer {
    let mut _form: String = String::new(); // 试卷格式类型
    let mut content = clean_content(contents);
    (content, _form) = mark_content(content, subject_name.to_string(), name.to_string());
    (content, _form) = format_mark(content, subject_name.to_string(), _form);
    PaperWithAnswer::new(content, question_type_dict)
}

pub fn merge_paper(paper: &mut Paper, answer_paper: &mut Paper) -> Paper {
    let mut question_types = Vec::new();
    for (_index, to_question_type) in paper.question_types.iter().enumerate() {
        let mut new_qt: QuestionType = QuestionType {
            text: to_question_type.text.clone(),
            code: to_question_type.code.clone(),
            name: to_question_type.name.clone(),
            questions: Vec::new(),
        };
        for q in &(to_question_type.questions) {
            let mut new_q: Question = Question {
                number: q.number.clone(),
                material: q.material.clone(),
                stem: q.stem.clone(),
                sub_questions: Vec::new(),
                answer: q.answer.clone(),
                score: q.score.clone(),
                options: q.options.clone(),
                content: q.content.clone(),
            };
            if to_question_type.code != None {
                for (_a_index, answer_qt) in answer_paper.question_types.iter().enumerate() {
                    if answer_qt.code != None
                        && to_question_type.code.unwrap() == answer_qt.code.unwrap()
                    {
                        for answer_q in &(answer_qt.questions) {
                            if q.number == answer_q.number {
                                new_q.answer = String::from(format!(
                                    "{}{}{}",
                                    new_q.answer, answer_q.stem, answer_q.answer
                                ))
                                .trim()
                                .to_string();
                                break;
                            }
                        }
                        break;
                    }
                }
            }
            new_qt.questions.push(new_q);
        }
        question_types.push(new_qt);
    }
    Paper { question_types }
}

pub fn trans_to_answer(paper: &mut Paper) -> Paper {
    let mut question_types = Vec::new();
    for (_index, to_question_type) in paper.question_types.iter().enumerate() {
        let mut new_qt: QuestionType = QuestionType {
            text: to_question_type.text.clone(),
            code: to_question_type.code.clone(),
            name: to_question_type.name.clone(),
            questions: Vec::new(),
        };
        for q in &(to_question_type.questions) {
            let mut new_q: Question = Question {
                number: q.number.clone(),
                material: String::from(""),
                stem: String::from(""),
                sub_questions: Vec::new(),
                answer: [q.material.clone(), q.stem.clone(), q.answer.clone()]
                    .join("\n")
                    .trim_start_matches('\n')
                    .trim_end_matches('\n')
                    .to_string(),
                score: q.score.clone(),
                options: q.options.clone(),
                content: q.content.clone(),
            };
            for sq in &q.sub_questions {
                let new_sq: Question = Question {
                    number: sq.number.clone(),
                    material: String::from(""),
                    stem: String::from(""),
                    sub_questions: Vec::new(),
                    answer: [sq.material.clone(), sq.stem.clone(), sq.answer.clone()]
                        .join("\n")
                        .trim_start_matches('\n')
                        .trim_end_matches('\n')
                        .to_string(),
                    score: sq.score.clone(),
                    options: sq.options.clone(),
                    content: sq.content.clone(),
                };
                new_q.sub_questions.push(new_sq);
            }
            new_qt.questions.push(new_q);
        }
        question_types.push(new_qt);
    }
    Paper { question_types }
}

#[test]
fn test01() -> Result<(), Box<dyn std::error::Error>> {
    use crate::parser::fang_zheng::parse_fbd;
    let mut contents = parse_fbd(Path::new(r"D:\temp\0014303.fbd"))?;
    // let mut contents = read_tex_file(Path::new(r"D:\temp\00061国家税收试卷1.tex"))?;
    let mut question_type_dict: Vec<(u64, String)> = Vec::new();
    question_type_dict.push((10, String::from("判断说明理由题")));
    question_type_dict.push((11, String::from("单项选择题")));
    question_type_dict.push((12, String::from("多项选择题")));
    question_type_dict.push((13, String::from("判断选择题")));
    question_type_dict.push((12, String::from("比较选择题")));
    question_type_dict.push((13, String::from("材料选择题")));
    question_type_dict.push((13, String::from("判断改错题")));
    question_type_dict.push((13, String::from("名词解释题")));
    question_type_dict.push((13, String::from("业务处理题")));
    question_type_dict.push((14, String::from("简答题")));
    question_type_dict.push((15, String::from("论述题")));
    question_type_dict.push((16, String::from("计算题")));
    question_type_dict.push((17, String::from("填空题")));
    question_type_dict.push((18, String::from("解答题")));
    question_type_dict.push((19, String::from("选择题")));
    question_type_dict.push((20, String::from("综合题")));
    question_type_dict.push((21, String::from("判断题")));
    question_type_dict.push((21, String::from("填表题")));
    let name = "";
    let subject = "";
    let exam = "";
    let paper = parser2(&mut contents, name, subject, exam, question_type_dict);
    println!("{:#?}", paper);
    Ok(())
}

// #[test]
// fn test() -> Result<(), Box<dyn std::error::Error>> {
//     let contents = read_tex_file(Path::new(r"D:\temp\docx\table.tex"))?;
//     let mut lines: Vec<String> = Vec::new();
//     for ct in contents.clone().split("\n") {
//         lines.push(ct.to_string());
//     }
//     for line in fix_table(lines) {
//     }
//     Ok(())
// }

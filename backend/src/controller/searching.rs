use actix_web::{HttpResponse, web};
use crate::search::PredictCardOrBookParams;

pub async fn ctrl_global_search_cards(
    params: web::Json<PredictCardOrBookParams>,
) -> HttpResponse {
    match crate::commands::searching::global_search_cards(params.0).await {
        Ok(result) => HttpResponse::Ok().json(result),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_global_search_books(
    params: web::Json<PredictCardOrBookParams>,
) -> HttpResponse {
    match crate::commands::searching::global_search_books(params.0).await {
        Ok(result) => HttpResponse::Ok().json(result),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}
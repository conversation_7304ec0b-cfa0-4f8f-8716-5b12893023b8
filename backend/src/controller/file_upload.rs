use std::fs;
use std::io::Write;
use actix_web::{web, HttpResponse};
use serde_derive::{Deserialize, Serialize};
use crate::utils::path::get_upload_dir;
use crate::models::ResponseVO;

#[derive(Deserialize)]
pub(crate) struct FileChunkParam {
    pub(crate) identifier: String,
    #[serde(rename = "chunkIndex")]
    pub(crate) chunk_index: usize,
}

#[derive(Deserialize)]
pub(crate) struct ChunksMergingParam {
    pub(crate) identifier: String,
    #[serde(rename = "fileName")]
    pub(crate) file_name: String,
    #[serde(rename = "numChunks")]
    pub(crate) num_chunks: usize,
}

#[derive(Serialize, Deserialize)]
pub(crate) struct FileMergingResult {
    #[serde(rename = "remoteFilePath")]
    pub(crate) remote_file_path: String,
}

pub(crate) async fn ctrl_upload_file_chunk(
    p: web::Query<FileChunkParam>,
    payload: web::Payload,
) -> HttpResponse {
    let result = payload.to_bytes().await;
    if let Err(e) = result {
        return HttpResponse::BadRequest().body(format!("{:?}", e));
    }
    let bytes = result.unwrap();
    let bytes = bytes.to_vec();

    let result = get_upload_dir();
    if let Err(e) = result {
        return HttpResponse::InternalServerError().body(format!("{:?}", e));
    }
    let upload_dir = result.unwrap();

    let saved_dir = upload_dir.join("chunks").join(&p.identifier);
    if !saved_dir.exists() {
        let result = fs::create_dir_all(&saved_dir);
        if let Err(e) = result {
            return HttpResponse::InternalServerError().body(format!("{:?}", e));
        }
    }

    let chunk_path = saved_dir.join(format!("{}_{}.chunks", p.identifier, p.chunk_index));
    let result = fs::write(chunk_path, bytes.as_slice());
    if let Err(e) = result {
        return HttpResponse::InternalServerError().body(format!("{:?}", e));
    }

    HttpResponse::Ok().finish()
}

pub(crate) async fn ctrl_merge_file_chunks(
    p: web::Query<ChunksMergingParam>
) -> HttpResponse {
    let result = get_upload_dir();
    if let Err(e) = result {
        return HttpResponse::InternalServerError().body(format!("{:?}", e));
    }
    let upload_dir = result.unwrap();

    let saved_dir = upload_dir.join("chunks").join(&p.identifier);
    if !saved_dir.exists() {
        return HttpResponse::InternalServerError().body("分片文件夹不存在");
    }

    let file_path = saved_dir.join(&p.file_name);
    if file_path.exists() {
        let result = fs::remove_file(&file_path);
        if let Err(e) = result {
            return HttpResponse::InternalServerError().body(format!("{:?}", e));
        }
    }

    let result = fs::File::create(&file_path);
    if let Err(e) = result {
        return HttpResponse::InternalServerError().body(format!("{:?}", e));
    }
    let mut file = result.unwrap();

    for chunk_index in 0..p.num_chunks {
        let chunk_path = saved_dir.join(format!("{}_{}.chunks", p.identifier, chunk_index));
        let result = fs::read(&chunk_path);
        if let Err(e) = result {
            return HttpResponse::InternalServerError().body(format!("{:?}", e));
        }
        let chunk_data = result.unwrap();
        let result = file.write(chunk_data.as_slice());
        if let Err(e) = result {
            return HttpResponse::InternalServerError().body(format!("{:?}", e));
        }

        let _ = fs::remove_file(&chunk_path);
    }

    let rst = FileMergingResult {
        remote_file_path: file_path.display().to_string(),
    };

    HttpResponse::Ok().json(rst)
}

pub async fn ctrl_get_snapshots() -> HttpResponse {
    // Return empty snapshots for now
    let snapshots: Vec<String> = vec![];
    HttpResponse::Ok().json(ResponseVO::success(Some(snapshots), None))
}

#[derive(Deserialize)]
pub struct SearchResourcesParam {
    pub query: String,
}

pub async fn ctrl_search_resources(params: web::Json<SearchResourcesParam>) -> HttpResponse {
    // Return empty search results for now
    let results: Vec<String> = vec![];
    HttpResponse::Ok().json(ResponseVO::success(Some(results), None))
}
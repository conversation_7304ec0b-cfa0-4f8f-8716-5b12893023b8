use std::path::PathBuf;
use actix_files::NamedFile;
use actix_web::{HttpRequest, HttpResponse};
use crate::utils::path::get_static_dir;

pub async fn ctrl_static_file(req: HttpRequest) -> Result<HttpResponse, Box<dyn std::error::Error>> {
    let rel_path: PathBuf = req.match_info().query("filename").parse()?;
    let static_dir = get_static_dir()?;
    // 将路径转换为标准形式，消除冗余的`.`或`..`
    let static_dir = static_dir.canonicalize()?;
    let abs_path = static_dir.join(&rel_path);
    let result = abs_path.canonicalize();
    if let Err(_) = result {
        return Ok(HttpResponse::NotFound().finish());
    }
    let abs_path = result.unwrap();

    if !abs_path.is_file() || !abs_path.starts_with(&static_dir) {
        return Ok(HttpResponse::NotFound().finish());
    }

    let file = NamedFile::open(abs_path)?;
    Ok(file.use_last_modified(true).disable_content_disposition().into_response(&req))
}

use futures_util::StreamExt;
use std::time::{Duration, Instant};
use actix_http::ws::Message;
use actix_web::{rt, web, Error, HttpRequest, HttpResponse};
use lazy_static::lazy_static;
use serde::Serialize;
use serde_derive::Deserialize;
use tokio::select;
use tokio::sync::{broadcast, RwLock};
use tokio::time::interval;


lazy_static! {
    pub static ref BROADCAST_CHANNEL: RwLock<broadcast::Sender<web::Bytes>> = RwLock::new(broadcast::channel(1024).0);
}

#[derive(Serialize, Deserialize, Debug, Clone)]
struct WsMsg<T: Serialize> {
    event: String,
    payload: T,
}

/// How often heartbeat pings are sent.
///
/// Should be half (or less) of the acceptable client timeout.
const HEARTBEAT_INTERVAL: Duration = Duration::from_secs(5);

/// How long before lack of client response causes a timeout.
const CLIENT_TIMEOUT: Duration = Duration::from_secs(10);

/// Broadcast text & binary messages received from a client, respond to ping messages, and monitor
/// connection health to detect network issues and free up resources.
async fn handle_broadcast_ws(
    mut session: actix_ws::Session,
    mut msg_stream: actix_ws::MessageStream,
    mut rx: broadcast::Receiver<web::Bytes>,
) {
    log::info!("connected");

    let mut last_heartbeat = Instant::now();
    let mut interval = interval(HEARTBEAT_INTERVAL);

    let reason = loop {
        // waits for either `msg_stream` to receive a message from the client, the broadcast channel
        // to send a message, or the heartbeat interval timer to tick, yielding the value of
        // whichever one is ready first
        select! {
            broadcast_msg = rx.recv() => {
                let msg = match broadcast_msg {
                    Ok(msg) => msg,
                    Err(broadcast::error::RecvError::Closed) => break None,
                    Err(broadcast::error::RecvError::Lagged(_)) => continue,
                };

                let res = match std::str::from_utf8(&msg) {
                    Ok(val) => session.text(val).await,
                    Err(_) => session.binary(msg).await,
                };

                if let Err(err) = res {
                    log::error!("{err}");
                    break None;
                }
            }

            // heartbeat interval ticked
            _tick = interval.tick() => {
                // if no heartbeat ping/pong received recently, close the connection
                if Instant::now().duration_since(last_heartbeat) > CLIENT_TIMEOUT {
                    log::info!(
                        "client has not sent heartbeat in over {CLIENT_TIMEOUT:?}; disconnecting"
                    );

                    break None;
                }

                // send heartbeat ping
                let _ = session.ping(b"").await;
            },

            msg = msg_stream.next() => {
                let msg = match msg {
                    // received message from WebSocket client
                    Some(Ok(msg)) => msg,

                    // client WebSocket stream error
                    Some(Err(err)) => {
                        log::error!("{err}");
                        break None;
                    }

                    // client WebSocket stream ended
                    None => break None
                };

                log::debug!("msg: {msg:?}");

                match msg {
                    Message::Text(_) => {
                        // drop client's text messages
                    }

                    Message::Binary(_) => {
                        // drop client's binary messages
                    }

                    Message::Close(reason) => {
                        break reason;
                    }

                    Message::Ping(bytes) => {
                        last_heartbeat = Instant::now();
                        let _ = session.pong(&bytes).await;
                    }

                    Message::Pong(_) => {
                        last_heartbeat = Instant::now();
                    }

                    Message::Continuation(_) => {
                        log::warn!("no support for continuation frames");
                    }

                    // no-op; ignore
                    Message::Nop => {}
                };
            }
        }
    };

    // attempt to close connection gracefully
    let _ = session.close(reason).await;

    log::info!("disconnected");
}

/// Handshake and start broadcast WebSocket handler with heartbeats.
pub async fn broadcast_ws(
    req: HttpRequest,
    stream: web::Payload,
) -> Result<HttpResponse, Error> {
    let (res, session, msg_stream) = actix_ws::handle(&req, stream)?;

    let tx = BROADCAST_CHANNEL.read().await;

    // spawn websocket handler (and don't await it) so that the response is returned immediately
    rt::spawn(handle_broadcast_ws(session, msg_stream, tx.subscribe()));

    Ok(res)
}

/// Send message to clients connected to broadcast WebSocket.
pub async fn send_to_broadcast_ws(
    event: &str,
    payload: impl Serialize,
) -> Result<(), Box<dyn std::error::Error>> {
    let tx = BROADCAST_CHANNEL.read().await;
    if tx.receiver_count() > 0 {
        let msg = WsMsg {
            event: event.to_string(),
            payload,
        };
        tx.send(web::Bytes::from(serde_json::to_string(&msg)?))?;
    }
    Ok(())
}
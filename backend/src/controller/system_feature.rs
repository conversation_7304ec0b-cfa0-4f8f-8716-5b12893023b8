use actix_web::{web, HttpResponse};
use crate::init_db;
use crate::models::ResponseVO;
use crate::services::system_feature::SystemFeature;
use crate::utils::path::set_user_data_dir_custom_config;
use crate::vo::path::UserDataDirCustomConfigVo;


pub async fn ctrl_get_system_feat_details() -> HttpResponse {
    let result = SystemFeature::get_system_feat_details();
    if let Err(e) = result {
        return HttpResponse::InternalServerError().json(e);
    }
    let system_feat_details = result.unwrap();
    HttpResponse::Ok().json(system_feat_details)
}


pub async fn ctrl_set_user_data_dir_custom_config(
    config: web::Json<UserDataDirCustomConfigVo>,
) -> HttpResponse {
    let config = config.0.to();
    let result = set_user_data_dir_custom_config(config);
    if let Err(e) = result {
        return HttpResponse::InternalServerError().json(ResponseVO::<()>::error(Some(e.to_string())));
    }
    let result = init_db().await;
    if let Err(e) = result {
        return HttpResponse::InternalServerError().json(ResponseVO::<()>::error(Some(e.to_string())));
    }
    // 更改自定义文件夹后，需要重新初始化db
    HttpResponse::Ok().json(ResponseVO::<()>::success(None, None))
}


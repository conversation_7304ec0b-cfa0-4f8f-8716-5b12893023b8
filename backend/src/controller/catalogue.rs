use actix_web::{web, HttpResponse};
use serde_derive::Deserialize;
use crate::services::catalogue;

#[derive(Deserialize)]
pub struct BookIdParam {
    #[serde(rename = "bookId")]
    pub book_id: String,
}

pub async fn ctrl_get_catalogues_by_book_id(
    p: web::Query<BookIdParam>,
) -> HttpResponse {
    let result = catalogue::get_catalogues_by_book_id(p.0.book_id).await;
    match result {
        Ok(book_page) => HttpResponse::Ok().json(book_page),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_get_newspaper_catalogue_by_book_id(
    p: web::Query<BookIdParam>,
) -> HttpResponse {
    let result = catalogue::get_newspaper_catalogue_by_book_id(p.0.book_id).await;
    match result {
        Ok(book_page) => HttpResponse::Ok().json(book_page),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

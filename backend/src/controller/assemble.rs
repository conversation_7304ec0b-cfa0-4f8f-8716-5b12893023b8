use actix_web::{web, HttpResponse};
use serde_derive::Deserialize;
use crate::services::assemble::AssembleService;

#[derive(Deserialize)]
pub struct BookTypeParam {
    #[serde(rename = "bookType")]
    pub book_type: String,
}

pub async fn ctrl_get_assembles_by_type(
    p: web::Query<BookTypeParam>,
) -> HttpResponse {
    let result = AssembleService::get_assembles_by_type(p.0.book_type).await;
    match result {
        Ok(assembles) => HttpResponse::Ok().json(assembles),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}
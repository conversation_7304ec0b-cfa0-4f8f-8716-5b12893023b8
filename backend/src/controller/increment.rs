use actix_web::{web, HttpResponse};
use serde_derive::{Deserialize, Serialize};
use crate::services::increment::IncrementService;

#[derive(Deserialize, Serialize)]
pub(crate) struct ImportIncQueueParam {
    #[serde(rename = "filePaths")]
    pub(crate) file_paths: Vec<String>,
    #[serde(rename = "deleteFiles")]
    pub(crate) opt_delete_files: Option<bool>,
}

#[derive(Deserialize, Serialize)]
pub(crate) struct RecordIdParam {
    #[serde(rename = "rId")]
    pub(crate) r_id: String,
}

pub async fn ctrl_get_increment_result() -> HttpResponse {
    let result =
        IncrementService::get_increment_result().await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_import_increments_queue(
    p: web::Json<ImportIncQueueParam>
) -> HttpResponse {
    let result =
        IncrementService::import_increments_queue(p.0.file_paths, p.0.opt_delete_files.unwrap_or(false)).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_delete_increment_record_by_id(
    p: web::Query<RecordIdParam>
) -> HttpResponse {
    let result =
        IncrementService::delete_record_by_id(p.0.r_id).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}
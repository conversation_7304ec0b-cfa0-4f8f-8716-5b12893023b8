use actix_web::{web, HttpResponse};
use crate::models::maps::{QueryMaps, QueryMapsFilter};
use crate::services::{ maps};

pub async fn ctrl_get_maps(
    p: web::Json<QueryMaps>,
) -> HttpResponse {
    let result = maps::get_maps(p.0).await;
    match result {
        Ok(map_page) => HttpResponse::Ok().json(map_page),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_get_maps_bread_crumb() -> HttpResponse {
    let result = maps::get_maps_bread_crumb().await;
    match result {
        Ok(map_page) => HttpResponse::Ok().json(map_page),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}
pub async fn ctrl_get_maps_filters(
    query: web::Json<QueryMapsFilter>
) -> HttpResponse {
    let result = maps::get_maps_filters(query.0).await;
    match result {
        Ok(map_page) => HttpResponse::Ok().json(map_page),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}
pub async fn ctrl_get_maps_total() -> HttpResponse {
    let result = maps::get_maps_total().await;
    match result {
        Ok(map_page) => HttpResponse::Ok().json(map_page),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}
use actix_web::{web, HttpResponse};
use serde_derive::Deserialize;
use crate::models::book::QueryNews;
use crate::services::book;
use crate::services::book::{BookPageByTypesParams, BookService};

#[derive(Deserialize)]
pub struct BookIdParam {
    #[serde(rename = "bookId")]
    pub book_id: String,
}

pub async fn ctrl_get_book_by_book_id(
    p: web::Query<BookIdParam>,
) -> HttpResponse {
    let result = book::get_book_by_book_id(p.0.book_id).await;
    match result {
        Ok(book_page) => HttpResponse::Ok().json(book_page),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_get_book_news_info_by_brand_pub_date(
    p: web::Json<QueryNews>,
) -> HttpResponse {
    let result = book::get_book_news_info_by_brand_pub_date(p.0).await;
    match result {
        Ok(book_page) => HttpResponse::Ok().json(book_page),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_get_book_page_by_types(
    params: web::Json<BookPageByTypesParams>
) -> HttpResponse {
    let result = BookService::get_book_page_by_types(params.0).await;
    match result {
        Ok(book_page) => HttpResponse::Ok().json(book_page),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}
use actix_web::{web, HttpResponse};
use serde_derive::Deserialize;
use crate::services::book::BookService;
use crate::services::dictionary::{DictionaryService, SearchResourcesParams};

#[derive(Deserialize)]
pub struct BookIdParam {
    #[serde(rename = "bookId")]
    pub book_id: String,
}

#[derive(Deserialize)]
pub struct BookPwtParam {
    #[serde(rename = "bookId")]
    pub book_id: String,
    #[serde(rename = "pyWithoutTone")]
    pub py_without_tone: String,
}

#[derive(Deserialize)]
pub struct LetterIdParam {
    #[serde(rename = "letterId")]
    pub letter_id: u64,
}

#[derive(Deserialize)]
pub struct VocIdParam {
    #[serde(rename = "vocabularyId")]
    pub vocabulary_id: u64,
}

#[derive(Deserialize)]
pub struct BIdRIdParam {
    #[serde(rename = "bookId")]
    pub book_id: String,
    #[serde(rename = "radicalId")]
    pub radical_id: u64,
}

#[derive(Deserialize)]
pub struct LetterStrokeBIdParam {
    #[serde(rename = "bookId")]
    pub book_id: String,
    #[serde(rename = "strokeCount")]
    pub stroke_count: u16,
    #[serde(rename = "startStroke")]
    pub start_stroke: Option<String>,
}

pub async fn ctrl_get_dictionary(
    p: web::Query<BookIdParam>,
) -> HttpResponse {
    let result =
        BookService::get_book_vo_by_id(p.0.book_id).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_get_pinyin_without_tones(
    p: web::Query<BookIdParam>,
) -> HttpResponse {
    let result =
        DictionaryService::get_pinyin_without_tones(p.0.book_id).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_get_letters_with_pinyin_by_without_tone(
    p: web::Query<BookPwtParam>,
) -> HttpResponse {
    let result =
        DictionaryService::get_letters_with_pinyin_by_without_tone(p.0.book_id, p.0.py_without_tone).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_get_letter_by_id(
    p: web::Query<LetterIdParam>,
) -> HttpResponse {
    let result =
        DictionaryService::get_letter_by_id(p.0.letter_id).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_get_vocabulary_by_id(
    p: web::Query<VocIdParam>,
) -> HttpResponse {
    let result =
        DictionaryService::get_vocabulary_by_id(p.0.vocabulary_id).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_get_all_radicals(
    p: web::Query<BookIdParam>,
) -> HttpResponse {
    let result =
        DictionaryService::get_all_radicals(p.0.book_id).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_get_letter_sections_by_radical_id(
    p: web::Query<BIdRIdParam>,
) -> HttpResponse {
    let result =
        DictionaryService::get_letter_sections_by_radical_id(p.0.radical_id, p.0.book_id).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_get_letters_by_stroke_count(
    p: web::Query<LetterStrokeBIdParam>,
) -> HttpResponse {
    let result =
        DictionaryService::get_letters_by_stroke_count(p.0.stroke_count, p.0.book_id, p.0.start_stroke).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_get_snapshots(
    p: web::Query<BookIdParam>,
) -> HttpResponse {
    let result =
        DictionaryService::get_snapshots(p.0.book_id).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_search_resources(
    p: web::Json<SearchResourcesParams>,
) -> HttpResponse {
    let result =
        DictionaryService::search_resources(p.0).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

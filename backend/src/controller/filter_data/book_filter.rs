use actix_web::{web, HttpResponse};
use crate::services::filter_data::book_filter::{BookFilter, BookFilterParams};


pub async fn ctrl_filter_subjects(
    p: web::Json<BookFilterParams>,
) -> HttpResponse {
    let result = BookFilter::subjects(p.0).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_filter_versions(
    p: web::Json<BookFilterParams>,
) -> HttpResponse {
    let result = BookFilter::versions(p.0).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_filter_grades(
    p: web::Json<BookFilterParams>,
) -> HttpResponse {
    let result = BookFilter::grades(p.0).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_filter_series(
    p: web::Json<BookFilterParams>,
) -> HttpResponse {
    let result = BookFilter::series(p.0).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_filter_authors(
    p: web::Json<BookFilterParams>,
) -> HttpResponse {
    let result = BookFilter::authors(p.0).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_filter_book_types(
    p: web::Json<BookFilterParams>,
) -> HttpResponse {
    let result = BookFilter::book_types(p.0).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_filter_books(
    p: web::Json<BookFilterParams>,
) -> HttpResponse {
    let result = BookFilter::books(p.0).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}


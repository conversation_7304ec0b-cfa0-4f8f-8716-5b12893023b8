use actix_web::{HttpResponse, web};
use crate::models::activation::ParseCodeParams;
use crate::models::ResponseVO;
use crate::mutex::activation::{is_activate_load, is_activated, machine_key, parse_code, un_activate_info};


pub async fn ctrl_get_activate_load_status() -> HttpResponse {
    let res = is_activate_load().await;
    let resp = ResponseVO::success(Some(res), None);
    HttpResponse::Ok().json(resp)
}

pub async fn ctrl_get_activate_status() -> HttpResponse {
    let res = is_activated().await;
    let resp = ResponseVO::success(Some(res), None);
    HttpResponse::Ok().json(resp)
}

pub async fn ctrl_get_un_activate_info() -> HttpResponse {
    let res = un_activate_info().await;
    let resp = ResponseVO::success(Some("单机版 ".to_owned() + &res), None);
    HttpResponse::Ok().json(resp)
}

pub async fn ctrl_get_activate_key() -> HttpResponse {
    let res = machine_key().await;
    let resp = ResponseVO::success(Some(res), None);
    HttpResponse::Ok().json(resp)
}

pub async fn ctrl_activate_parse_code(query: web::Query<ParseCodeParams>) -> HttpResponse {
    let res = parse_code(query.into_inner()).await;
    let resp = ResponseVO::success(Some(res), None);
    HttpResponse::Ok().json(resp)
}
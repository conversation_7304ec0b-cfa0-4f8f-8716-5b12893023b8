use actix_web::{web, HttpResponse};
use crate::models::law::QueryLaws;
use crate::services::{law};

pub async fn ctrl_get_law(
    p: web::Json<QueryLaws>,
) -> HttpResponse {
    let result = law::get_law(p.0).await;
    match result {
        Ok(law_page) => HttpResponse::Ok().json(law_page),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}
pub async fn ctrl_get_law_total() -> HttpResponse {
    let result = law::get_law_total().await;
    match result {
        Ok(law_page) => HttpResponse::Ok().json(law_page),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

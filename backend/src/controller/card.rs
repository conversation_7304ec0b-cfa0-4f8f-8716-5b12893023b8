use actix_web::{web, HttpResponse};
use serde_derive::Deserialize;
use crate::models::card::QueryNewspaperContext;
use crate::services::card;

#[derive(Deserialize)]
pub struct BookIdParams {
    #[serde(rename = "bookId")]
    pub book_id: u64,
}

pub async fn ctrl_get_card_infos_by_book_id(
    p: web::Query<BookIdParams>,
) -> HttpResponse {
    let result = card::get_card_infos_by_book_id(p.0.book_id).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}

pub async fn ctrl_get_card_content_by_book_id_card_id(
    p: web::Json<QueryNewspaperContext>,
) -> HttpResponse {
    let result = card::get_card_content_by_book_id_card_id(p.0).await;
    match result {
        Ok(data) => HttpResponse::Ok().json(data),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}
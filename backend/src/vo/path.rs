use std::path::Path;
use serde_derive::{Deserialize, Serialize};
use crate::utils::path::UserDataDirCustomConfig;

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct UserDataDirCustomConfigVo {
    pub enable: bool,
    pub path: String,
    #[serde(rename = "warning")]
    pub opt_warning: Option<String>,
}

impl UserDataDirCustomConfigVo {
    pub fn from(config: &UserDataDirCustomConfig) -> Self {
        let mut temp_enable = config.enable;
        let temp_path = Path::new(&config.path);
        let opt_warning = if config.enable && (!temp_path.exists() || !temp_path.is_dir()) {
            temp_enable = false;
            Some("自定义用户数据文件夹不存在或不合法，请重新设置".to_string())
        } else { None };
        Self {
            enable: temp_enable,
            path: config.path.clone(),
            opt_warning,
        }
    }

    pub fn to(&self) -> UserDataDirCustomConfig {
        UserDataDirCustomConfig {
            enable: self.enable,
            path: self.path.clone(),
        }
    }
}
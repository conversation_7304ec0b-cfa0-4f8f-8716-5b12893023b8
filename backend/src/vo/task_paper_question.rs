use crate::services::task_paper_question::{Catalogue, SAGCRItem, TaskPaperQuestion};
use serde_derive::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TaskPaperQuestionVo {
    #[serde(rename = "id")]
    pub id: String,
    #[serde(rename = "subjectCode")]
    pub subject_code: String,
    #[serde(rename = "taskId")]
    pub task_id: String,
    #[serde(rename = "paperId")]
    pub paper_id: String,
    pub serial: i32,
    pub status: i32,
    #[serde(rename = "answerSheetLine")]
    pub answer_sheet_line: i32,
    #[serde(rename = "queNo")]
    pub que_no: String,
    #[serde(rename = "parsedQueNo")]
    pub parsed_que_no: Option<String>,
    #[serde(rename = "queTypeCode")]
    pub que_type_code: u64,
    #[serde(rename = "queTypeName")]
    pub que_type_name: String,
    #[serde(rename = "choiceCount")]
    pub choice_count: usize,
    pub score: f64,
    pub catalogues: Vec<Catalogue>,
    #[serde(rename = "catalogTree")]
    pub catalog_tree: Option<String>,
    pub content: String,
    pub material: Option<String>,
    pub stem: String,
    #[serde(rename = "choicesList")]
    pub choices_list: Vec<String>,
    pub answer: String,
    #[serde(rename = "subQuestions")]
    pub sub_questions: Vec<TaskPaperQuestion>,
    #[serde(rename = "materialSAGCR")]
    pub material_sagcr: Option<Vec<SAGCRItem>>,
    #[serde(rename = "stemSAGCR")]
    pub stem_sagcr: Option<Vec<SAGCRItem>>,
    #[serde(rename = "choicesListSAGCR")]
    pub choices_list_sagcr: Option<Vec<Vec<SAGCRItem>>>,
    #[serde(rename = "answerSAGCR")]
    pub answer_sagcr: Option<Vec<SAGCRItem>>,
    pub difficulty: Option<f64>,
    pub discrimination: Option<f64>,
    #[serde(rename = "perceptionLevel")]
    pub perception_level: Option<String>,
    #[serde(rename = "timeConsumption")]
    pub time_consumption: Option<String>,
}

impl TaskPaperQuestionVo {
    pub fn from(q: &TaskPaperQuestion) -> Self {
        Self {
            id: q.id.clone(),
            subject_code: q.subject_code.clone(),
            task_id: q.task_id.to_string(),
            paper_id: q.paper_id.to_string(),
            serial: q.serial,
            status: q.status,
            answer_sheet_line: q.answer_sheet_line,
            que_no: q.que_no.clone(),
            parsed_que_no: q.parsed_que_no.clone(),
            que_type_code: q.que_type_code,
            que_type_name: q.que_type_name.clone(),
            choice_count: q.choice_count,
            score: q.score,
            catalogues: q.catalogues.clone(),
            catalog_tree: q.catalog_tree.clone(),
            content: q.content.clone(),
            material: q.material.clone(),
            stem: q.stem.clone(),
            choices_list: q.choices_list.clone(),
            answer: q.answer.clone(),
            sub_questions: q.sub_questions.clone(),
            material_sagcr: q.material_sagcr.clone(),
            stem_sagcr: q.stem_sagcr.clone(),
            choices_list_sagcr: q.choices_list_sagcr.clone(),
            answer_sagcr: q.answer_sagcr.clone(),
            difficulty: q.difficulty,
            discrimination: q.discrimination,
            perception_level: q.perception_level.clone(),
            time_consumption: q.time_consumption.clone(),
        }
    }

    pub fn from_list(qs: &Vec<TaskPaperQuestion>) -> Vec<Self> {
        qs.iter().map(|b| Self::from(b)).collect()
    }

    pub fn to(&self) -> TaskPaperQuestion {
        TaskPaperQuestion {
            id: self.id.clone(),
            subject_code: self.subject_code.clone(),
            task_id: self.task_id.parse().unwrap(),
            paper_id: self.paper_id.parse().unwrap(),
            serial: self.serial,
            status: self.status,
            answer_sheet_line: self.answer_sheet_line,
            que_no: self.que_no.clone(),
            parsed_que_no: self.parsed_que_no.clone(),
            que_type_code: self.que_type_code,
            que_type_name: self.que_type_name.clone(),
            choice_count: self.choice_count,
            score: self.score,
            catalogues: self.catalogues.clone(),
            catalog_tree: self.catalog_tree.clone(),
            content: self.content.clone(),
            material: self.material.clone(),
            stem: self.stem.clone(),
            choices_list: self.choices_list.clone(),
            answer: self.answer.clone(),
            sub_questions: self.sub_questions.clone(),
            material_sagcr: self.material_sagcr.clone(),
            stem_sagcr: self.stem_sagcr.clone(),
            choices_list_sagcr: self.choices_list_sagcr.clone(),
            answer_sagcr: self.answer_sagcr.clone(),
            difficulty: self.difficulty,
            discrimination: self.discrimination,
            perception_level: self.perception_level.clone(),
            time_consumption: self.time_consumption.clone(),
        }
    }

    // pub fn to_list(vos: &Vec<Self>) -> Vec<TaskPaperQuestion> {
    //     vos.iter().map(|b| b.to()).collect()
    // }
}

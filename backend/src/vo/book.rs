use serde_derive::{Deserialize, Serialize};
use crate::models::book::Book;

#[derive(Deserialize, Serialize, Debug, Clone)]
pub(crate) struct BookVo {
    pub id: String,
    pub batch: Option<i32>,
    pub author: Option<String>,
    #[serde(rename = "bookName")]
    pub book_name: String,
    pub isbn: String,
    pub publisher: Option<String>,
    pub series: Option<String>,
    #[serde(rename = "coverImageUrl")]
    pub cover_image_url: Option<String>,
    #[serde(rename = "subjectCode")]
    pub subject_code: String,
    #[serde(rename = "subjectName")]
    pub subject_name: String,
    #[serde(rename = "type")]
    pub r#type: String,
    #[serde(rename = "updateTime")]
    pub update_time: Option<String>,
    pub version: Option<String>,
    pub edition: Option<String>,
    #[serde(rename = "publishDate")]
    pub publish_date: Option<String>,
    pub brand: Option<String>,
    pub period: Option<i32>,
    pub grade: Option<String>,
}

impl BookVo {
    pub fn from(book: &Book) -> Self {
        Self {
            id: book.book_id.to_string(),
            batch: book.batch,
            author: book.author.clone(),
            book_name: book.book_name.clone(),
            isbn: book.isbn.clone(),
            publisher: book.publisher.clone(),
            series: book.series.clone(),
            cover_image_url: book.snapshots.first().map(|s| s.path.clone()),
            subject_code: book.subject_code.clone(),
            subject_name: book.subject_name.clone(),
            r#type: book.r#type.clone(),
            update_time: book.update_time.clone(),
            version: book.version.clone(),
            edition: book.edition.clone(),
            publish_date: book.publish_date.clone(),
            brand: book.brand.clone(),
            period: book.period,
            grade: book.grade.clone(),
        }
    }

    pub fn from_list(list: &Vec<Book>) -> Vec<Self> {
        list.iter().map(Self::from).collect()
    }
}
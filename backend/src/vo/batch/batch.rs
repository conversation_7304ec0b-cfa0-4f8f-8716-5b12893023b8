use crate::services::batch::batch::GeneralExamPaperBatch;
use serde_derive::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct GeneralExamPaperBatchVo {
    #[serde(rename = "id")]
    pub id: String,
    pub name: String,
    #[serde(rename = "paperCount")]
    pub paper_count: u32,
    #[serde(rename = "answerCount")]
    pub answer_count: u32,
    #[serde(rename = "isDeleted")]
    pub is_deleted: u32,
    #[serde(rename = "updateTime")]
    pub update_time: u64,
    #[serde(rename = "createTime")]
    pub create_time: u64,
    pub status: u64,
}

impl GeneralExamPaperBatchVo {
    pub fn from(b: &GeneralExamPaperBatch) -> Self {
        Self {
            id: b.id.to_string(),
            name: b.name.clone(),
            paper_count: b.paper_count,
            answer_count: b.answer_count,
            is_deleted: b.is_deleted,
            update_time: b.update_time,
            create_time: b.create_time,
            status: b.status,
        }
    }

    pub fn from_list(batches: &Vec<GeneralExamPaperBatch>) -> Vec<Self> {
        batches.iter().map(|b| Self::from(b)).collect()
    }

    // pub fn to(&self) -> GeneralExamPaperBatch {
    //     GeneralExamPaperBatch {
    //         id: self.id.parse().unwrap(),
    //         name: self.name.clone(),
    //         paper_count: self.paper_count,
    //         answer_count: self.answer_count,
    //         is_deleted: self.is_deleted,
    //         update_time: self.update_time,
    //         create_time: self.create_time,
    //         status: self.status,
    //     }
    // }

    // pub fn to_list(batches: &Vec<Self>) -> Vec<GeneralExamPaperBatch> {
    //     batches.iter().map(|b| b.to()).collect()
    // }
}

use crate::services::batch::general_exam_paper::GeneralExamPaper;
use serde_derive::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct GeneralExamPaperVo {
    #[serde(rename = "id")]
    pub id: String,
    #[serde(rename = "batchId")]
    pub batch_id: String,
    pub name: String,
    #[serde(rename = "fileName")]
    pub file_name: String,
    #[serde(rename = "subjectName")]
    pub subject_name: String,
    #[serde(rename = "subjectCode")]
    pub subject_code: String,
    #[serde(rename = "isAnswer")]
    pub is_answer: bool,
    #[serde(rename = "answerId")]
    pub answer_id: Option<String>,
    #[serde(rename = "isDeleted")]
    pub is_deleted: u32,
    // 状态枚举：100：初始化；200：绑定任务；300：解析中；310：解析完成；400：异常
    pub status: u32,
    #[serde(rename = "updateTime")]
    pub update_time: u64,
    #[serde(rename = "createTime")]
    pub create_time: u64,
    #[serde(rename = "taskId")]
    pub task_id: String,
    #[serde(rename = "taskPaperId")]
    pub task_paper_id: String,
    pub md5: String,
    pub remark: String,
}

impl GeneralExamPaperVo {
    pub fn from(p: &GeneralExamPaper) -> Self {
        Self {
            id: p.id.to_string(),
            batch_id: p.batch_id.to_string(),
            name: p.name.clone(),
            file_name: p.file_name.clone(),
            subject_name: p.subject_name.clone(),
            subject_code: p.subject_code.clone(),
            is_answer: p.is_answer,
            answer_id: p.answer_id.map(|id| id.to_string()),
            is_deleted: p.is_deleted,
            status: p.status,
            update_time: p.update_time,
            create_time: p.create_time,
            task_id: p.task_id.to_string(),
            task_paper_id: p.task_paper_id.to_string(),
            md5: p.md5.clone(),
            remark: p.remark.clone(),
        }
    }

    pub fn from_list(batches: &Vec<GeneralExamPaper>) -> Vec<Self> {
        batches.iter().map(|b| Self::from(b)).collect()
    }

    // pub fn to(&self) -> GeneralExamPaper {
    //     GeneralExamPaper {
    //         id: self.id.parse().unwrap(),
    //         batch_id: self.batch_id.parse().unwrap(),
    //         name: self.name.clone(),
    //         file_name: self.file_name.clone(),
    //         subject_name: self.subject_name.clone(),
    //         subject_code: self.subject_code.clone(),
    //         is_answer: self.is_answer,
    //         answer_id: self.answer_id.as_ref().map(|id| id.parse::<u64>().unwrap()),
    //         is_deleted: self.is_deleted,
    //         status: self.status,
    //         update_time: self.update_time,
    //         create_time: self.create_time,
    //         task_id: self.task_id.parse().unwrap(),
    //         task_paper_id: self.task_paper_id.parse().unwrap(),
    //         md5: self.md5.clone(),
    //         remark: self.remark.clone(),
    //     }
    // }

    // pub fn to_list(vos: &Vec<Self>) -> Vec<GeneralExamPaper> {
    //     vos.iter().map(|b| b.to()).collect()
    // }
}

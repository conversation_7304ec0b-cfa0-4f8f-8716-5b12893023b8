use indexmap::IndexMap;
use serde_derive::{Deserialize, Serialize};
use crate::models::radical::Radical;

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct PinyinWithoutToneVo {
    #[serde(rename = "firstChar")]
    pub first_char: String,
    #[serde(rename = "withoutTones")]
    pub without_tones: Vec<String>,
}

#[derive(Serialize, Deserialize, Debug, <PERSON>lone)]
pub struct PinyinSectionVo {
    #[serde(rename = "pinyinId")]
    pub pinyin_id: u64,
    #[serde(rename = "pyWithTone")]
    pub py_with_tone: String,
    pub letters: Vec<LetterInfoVo>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct LetterInfoVo {
    #[serde(rename = "letterId")]
    pub letter_id: u64,
    pub name: String,
    #[serde(rename = "nameOutBase")]
    pub name_out_base: String,
    #[serde(rename = "strokeCount")]
    pub opt_stroke_count: Option<u16>,
}

#[derive(Serialize, Deserialize, Debug, <PERSON>lone)]
pub struct StrokeRadicalsVo {
    pub stroke: u16,
    pub list: Vec<Radical>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct LetterPinyinInfoVo {
    #[serde(rename = "letterId")]
    pub letter_id: u64,
    pub name: String,
    #[serde(rename = "nameOutBase")]
    pub name_out_base: String,
    #[serde(rename = "pinyinList")]
    pub pinyin_list: Vec<String>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct LetterRadicalIdSectionVo {
    #[serde(rename = "radicalId")]
    pub radical_id: u64,
    #[serde(rename = "remainingStrokeCount")]
    pub remaining_stroke_count: u16,
    pub letters: Vec<LetterPinyinInfoVo>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct SRRListItemVo {
    #[serde(rename = "type")]
    pub r#type: String,
    pub id: u64,
    pub name: String,
    #[serde(rename = "nameOutBase")]
    pub name_out_base: String,
    #[serde(rename = "pyWithTone")]
    pub py_with_tone: Vec<String>,
    pub illustration: String,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct SRRListVo {
    pub tip: String,
    pub items: Vec<SRRListItemVo>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct SearchResourceResultVo {
    #[serde(rename = "showRecommend")]
    pub show_recommend: bool,
    #[serde(rename = "showSamePinyin")]
    pub show_same_pinyin: bool,
    #[serde(rename = "showSameRadical")]
    pub show_same_radical: bool,
    pub list: Vec<SRRListVo>,
}

impl StrokeRadicalsVo {
    pub fn from_radicals(mut radicals: Vec<Radical>) -> Vec<Self> {
        radicals.sort_by(|a, b| a.stroke_count.cmp(&b.stroke_count));
        let mut stroke_map: IndexMap<u16, Vec<Radical>> = IndexMap::new();
        for radical in radicals {
            stroke_map.entry(radical.stroke_count).or_insert(Vec::new()).push(radical);
        }
        let return_list: Vec<Self> = stroke_map.into_iter().map(|(sk, rs)| Self {
            stroke: sk,
            list: rs,
        }).collect();
        return_list
    }
}
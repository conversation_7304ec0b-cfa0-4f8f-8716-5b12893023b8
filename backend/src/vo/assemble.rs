use serde_derive::{Deserialize, Serialize};
use crate::models::assemble::Assemble;

#[derive(Serialize, Deserialize, <PERSON><PERSON>, Debug)]
pub struct AssembleVo {
    #[serde(rename = "assembleId")]
    pub assemble_id: String,
    pub name: String,
    #[serde(rename = "bookCount")]
    pub book_count: usize,
    pub publisher: Option<String>,
    #[serde(rename = "subjectCode")]
    pub subject_code: String,
    #[serde(rename = "subjectName")]
    pub subject_name: String,
    #[serde(rename = "type")]
    pub r#type: String,
    #[serde(rename = "recentPublishDate")]
    pub recent_publish_date: Option<String>,
    #[serde(rename = "earliestPublishDate")]
    pub earliest_publish_date: Option<String>,
    pub batch: Option<i32>,
}

impl AssembleVo {
    pub fn from(e: &Assemble) -> Self {
        Self {
            assemble_id: e.assemble_id.to_string(),
            name: e.name.clone(),
            book_count: e.books.len(),
            publisher: e.publisher.clone(),
            subject_code: e.subject_code.clone(),
            subject_name: e.subject_name.clone(),
            r#type: e.r#type.clone(),
            recent_publish_date: e.recent_publish_date.clone(),
            earliest_publish_date: e.earliest_publish_date.clone(),
            batch: e.batch,
        }
    }

    pub fn from_list(list: &Vec<Assemble>) -> Vec<Self> {
        list.iter().map(|e| Self::from(e)).collect()
    }
}
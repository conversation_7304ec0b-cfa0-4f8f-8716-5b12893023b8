use std::collections::HashMap;
use crate::services::task::{
    Attachments, Task, TaskExamSyllabuses, TaskPaper, TaskPropositionResource, Textbook,
};
use serde_derive::{Deserialize, Serialize};
use serde_json::Value;
use crate::services::question_issue::QuestionIssue;
use crate::services::task_paper_question::TaskPaperQuestion;

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct TaskPaperVo {
    #[serde(rename = "deleted")]
    pub deleted: bool,
    #[serde(rename = "paperName")]
    pub paper_name: String,
    #[serde(rename = "taskPaperDocxes")]
    pub task_paper_docxes: Vec<Option<Value>>,
    #[serde(rename = "id")]
    pub id: String,
    #[serde(rename = "acceptanceStatus")]
    pub acceptance_status: i64,
    #[serde(rename = "status")]
    pub status: i64,
}

impl TaskPaperVo {
    pub fn from(p: &TaskPaper) -> Self {
        Self {
            deleted: p.deleted,
            paper_name: p.paper_name.clone(),
            task_paper_docxes: p.task_paper_docxes.clone(),
            id: p.id.to_string(),
            acceptance_status: p.acceptance_status,
            status: p.status,
        }
    }

    pub fn from_list(batches: &Vec<TaskPaper>) -> Vec<Self> {
        batches.iter().map(|b| Self::from(b)).collect()
    }

    // pub fn to(&self) -> TaskPaper {
    //     TaskPaper {
    //         deleted: self.deleted,
    //         paper_name: self.paper_name.clone(),
    //         task_paper_docxes: self.task_paper_docxes.clone(),
    //         id: self.id.parse().unwrap(),
    //         acceptance_status: self.acceptance_status,
    //         status: self.status,
    //     }
    // }

    // pub fn to_list(vos: &Vec<Self>) -> Vec<TaskPaper> {
    //     vos.iter().map(|b| b.to()).collect()
    // }
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct TaskVo {
    #[serde(rename = "createUserId")]
    pub create_user_id: i64,
    #[serde(rename = "updateDate")]
    pub update_date: String,
    #[serde(rename = "attachments")]
    pub attachments: Vec<Attachments>,
    #[serde(rename = "cutoffTime")]
    pub cutoff_time: String,
    #[serde(rename = "provideTextBook")]
    pub provide_text_book: bool,
    #[serde(rename = "provideExamSyllabus")]
    pub provide_exam_syllabus: bool,
    #[serde(rename = "taskExamSyllabuses")]
    pub task_exam_syllabuses: Vec<TaskExamSyllabuses>,
    #[serde(rename = "hasTeacherTerminal")]
    pub has_teacher_terminal: bool,
    #[serde(rename = "taskPapers")]
    pub task_papers: Vec<TaskPaperVo>,
    #[serde(rename = "textbooks")]
    pub textbooks: Vec<Textbook>,
    #[serde(rename = "createdDate")]
    pub created_date: String,
    #[serde(rename = "createStep")]
    pub create_step: i64,
    #[serde(rename = "taskPropositionResources")]
    pub task_proposition_resources: Vec<TaskPropositionResource>,
    #[serde(rename = "paperSection")]
    pub paper_section: i64,
    #[serde(rename = "taskName")]
    pub name: String,
    #[serde(rename = "id")]
    pub id: String,
    #[serde(rename = "subjectCode")]
    pub subject_code: String,
    #[serde(rename = "subjectName")]
    pub subject_name: Option<String>,
    #[serde(rename = "status")]
    pub status: i32,
    #[serde(rename = "fromBatchUpload")]
    pub from_batch_upload: Option<bool>,
}

impl TaskVo {
    pub fn from(t: &Task) -> Self {
        Self {
            create_user_id: t.create_user_id,
            update_date: t.update_date.clone(),
            attachments: t.attachments.clone(),
            cutoff_time: t.cutoff_time.clone(),
            provide_text_book: t.provide_text_book,
            provide_exam_syllabus: t.provide_exam_syllabus,
            task_exam_syllabuses: t.task_exam_syllabuses.clone(),
            has_teacher_terminal: t.has_teacher_terminal,
            task_papers: TaskPaperVo::from_list(&t.task_papers),
            textbooks: t.textbooks.clone(),
            created_date: t.created_date.clone(),
            create_step: t.create_step,
            task_proposition_resources: t.task_proposition_resources.clone(),
            paper_section: t.paper_section,
            name: t.name.clone(),
            id: t.id.to_string(),
            subject_code: t.subject_code.to_string(),
            subject_name: t.subject_name.clone(),
            status: t.status,
            from_batch_upload: t.from_batch_upload,
        }
    }

    pub fn from_list(tasks: &Vec<Task>) -> Vec<Self> {
        tasks.iter().map(|b| Self::from(b)).collect()
    }

    // pub fn to(&self) -> Task {
    //     Task {
    //         create_user_id: self.create_user_id,
    //         update_date: self.update_date.clone(),
    //         attachments: self.attachments.clone(),
    //         cutoff_time: self.cutoff_time.clone(),
    //         provide_text_book: self.provide_text_book,
    //         provide_exam_syllabus: self.provide_exam_syllabus,
    //         task_exam_syllabuses: self.task_exam_syllabuses.clone(),
    //         has_teacher_terminal: self.has_teacher_terminal,
    //         task_papers: TaskPaperVo::to_list(&self.task_papers),
    //         textbooks: self.textbooks.clone(),
    //         created_date: self.created_date.clone(),
    //         create_step: self.create_step,
    //         task_proposition_resources: self.task_proposition_resources.clone(),
    //         paper_section: self.paper_section,
    //         name: self.name.clone(),
    //         id: self.id.parse().unwrap(),
    //         subject_code: self.subject_code.to_string(),
    //         subject_name: self.subject_name.clone(),
    //         status: self.status,
    //         from_batch_upload: self.from_batch_upload,
    //     }
    // }

    // pub fn to_list(vos: &Vec<Self>) -> Vec<Task> {
    //     vos.iter().map(|b| b.to()).collect()
    // }
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct QueInfoPIUIITVo {
    #[serde(rename = "questionId")]
    pub question_id: String,
    #[serde(rename = "queNo")]
    pub que_no: String,
    pub messages: Vec<String>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct PaperInfoUIITVo {
    #[serde(rename = "paperId")]
    pub paper_id: String,
    #[serde(rename = "paperName")]
    pub paper_name: String,
    #[serde(rename = "queInfos")]
    pub que_infos: Vec<QueInfoPIUIITVo>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct UnfinishedInfoInTaskVo {
    #[serde(rename = "taskId")]
    pub task_id: String,
    // 枚举，0：没有未完成信息，可以导出成品包；1：有未完成信息，最好不要导出成品包
    pub code: i16,
    #[serde(rename = "paperInfos")]
    pub paper_infos: Vec<PaperInfoUIITVo>,
}

impl UnfinishedInfoInTaskVo {
    pub fn from(task: &Task, questions: &Vec<TaskPaperQuestion>, que_issues: &Vec<QuestionIssue>) -> Result<Self, Box<dyn std::error::Error>> {
        let mut code = 0;
        let mut pid_ques_map: HashMap<u64, Vec<&TaskPaperQuestion>> = HashMap::new();
        let mut qid_issues_map: HashMap<String, Vec<&QuestionIssue>> = HashMap::new();
        for q in questions.iter() {
            let ques = pid_ques_map.entry(q.paper_id).or_insert(vec![]);
            ques.push(q);
        }
        for iss in que_issues.iter() {
            if let Some(ref qi1) = iss.que_info1 {
                let issues = qid_issues_map.entry(qi1.question_id.clone()).or_insert(vec![]);
                issues.push(iss);
            }
            if let Some(ref qi2) = iss.que_info2 {
                let issues = qid_issues_map.entry(qi2.question_id.clone()).or_insert(vec![]);
                issues.push(iss);
            }
        }

        let mut paper_infos = vec![];
        for tp in task.task_papers.iter() {
            let ques = pid_ques_map.get(&tp.id)
                .ok_or(anyhow::Error::msg(format!("找不到指定试卷试题，paper_id: {}", tp.id)))?;
            let mut que_infos = vec![];
            for q in ques {
                let mut messages = vec![];
                if q.status == 0  {
                    messages.push(format!("第{}题为空题", q.que_no));
                } else if q.status == 3 {
                    messages.push(format!("第{}题内容被修改，需重新校验", q.que_no));
                }
                if q.difficulty.is_none() {
                    messages.push(format!("第{}题未设置预估难度值", q.que_no));
                }
                if let Some(difficulty) = q.difficulty {
                    if difficulty <= 0f64 || difficulty >= 1f64 {
                        messages.push(format!("第{}题预估难度值不能是{}", q.que_no, difficulty));
                    }
                }
                if q.perception_level.is_none() {
                    messages.push(format!("第{}题未设置认知层次", q.que_no));
                }
                if let Some(issues) = qid_issues_map.get(&q.id) {
                    for iss in issues {
                        if iss.status != 0 {
                            messages.push(format!("第{}题存在未消除或未解决的异常", q.que_no));
                            break;
                        }
                    }
                }
                if !messages.is_empty() {
                    que_infos.push(QueInfoPIUIITVo {
                        question_id: q.id.clone(),
                        que_no: q.que_no.clone(),
                        messages,
                    });
                }
            }
            if !que_infos.is_empty() {
                paper_infos.push(PaperInfoUIITVo {
                    paper_id: tp.id.to_string(),
                    paper_name: tp.paper_name.clone(),
                    que_infos,
                });
            }
        }

        if !paper_infos.is_empty() {
            code = 1;
        }

        let uiit_vo = Self {
            task_id: task.id.to_string(),
            code,
            paper_infos,
        };

        Ok(uiit_vo)
    }
}

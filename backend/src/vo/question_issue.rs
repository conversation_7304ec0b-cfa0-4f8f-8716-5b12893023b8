use serde_derive::{Deserialize, Serialize};
use crate::services::question_issue::QueWIDetails;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DupQueRepoTypeMarkVo {
    #[serde(rename = "issueName")]
    pub issue_name: String,
    #[serde(rename = "details")]
    pub details: Vec<Vec<QueWIDetails>>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PaperDupQueRepoItemVo {
    #[serde(rename = "paperId")]
    pub paper_id: String,
    #[serde(rename = "paperName")]
    pub paper_name: String,
    #[serde(rename = "issueTypeMarks")]
    pub issue_type_marks: Vec<DupQueRepoTypeMarkVo>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DupQueReportDetailsVo {
    #[serde(rename = "taskId")]
    pub task_id: String,
    #[serde(rename = "taskName")]
    pub task_name: String,
    #[serde(rename = "subjectCode")]
    pub subject_code: String,
    #[serde(rename = "subjectName")]
    pub subject_name: String,
    #[serde(rename = "taskDqIssueTypeMarks")]
    pub task_dq_issue_type_marks: Vec<DupQueRepoTypeMarkVo>,
    #[serde(rename = "paperDqRepoItems")]
    pub paper_dq_repo_items: Vec<PaperDupQueRepoItemVo>,
}
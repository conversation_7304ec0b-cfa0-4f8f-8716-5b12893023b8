use actix_cors::Cors;
use actix_web::{web, App, HttpServer, middleware::Logger};
use clap::Parser;
use dotenv::dotenv;
use std::env;

mod controller;
mod database;
mod models;
mod services;
mod utils;
mod vo;
mod configs;
mod search;
mod similarity;
mod parser;
mod mutex;
mod activation;

use controller::*;

#[derive(Parser)]
#[command(name = "book-guard-backend")]
#[command(about = "Book Guard Backend API Server")]
struct Args {
    /// Server port
    #[arg(short, long, default_value = "8080")]
    port: u16,
    
    /// Server host
    #[arg(long, default_value = "0.0.0.0")]
    host: String,
    
    /// Enable development mode
    #[arg(short, long)]
    dev: bool,
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    // Load environment variables
    dotenv().ok();
    
    // Initialize logger
    env_logger::init();
    
    // Parse command line arguments
    let args = Args::parse();
    
    // Initialize database
    if let Err(e) = database::surreal::init_db().await {
        eprintln!("Failed to initialize database: {}", e);
        std::process::exit(1);
    }
    
    println!("Starting Book Guard Backend Server on {}:{}", args.host, args.port);
    
    HttpServer::new(move || {
        let cors = if args.dev {
            Cors::permissive()
        } else {
            Cors::default()
                .allowed_origin("http://localhost:5173") // Vite dev server
                .allowed_origin("http://localhost:4173") // Vite preview
                .allowed_methods(vec!["GET", "POST", "PUT", "DELETE", "OPTIONS"])
                .allowed_headers(vec!["Content-Type", "Authorization"])
                .supports_credentials()
        };
        
        App::new()
            .wrap(cors)
            .wrap(Logger::default())
            .service(web::resource("/ws-broadcast").route(web::get().to(websocket::broadcast_ws)))
            .route("/static/{filename:.*}", web::get().to(static_files::ctrl_static_file))
            .service(
                web::scope("/api")
                    // Activation routes
                    .route("/activate/load", web::get().to(activate::ctrl_get_activate_load_status))
                    .route("/activate/status", web::get().to(activate::ctrl_get_activate_status))
                    .route("/activate/info", web::get().to(activate::ctrl_get_un_activate_info))
                    .route("/activate/key", web::get().to(activate::ctrl_get_activate_key))
                    .route("/activate/parseCode", web::get().to(activate::ctrl_activate_parse_code))
                    
                    // System feature routes
                    .route("/system-feature/details", web::get().to(system_feature::ctrl_get_system_feat_details))
                    .route("/system-feature/usage-mode", web::get().to(system_feature::ctrl_get_usage_mode))
                    .route("/system-feature/usage-mode", web::post().to(system_feature::ctrl_set_usage_mode))
                    
                    // Search routes
                    .route("/global-search/cards", web::post().to(searching::ctrl_global_search_cards))
                    .route("/global-search/books", web::post().to(searching::ctrl_global_search_books))
                    
                    // Book routes
                    .route("/book/by-id", web::get().to(book::ctrl_get_book_by_book_id))
                    .route("/book/news-info/by-brand-pub-date", web::post().to(book::ctrl_get_book_news_info_by_brand_pub_date))
                    .route("/book/page/by-types", web::post().to(book::ctrl_get_book_page_by_types))
                    
                    // Card routes
                    .route("/card/infos/by-book-id", web::get().to(card::ctrl_get_card_infos_by_book_id))
                    .route("/card/content/by-book-id-card-id", web::post().to(card::ctrl_get_card_content_by_book_id_card_id))
                    
                    // Catalogue routes
                    .route("/catalogue/by-book-id", web::get().to(catalogue::ctrl_get_catalogues_by_book_id))
                    .route("/catalogue/newspaper/by-book-id", web::get().to(catalogue::ctrl_get_newspaper_catalogue_by_book_id))
                    
                    // Dictionary routes
                    .route("/dictionary", web::get().to(dictionary::ctrl_get_dictionary))
                    .route("/dictionary/pinyin/without-tones", web::get().to(dictionary::ctrl_get_pinyin_without_tones))
                    .route("/dictionary/letters/with-pinyin/by-without-tone", web::get().to(dictionary::ctrl_get_letters_with_pinyin_by_without_tone))
                    .route("/dictionary/letter/by-id", web::get().to(dictionary::ctrl_get_letter_by_id))
                    .route("/dictionary/vocabulary/by-id", web::get().to(dictionary::ctrl_get_vocabulary_by_id))
                    .route("/dictionary/radicals/all", web::get().to(dictionary::ctrl_get_all_radicals))
                    .route("/dictionary/letter-sections/by-radical-id", web::get().to(dictionary::ctrl_get_letter_sections_by_radical_id))
                    .route("/dictionary/letters/by-stroke-count", web::get().to(dictionary::ctrl_get_letters_by_stroke_count))
                    
                    // Filter data routes
                    .route("/filter/book-types", web::post().to(filter_data::ctrl_filter_book_types))
                    .route("/filter/subjects", web::post().to(filter_data::ctrl_filter_subjects))
                    .route("/filter/versions", web::post().to(filter_data::ctrl_filter_versions))
                    .route("/filter/grades", web::post().to(filter_data::ctrl_filter_grades))
                    .route("/filter/series", web::post().to(filter_data::ctrl_filter_series))
                    .route("/filter/authors", web::post().to(filter_data::ctrl_filter_authors))
                    .route("/filter/books", web::post().to(filter_data::ctrl_filter_books))
                    
                    // Assemble routes
                    .route("/assemble/by-type", web::get().to(assemble::ctrl_get_assembles_by_type))
                    
                    // Increment routes
                    .route("/increment/result", web::get().to(increment::ctrl_get_increment_result))
                    .route("/increment/queue", web::post().to(increment::ctrl_import_increments_queue))
                    .route("/increment/record/{id}", web::delete().to(increment::ctrl_delete_increment_record_by_id))
                    .route("/increment/file-upd/records", web::get().to(increment::ctrl_get_inc_file_upd_records))
                    
                    // File upload routes
                    .route("/file-upload/snapshots", web::get().to(file_upload::ctrl_get_snapshots))
                    .route("/file-upload/search-resources", web::post().to(file_upload::ctrl_search_resources))
                    
                    // Maps routes
                    .route("/maps", web::get().to(maps::ctrl_get_maps))
                    .route("/maps/bread-crumb", web::get().to(maps::ctrl_get_maps_bread_crumb))
                    .route("/maps/filters", web::get().to(maps::ctrl_get_maps_filters))
                    .route("/maps/total", web::get().to(maps::ctrl_get_maps_total))
                    
                    // Law routes
                    .route("/law", web::get().to(law::ctrl_get_law))
                    .route("/law/total", web::get().to(law::ctrl_get_law_total))
                    
                    // Plagiarism routes
                    .route("/plagiarism/events", web::get().to(plagiarism::plagiarism_events_sse))
                    .route("/plagiarism/compare", web::post().to(plagiarism::ctrl_plagiarism_compare))
                    .route("/plagiarism/results", web::get().to(plagiarism::ctrl_plagiarism_results))
                    .route("/plagiarism/batches", web::get().to(plagiarism::ctrl_plagiarism_batches))
                    .route("/plagiarism/batch/{batch_id}/detail", web::get().to(plagiarism::ctrl_plagiarism_batch_detail))
                    .route("/plagiarism/batch/{batch_id}/statistics", web::get().to(plagiarism::ctrl_plagiarism_batch_statistics))
                    .route("/plagiarism/batch/{batch_id}", web::delete().to(plagiarism::ctrl_plagiarism_delete_batch))
                    .route("/plagiarism/batch/{batch_id}/cancel", web::post().to(plagiarism::ctrl_plagiarism_cancel_comparison))
            )
    })
    .bind((args.host, args.port))?
    .run()
    .await
}

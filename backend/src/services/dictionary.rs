use std::collections::{HashMap, HashSet};
use indexmap::IndexMap;
use serde_derive::{Deserialize, Serialize};
use crate::database::book::BookRepository;
use crate::database::letter::LetterRepository;
use crate::database::pinyin::PinyinRepository;
use crate::database::radical::RadicalRepository;
use crate::database::vocabulary::VocabularyRepository;
use crate::models::book::Snapshot;
use crate::models::letter::Letter;
use crate::models::ResponseVO;
use crate::models::vocabulary::Vocabulary;
use crate::services::filter_data::letter_filter::LetterFilter;
use crate::services::filter_data::vocabulary_filter::VocabularyFilter;
use crate::utils::string_util::all_chinese_characters;
use crate::vo::dictionary::{
    LetterInfoVo, LetterPinyinInfoVo, LetterRadicalIdSectionVo, PinyinSectionVo,
    PinyinWithoutToneVo, SRRListItemVo, SRRListVo, SearchResourceResultVo, StrokeRadicalsVo
};

#[derive(Serialize, Deserialize, Debug, Clone)]
pub(crate) struct SearchResourcesParams {
    #[serde(rename = "searchText")]
    pub(crate) search_text: String,
    // # 可选参数，枚举：1推荐；2同音；3同部首
    #[serde(rename = "searchType")]
    pub(crate) opt_search_type: Option<i32>,
    #[serde(rename = "bookId")]
    pub(crate) book_id: String,
}

pub(crate) struct DictionaryService;


impl DictionaryService {
    pub(crate) async fn get_pinyin_without_tones(book_id: String) -> Result<ResponseVO<Vec<PinyinWithoutToneVo>>, ResponseVO<()>> {
        let book_id = book_id.parse()
            .map_err(|_| ResponseVO::error(Some("invalid bookId".to_string())))?;
        let all_pinyin_list = PinyinRepository::find_all_by_book_id_and_is_single_kanji(book_id, true).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        let mut fc_py_map = HashMap::new();
        for py in all_pinyin_list {
            fc_py_map.entry(py.first_char.clone()).or_insert(Vec::new()).push(py);
        }

        let mut all_without_tone = vec![];
        for fc_index in 0..25 {
            let first_char = ((b'A' + fc_index) as char).to_string();
            let pinyin_list = fc_py_map.remove(&first_char).unwrap_or(Vec::new());
            if !pinyin_list.is_empty() {
                let without_tone_set: HashSet<String> = pinyin_list.into_iter()
                    .map(|p| p.without_tone)
                    .collect();
                let mut without_tones = Vec::new();
                without_tones.extend(without_tone_set.into_iter());
                without_tones.sort();
                all_without_tone.push(PinyinWithoutToneVo {
                    first_char: first_char.clone(),
                    without_tones,
                });
            }
        }
        Ok(ResponseVO::success(Some(all_without_tone), None))
    }

    pub(crate) async fn get_letters_with_pinyin_by_without_tone(
        book_id: String, py_without_tone: String
    ) -> Result<ResponseVO<Vec<PinyinSectionVo>>, ResponseVO<()>> {
        let book_id = book_id.parse()
            .map_err(|_| ResponseVO::error(Some("invalid bookId".to_string())))?;
        let pinyin_list = PinyinRepository::find_all_by_book_id_and_is_single_kanji_and_without_tone(
            book_id, true, py_without_tone
        ).await.map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        let pinyin_id_set: HashSet<u64> = pinyin_list.iter().map(|p| p.pinyin_id).collect();
        let letters = LetterFilter::filter_letters(book_id, |le| {
            if let Some(ref py_ids ) = le.opt_pinyin_ids {
                for py_id in py_ids {
                    if pinyin_id_set.contains(py_id) {
                        return true;
                    }
                }
            }
            false
        }).await;
        let mut py_letters_map: HashMap<u64, Vec<Letter>> = HashMap::new();
        for le in letters.into_iter() {
            if let Some(ref pinyin_ids) = le.opt_pinyin_ids {
                for py_id in pinyin_ids.iter() {
                    py_letters_map.entry(*py_id).or_insert(Vec::new()).push(le.clone());
                }
            }
        }

        let mut sections = vec![];
        for py in pinyin_list.iter() {
            let temp_letters = py_letters_map.remove(&py.pinyin_id).unwrap_or(Vec::new());
            let letter_infos = temp_letters
                .into_iter()
                .map(|tl| LetterInfoVo {
                    letter_id: tl.letter_id,
                    name: tl.name.clone(),
                    name_out_base: tl.name_out_base.clone(),
                    opt_stroke_count: tl.opt_stroke_count,
                }).collect::<Vec<LetterInfoVo>>();
            sections.push(PinyinSectionVo {
                pinyin_id: py.pinyin_id,
                py_with_tone: py.with_tone.clone(),
                letters: letter_infos,
            });
        }

        Ok(ResponseVO::success(Some(sections), None))
    }

    pub(crate) async fn get_letter_by_id(
        letter_id: u64
    ) -> Result<ResponseVO<Letter>, ResponseVO<()>> {
        let letter = LetterRepository::find_by_id(letter_id).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?
            .ok_or(ResponseVO::error(Some("找不到指定汉字".to_string())))?;
        Ok(ResponseVO::success(Some(letter), None))
    }

    pub(crate) async fn get_vocabulary_by_id(
        vocabulary_id: u64
    ) -> Result<ResponseVO<Vocabulary>, ResponseVO<()>> {
        let voc = VocabularyRepository::find_by_id(vocabulary_id).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?
            .ok_or(ResponseVO::error(Some("找不到指定词语".to_string())))?;
        Ok(ResponseVO::success(Some(voc), None))
    }

    pub(crate) async fn get_all_radicals(
        book_id: String
    ) -> Result<ResponseVO<Vec<StrokeRadicalsVo>>, ResponseVO<()>> {
        let book_id = book_id.parse().map_err(|_| ResponseVO::error(Some("invalid bookId".to_string())))?;
        let radicals = RadicalRepository::find_all_by_book_id(book_id).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        Ok(ResponseVO::success(Some(StrokeRadicalsVo::from_radicals(radicals)), None))
    }

    pub(crate) async fn get_letter_sections_by_radical_id(
        radical_id: u64, book_id: String
    ) -> Result<ResponseVO<Vec<LetterRadicalIdSectionVo>>, ResponseVO<()>> {
        let book_id = book_id.parse().map_err(|_| ResponseVO::error(Some("invalid bookId".to_string())))?;
        let radical = RadicalRepository::find_by_id_and_book_id(radical_id, book_id).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?
            .ok_or(ResponseVO::error(Some("请指定正确的部首".to_string())))?;
        
        let mut letters = LetterFilter::filter_letters(book_id, |le| {
            if let Some(ref tmp_r_id) = le.opt_radical_id {
                if *tmp_r_id == radical_id { return true; }
            }
            for tmp_r_id in le.habitual_radical_ids.iter() {
                if *tmp_r_id == radical_id { return true; }
            }
            return false;
        }).await;
        letters.sort_by(|a, b| a.opt_stroke_count.cmp(&b.opt_stroke_count));

        let mut stroke_count_letter_map: IndexMap<Option<u16>, Vec<Letter>> = IndexMap::new();
        for letter in letters {
            stroke_count_letter_map.entry(letter.opt_stroke_count).or_insert(Vec::new()).push(letter);
        }

        let mut sections = vec![];
        for (opt_stroke_count, lets) in stroke_count_letter_map {
            sections.push(LetterRadicalIdSectionVo {
                radical_id,
                remaining_stroke_count: opt_stroke_count.unwrap_or(radical.stroke_count) - radical.stroke_count,
                letters: lets.into_iter().map(|le| LetterPinyinInfoVo {
                    letter_id: le.letter_id,
                    name: le.name.clone(),
                    name_out_base: le.name_out_base.clone(),
                    pinyin_list: le.pinyin_with_tones.clone(),
                }).collect(),
            });
        }
        
        Ok(ResponseVO::success(Some(sections), None))
    }

    pub(crate) async fn get_letters_by_stroke_count(
        stroke_count: u16, book_id: String, start_stroke: Option<String>,
    ) -> Result<ResponseVO<Vec<Letter>>, ResponseVO<()>> {
        if stroke_count <= 0u16 {
            Err(ResponseVO::error(Some("请指定正确的笔画数".to_string())))?
        }
        let book_id = book_id.parse()
            .map_err(|_| ResponseVO::error(Some("invalid bookId".to_string())))?;

        let mut letters = LetterFilter::filter_letters(book_id, |le| {
            let rt1 = if let Some(le_stroke_count) = le.opt_stroke_count {
                le_stroke_count == stroke_count
            } else {
                false
            };
            let mut rt2 = true;
            if let Some(ref ss) = start_stroke {
                if let Some(ref ss1) = le.opt_start_stroke {
                    rt2 = ss1 == ss;
                }
            }
            rt1 && rt2
        }).await;

        letters.sort_by(|a, b| a.opt_start_stroke
            .as_ref()
            .unwrap_or(&String::new()).cmp(
            &b.opt_start_stroke.as_ref().unwrap_or(&String::new())
        ));

        Ok(ResponseVO::success(Some(letters), None))
    }

    pub(crate) async fn get_snapshots(book_id: String) -> Result<ResponseVO<Vec<Snapshot>>, ResponseVO<()>> {
        let book_id = book_id.parse()
            .map_err(|_| ResponseVO::error(Some("invalid bookId".to_string())))?;
        let book = BookRepository::find_by_id(book_id).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?
            .ok_or(ResponseVO::error(Some("找不到书本信息".to_string())))?;
        let snapshots = book.snapshots.into_iter()
            .filter(|s| s.page.is_some_and(|p| p >= 0))
            .collect();
        Ok(ResponseVO::success(Some(snapshots), None))
    }

    pub(crate) async fn search_resources(
        params: SearchResourcesParams
    ) -> Result<ResponseVO<SearchResourceResultVo>, ResponseVO<()>> {
        if params.search_text.trim().is_empty() {
            Err(ResponseVO::error(Some("请输入搜索内容".to_string())))?
        }

        let search_text = params.search_text.trim();

        let book_id: u64 = params.book_id.parse()
            .map_err(|_| ResponseVO::error(Some("invalid bookId".to_string())))?;

        let opt_search_stroke_count = search_text.parse::<u16>()
            .map(|s| Some(s))
            .unwrap_or(None);

        let resp: SearchResourceResultVo;

        // 搜索文本全部是中文字符时
        if all_chinese_characters(search_text) {
            // 用户查询是单字，需显示“推荐、同音、同部首”选项
            if search_text.chars().count() == 1 {
                // 选推荐
                if params.opt_search_type.is_none() || params.opt_search_type.is_some_and(|t| t == 1) {
                    // 单个字逐个查
                    let letters = LetterFilter::filter_letters(
                        book_id, |le| le.name_out_base == search_text
                    ).await;

                    // 词汇用模糊匹配
                    let voc_list = VocabularyFilter::filter_voc_list(
                        book_id, |v| v.name_out_base.contains(search_text)
                    ).await;

                    let mut tip_map = IndexMap::new();
                    let mut letter_items = vec![];
                    for le in letters {
                        letter_items.push(SRRListItemVo {
                            r#type: "LETTER".to_string(),
                            id: le.letter_id,
                            name: le.name,
                            name_out_base: le.name_out_base,
                            py_with_tone: le.pinyin_with_tones,
                            illustration: le.illustration,
                        });
                    }
                    if !letter_items.is_empty() {
                        tip_map.insert(search_text.to_string(), letter_items);
                    }

                    for v in voc_list {
                        let tip = if v.name.starts_with(search_text) {
                            format!("{}*", search_text)
                        } else if v.name.ends_with(search_text) {
                            format!("*{}", search_text)
                        } else { 
                            format!("*{}*", search_text)
                        };
                        tip_map.entry(tip).or_insert(Vec::new())
                            .push(SRRListItemVo {
                                r#type: "VOCABULARY".to_string(),
                                id: v.v_id,
                                name: v.name,
                                name_out_base: v.name_out_base,
                                py_with_tone: vec![v.pinyin_with_tone],
                                illustration: v.illustration,
                            });
                    }

                    let mut data_list = Vec::new();
                    for (tip, items) in tip_map {
                        data_list.push(SRRListVo {
                            tip,
                            items,
                        });
                    }

                    resp = SearchResourceResultVo {
                        show_recommend: true,
                        show_same_pinyin: true,
                        show_same_radical: true,
                        list: data_list,
                    }
                } else if params.opt_search_type.is_some_and(|s| s == 2) {
                    // 选同音
                    let spyl = LetterFilter::filter_letters(
                        book_id, |le| le.name_out_base == search_text
                    ).await;

                    let _same_py_letters = |le: &Letter| {
                        if le.pinyin_with_tones.is_empty() {
                            return false;
                        }
                        for i in spyl.iter() {
                            if i.pinyin_with_tones.is_empty() {
                                continue;
                            }
                            for j in i.pinyin_with_tones.iter() {
                                if le.pinyin_with_tones.contains(&j)  {
                                    return true;
                                }
                            }
                        }
                        false
                    };

                    let letters = LetterFilter::filter_letters(book_id, _same_py_letters).await;

                    let mut tip_map = IndexMap::new();
                    for le in letters {
                        if le.pinyin_with_tones.is_empty() {
                            continue;
                        }
                        for pywt in le.pinyin_with_tones.iter() {
                            let tip = format!("同音：{}", pywt);
                            tip_map.entry(tip).or_insert(Vec::new())
                                .push(SRRListItemVo {
                                    r#type: "LETTER".to_string(),
                                    id: le.letter_id,
                                    name: le.name.clone(),
                                    name_out_base: le.name_out_base.clone(),
                                    py_with_tone: le.pinyin_with_tones.clone(),
                                    illustration: le.illustration.clone(),
                                });
                        }
                    }
                    
                    let mut data_list = Vec::new();
                    for (tip, items) in tip_map {
                        data_list.push(SRRListVo {
                            tip,
                            items,
                        });
                    }

                    resp = SearchResourceResultVo {
                        show_recommend: true,
                        show_same_pinyin: true,
                        show_same_radical: true,
                        list: data_list,
                    }
                } else if params.opt_search_type.is_some_and(|s| s == 3) {
                    // 选同部首
                    let spyl = LetterFilter::filter_letters(
                        book_id, |le| le.name_out_base == search_text
                    ).await;
                    let spyl = spyl.first()
                        .map(|le| Some(le.clone()))
                        .unwrap_or(None);

                    let _same_radical_letters = |le: &Letter| {
                        if le.opt_radical.is_none() && le.habitual_radicals.is_empty() {
                            return false;
                        }
                        if le.opt_stroke_count.is_some_and(|sc| sc <= 0) {
                            return false;
                        }
                        if let Some(ref tmp_spyl) = spyl {
                            if tmp_spyl.opt_radical_id == le.opt_radical_id {
                                return true;
                            }
                            if !tmp_spyl.habitual_radical_ids.is_empty() && !le.habitual_radical_ids.is_empty() {
                                for hrc_id in tmp_spyl.habitual_radical_ids.iter() {
                                    if le.habitual_radical_ids.contains(&hrc_id) {
                                        return true;
                                    }
                                }
                            }
                        }
                        return false;
                    };

                    let mut letters = LetterFilter::filter_letters(
                        book_id, _same_radical_letters
                    ).await;
                    letters.sort_by(|a, b| a.opt_stroke_count.cmp(&b.opt_stroke_count));

                    let mut tip_map = IndexMap::new();
                    let mut data_list = Vec::new();
                    if let Some(ref tmp) = spyl {
                        if let Some(ref radical_id) = tmp.opt_radical_id {
                            let opt_rad = RadicalRepository::find_by_id(*radical_id).await
                                .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
                            if let Some(rad) = opt_rad {
                                for le in letters {
                                    let tip = format!(
                                        "部首：{} 剩余: {}画",
                                        rad.name, le.opt_stroke_count.unwrap_or(rad.stroke_count) - rad.stroke_count
                                    );
                                    tip_map.entry(tip).or_insert(Vec::new())
                                        .push(SRRListItemVo {
                                            r#type: "LETTER".to_string(),
                                            id: le.letter_id,
                                            name: le.name,
                                            name_out_base: le.name_out_base,
                                            py_with_tone: le.pinyin_with_tones,
                                            illustration: le.illustration,
                                        });
                                }
                            }
                            for (tip, items) in tip_map {
                                data_list.push(SRRListVo {
                                    tip,
                                    items,
                                });
                            }
                        }
                    }
                    
                    resp = SearchResourceResultVo {
                        show_recommend: true,
                        show_same_pinyin: true,
                        show_same_radical: true,
                        list: data_list,
                    }
                } else {
                    return Err(ResponseVO::error(Some("无效的搜索类型".to_string())));
                }
            } else {
                // 查询多个字，如果查询词汇没结果，就拆分成单字分别查询，并且只显示“推荐”选项
                let vo_list = VocabularyFilter::filter_voc_list(
                    book_id, |v| v.name_out_base == search_text
                ).await;

                let mut data_list = Vec::new();
                if !vo_list.is_empty() {
                    data_list.push(SRRListVo {
                        tip: "".to_string(),
                        items: vo_list.into_iter().map(|v| SRRListItemVo {
                            r#type: "VOCABULARY".to_string(),
                            id: v.v_id,
                            name: v.name,
                            name_out_base: v.name_out_base,
                            py_with_tone: vec![v.pinyin_with_tone],
                            illustration: v.illustration,
                        }).collect(),
                    });
                } else {
                    let sls = search_text.chars()
                        .map(|c| c.to_string())
                        .collect::<HashSet<String>>();
                    let letters = LetterFilter::filter_letters(
                        book_id, |le| sls.contains(le.name_out_base.as_str())
                    ).await;
                    
                    let mut tip_map = IndexMap::new();
                    for le in letters {
                        if !le.pinyin_with_tones.is_empty() {
                            let tip = le.name.clone();
                            tip_map.entry(tip).or_insert(Vec::new())
                                .push(SRRListItemVo {
                                    r#type: "LETTER".to_string(),
                                    id: le.letter_id,
                                    name: le.name,
                                    name_out_base: le.name_out_base,
                                    py_with_tone: le.pinyin_with_tones,
                                    illustration: le.illustration,
                                });
                        }
                    }
                    for (tip, items) in tip_map {
                        data_list.push(SRRListVo {
                            tip,
                            items,
                        });
                    }
                }
                
                resp = SearchResourceResultVo {
                    show_recommend: true,
                    show_same_pinyin: false,
                    show_same_radical: false,
                    list: data_list,
                }
            }

        } else if opt_search_stroke_count.is_some_and(|s| s > 0) {
            // 此时按笔画数搜索单字，无需显示“推荐、同音、同部首”选项
            let search_stroke_count = opt_search_stroke_count.unwrap();
            let letters = LetterFilter::filter_letters(
                book_id, |le| le.opt_stroke_count.is_some_and(|sc| sc == search_stroke_count)
            ).await;

            let items = letters.into_iter().map(|le| SRRListItemVo {
                r#type: "LETTER".to_string(),
                id: le.letter_id,
                name: le.name,
                name_out_base: le.name_out_base,
                py_with_tone: le.pinyin_with_tones,
                illustration: le.illustration,
            }).collect::<Vec<SRRListItemVo>>();
            
            resp = SearchResourceResultVo {
                show_recommend: false,
                show_same_pinyin: false,
                show_same_radical: false,
                list: vec![
                    SRRListVo {
                        tip: format!("同笔画数: {}", search_stroke_count),
                        items,
                    }
                ],
            };

        } else {
            // 此时认为是搜拼音，无需显示“推荐、同音、同部首”选项
            let _letter_filter_fun = |le: &Letter| {
                let st = search_text.to_string();
                return (!le.pinyin_with_tones.is_empty() && le.pinyin_with_tones.contains(&st))
                        || le.opt_pinyin_without_tones.as_ref().is_some_and(|pwt| !pwt.is_empty() && pwt.contains(&st))
            };

            let letters = LetterFilter::filter_letters(
                book_id, _letter_filter_fun
            ).await;

            let _voc_filter_fun = |v: &Vocabulary| {
                return v.pinyin_with_tone == search_text || v.opt_pinyin_without_tone.as_ref().is_some_and(|pwt| pwt == search_text)
            };

            let vo_list = VocabularyFilter::filter_voc_list(book_id, _voc_filter_fun).await;

            let mut tip_map = IndexMap::new();
            for le in letters {
                for pywt in le.pinyin_with_tones.iter() {
                    tip_map.entry(pywt.clone()).or_insert(Vec::new())
                        .push(SRRListItemVo {
                            r#type: "LETTER".to_string(),
                            id: le.letter_id,
                            name: le.name.clone(),
                            name_out_base: le.name_out_base.clone(),
                            py_with_tone: le.pinyin_with_tones.clone(),
                            illustration: le.illustration.clone(),
                        });
                }
            }

            for v in vo_list {
                if !v.pinyin_with_tone.is_empty() {
                    tip_map.entry(v.pinyin_with_tone.clone()).or_insert(Vec::new())
                        .push(SRRListItemVo {
                            r#type: "VOCABULARY".to_string(),
                            id: v.v_id,
                            name: v.name,
                            name_out_base: v.name_out_base,
                            py_with_tone: vec![v.pinyin_with_tone],
                            illustration: v.illustration,
                        });
                }
            }

            let mut same_py_list = tip_map.into_iter()
                .map(|(k, v)| (k.to_string(), v))
                .collect::<Vec<(String, Vec<SRRListItemVo>)>>();
            same_py_list.sort_by(|a, b| a.0.cmp(&b.0));

            let data_list = same_py_list.into_iter()
                .map(|(py, items)| SRRListVo {
                    tip: format!("同音: {}", py),
                    items,
                })
                .collect::<Vec<SRRListVo>>();

            resp = SearchResourceResultVo {
                show_recommend: false,
                show_same_pinyin: true,
                show_same_radical: false,
                list: data_list,
            };
        }

        Ok(ResponseVO::success(Some(resp), None))
    }

}
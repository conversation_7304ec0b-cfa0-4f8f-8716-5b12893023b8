use std::collections::{HashMap, HashSet};
use std::fs;
use std::fs::File;
use std::io::Read;
use std::path::{Path, PathBuf};
use indexmap::IndexMap;
use lazy_static::lazy_static;
use tauri::Emitter;
use tokio::sync::{mpsc, RwLock};
use tokio::sync::mpsc::{Receiver, Sender};
use zip::ZipArchive;
use crate::controller::websocket::send_to_broadcast_ws;
use crate::database::assemble::AssembleRepository;
use crate::database::book::BookRepository;
use crate::database::card::CardRepository;
use crate::database::catalogue::CatalogueRepository;
use crate::database::increment_record::IncrementRecordRepository;
use crate::database::law::LawRepository;
use crate::database::letter::LetterRepository;
use crate::database::maps::MapRepository;
use crate::database::pinyin::PinyinRepository;
use crate::database::radical::RadicalRepository;
use crate::database::vocabulary::VocabularyRepository;
use crate::search::book::training_book;
use crate::search::card::training_card;
use crate::models::assemble::Assemble;
use crate::models::book::Book;
use crate::models::card::Card;
use crate::models::catalogue::Catalogue;
use crate::models::increment_record::IncrementRecord;
use crate::models::law::Law;
use crate::models::letter::Letter;
use crate::models::maps::Maps;
use crate::models::pinyin::Pinyin;
use crate::models::radical::Radical;
use crate::models::ResponseVO;
use crate::models::vocabulary::Vocabulary;
use crate::services::filter_data::book_filter::BookFilter;
use crate::services::filter_data::letter_filter::LetterFilter;
use crate::services::filter_data::vocabulary_filter::VocabularyFilter;
use crate::utils::event_emitter::emit_event;
use crate::utils::path::{get_book_summary_file_path, get_static_dir};
use crate::utils::time_util::now_datetime_local;
use crate::vo::increment::IncrementResultVo;

lazy_static!{
    static ref INC_RCD_TX: RwLock<Option<Sender<IncrementRecord>>> = RwLock::new(None);
    static ref INC_RCD_RX: RwLock<Option<Receiver<IncrementRecord>>> = RwLock::new(None);
}

pub async fn init_increment_record_channel(capacity: usize) {
    {
        let (tx, rx) = mpsc::channel(capacity);
        let mut opt_tx = INC_RCD_TX.write().await;
        let mut opt_rx = INC_RCD_RX.write().await;
        *opt_tx = Some(tx);
        *opt_rx = Some(rx);
    }

    tokio::spawn(increment_record_consumer());
}


async fn increment_record_producer(rcd: IncrementRecord) -> Result<(), Box<dyn std::error::Error>> {
    let guard_tx = INC_RCD_TX.read().await;
    let tx = guard_tx.as_ref().unwrap();
    tx.send(rcd).await?;
    Ok(())
}
async fn increment_record_consumer() {
    let mut rx = INC_RCD_RX.write().await.take().unwrap();
    while let Some(mut rcd) = rx.recv().await {
        println!("Consumed: {:#?}", rcd);
        // 改为导入中状态
        rcd.status = 1;
        rcd.update_time = now_datetime_local();
        let _ = IncrementRecordRepository::save(&rcd).await;

        // 通知导入状态发生变化
        notify_increment_result_changed().await;

        // 执行导入操作
        let result = IncrementService::import_increment(rcd.file_path.clone())
            .await.map_err(|e| e.to_string());

        // 不论是否导入成功，当要求删掉源文件就果断删除
        if rcd.delete_file {
            // 不管删除是否成功
            let _ = fs::remove_file(&rcd.file_path);
        }

        if result.is_ok() {
            rcd.status = 2;
        } else if let Err(e) = result {
            rcd.status = 3;
            rcd.opt_msg = Some(e);
        }
        rcd.update_time = now_datetime_local();
        rcd.opt_handled_time = Some(now_datetime_local());

        // 保存导入结果
        let _ = IncrementRecordRepository::save(&rcd).await;

        // 通知导入状态发生变化
        notify_increment_result_changed().await;
    }
}

async fn notify_increment_result_changed() {
    let event = "increment-result-changed";
    // Emit event using our custom event emitter
    let _ = emit_event(event, serde_json::Value::String("".to_string()));

    // websocket广播消息
    let _ = send_to_broadcast_ws(event, "").await
        .map_err(|e| e.to_string());
}

pub struct IncrementService { }

impl IncrementService {
    pub async fn get_increment_result(

    ) -> Result<ResponseVO<IncrementResultVo>, ResponseVO<()>> {
        let list = IncrementRecordRepository::find_all_order_by_update_time_desc().await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        let total = list.len();
        let handled = list.iter()
            .filter(|r| r.status == 2 || r.status == 3)
            .count();
        Ok(ResponseVO::success(Some(IncrementResultVo {
            total,
            handled,
            list,
        }), None))
    }

    /// @Param delete_file: 导入处理完结后是否删除原文件
    pub async fn import_increments_queue(
        file_paths: Vec<String>,
        delete_file: bool,
    ) -> Result<ResponseVO<()>, ResponseVO<()>> {
        let mut records = vec![];
        for p in file_paths.iter() {
            let rcd = IncrementRecord::new(PathBuf::from(p), delete_file)
                .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
            records.push(rcd);
        }

        IncrementRecordRepository::save_all(&records).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;

        notify_increment_result_changed().await;

        for rcd in records {
            increment_record_producer(rcd).await
                .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        }

        Ok(ResponseVO::success(None, None))
    }

    async fn import_increment(
        file_path: PathBuf
    ) -> Result<(), Box<dyn std::error::Error>> {
        Bulking::create_db_indexes().await?;
        let bs_set_file_path = get_book_summary_file_path()?;
        let static_dir = get_static_dir()?;
        let mut bulking = Bulking::new(file_path, None)?;
        bulking.parse_json(bs_set_file_path.as_path()).await?;
        bulking.count_statistic().await?;
        bulking.parse_image(static_dir.as_path()).await?;
        bulking.parse_feats().await?;
        bulking.close();
        Ok(())
    }

    /// 用于软件初始化时，将数据库中所有状态为正在导入和未导入的记录标记为异常
    pub async fn cancel_all_increments_importing() -> Result<(), Box<dyn std::error::Error>> {
        // 检测并创建必要的索引
        IncrementRecordRepository::create_indexes().await?;

        let statuses = vec![0, 1];
        let mut records = IncrementRecordRepository::find_all_by_status_in(&statuses).await?;
        for rcd in records.iter_mut() {
            let now = now_datetime_local();
            rcd.update_time = now.clone();
            rcd.opt_handled_time = Some(now);
            rcd.status = 3;
            rcd.opt_msg = Some("应用被意外关闭，请重新导入".to_string());
        }
        if !records.is_empty() {
            IncrementRecordRepository::save_all(&records).await?;
        }
        Ok(())
    }

    pub async fn unfinished_increment_count() -> usize {
        let statuses = vec![0, 1];
        IncrementRecordRepository::count_by_status_in(&statuses).await
            .map_err(|e| e.to_string())
            .unwrap_or(0)
    }

    pub async fn delete_record_by_id(r_id: String) -> Result<ResponseVO<()>, ResponseVO<()>> {
        IncrementRecordRepository::delete_by_id(r_id).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        notify_increment_result_changed().await;
        Ok(ResponseVO::success(None::<()>, None))
    }
}

struct JsonPath {
    f_n_assemble: &'static str,
    f_n_book: &'static str,
    f_n_catalogue: &'static str,
    f_n_card: &'static str,
    f_n_letter: &'static str,
    f_n_pinyin: &'static str,
    f_n_radical: &'static str,
    f_n_vocabulary: &'static str,
    f_n_map: &'static str,
    f_n_law: &'static str,
    f_n_si_ku_quan_shu: &'static str,
}

struct Bulking {
    archive: ZipArchive<File>,
    assembles: Vec<Assemble>,
    books: Vec<Book>,
    cards: Vec<Card>,
    json_path: JsonPath,
    #[allow(dead_code)]
    opt_batch: Option<i32>,
}

impl Bulking {
    pub fn new(
        zip_path: PathBuf, opt_batch: Option<i32>
    ) -> Result<Self, Box<dyn std::error::Error>> {
        // 读取模板文件到内存
        let file = File::open(zip_path)?;
        let archive = ZipArchive::new(file)?;

        Ok(Self {
            archive,
            assembles: vec![],
            books: vec![],
            cards: vec![],
            json_path: JsonPath {
                f_n_assemble: "cms_data/assembles.json",
                f_n_book: "cms_data/books.json",
                f_n_catalogue: "cms_data/catalogues.json",
                f_n_card: "cms_data/cards.json",
                f_n_letter: "cms_data/letters.json",
                f_n_pinyin: "cms_data/pinyinList.json",
                f_n_radical: "cms_data/radicals.json",
                f_n_vocabulary: "cms_data/vocabularies.json",
                f_n_map: "cms_data/maps.json",
                f_n_law: "cms_data/lawList.json",
                f_n_si_ku_quan_shu: "cms_data/siKuQuanShu.json",
            },
            opt_batch,
        })
    }

    pub fn close(self) {
        self.archive;
    }

    async fn create_db_indexes() -> Result<(), Box<dyn std::error::Error>> {
        // 创建所有实体的索引
        AssembleRepository::create_indexes().await?;
        BookRepository::create_indexes().await?;
        CatalogueRepository::create_indexes().await?;
        CardRepository::create_indexes().await?;
        LetterRepository::create_indexes().await?;
        PinyinRepository::create_indexes().await?;
        RadicalRepository::create_indexes().await?;
        VocabularyRepository::create_indexes().await?;
        MapRepository::create_indexes().await?;
        LawRepository::create_indexes().await?;

        // 创建查重相关表的索引
        use crate::database::plagiarism_batch::PlagiarismBatchRepository;
        use crate::database::sentence_match::SentenceMatchRepository;
        PlagiarismBatchRepository::create_indexes().await?;
        SentenceMatchRepository::create_indexes().await?;

        Ok(())
    }

    fn get_entity_cfg_map(&self) -> IndexMap<&'static str, (bool, Option<String>)> {
        let jp = &self.json_path;
        let f_n_set: HashSet<String> = self.archive.file_names()
            .filter(|s| s.starts_with("cms_data/"))
            .map(|s| s.to_string())
            .collect();

        let map_result = f_n_set.contains(jp.f_n_map);
        let law_result = f_n_set.contains(jp.f_n_law);
        // 如果是地图册或法律法规
        if map_result || law_result {
            return IndexMap::from([
                (jp.f_n_assemble, (false, None)), (jp.f_n_book, (false, None)),
                (jp.f_n_catalogue, (false, None)), (jp.f_n_card, (false, None)),
                (jp.f_n_map, (false, None)), (jp.f_n_law, (false, None)),
            ]);
        }

        let letter_result = f_n_set.contains(jp.f_n_letter);
        // 如果是字词典
        if letter_result {
            return IndexMap::from([
                (jp.f_n_assemble, (true, None)), (jp.f_n_book, (true, None)),
                (jp.f_n_catalogue, (true, None)), (jp.f_n_card, (true, None)),
                (jp.f_n_letter, (true, None)), (jp.f_n_pinyin, (true, None)),
                (jp.f_n_radical, (true, None)), (jp.f_n_vocabulary, (false, None)),
            ]);
        }

        let si_ku_result = f_n_set.contains(jp.f_n_si_ku_quan_shu);
        // 如果是四库全书
        if si_ku_result {
            return IndexMap::from([]);
        }

        // 否则就是普通书本
        IndexMap::from([
            (jp.f_n_assemble, (true, None)), (jp.f_n_book, (true, None)),
            (jp.f_n_catalogue, (true, None)), (jp.f_n_card, (true, None)),
        ])
    }

    async fn parse_json(&mut self, bs_set_file_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
        let mut entity_cfg_map: IndexMap<&str, (bool, Option<String>)> = self.get_entity_cfg_map();
        for (file_name, (required, opt_json_str)) in entity_cfg_map.iter_mut() {
            let result = self.archive.by_name(*file_name);
            if result.is_ok() {
                let mut file = result.unwrap();
                // 读取文件内容
                let mut file_content = Vec::new();
                file.read_to_end(&mut file_content)?;
                let file_content = String::from_utf8_lossy(&file_content).into_owned();
                *opt_json_str = Some(file_content);
            } else if *required {
                result?;
            }
        }

        let jp = &self.json_path;
        for (file_name, (required, opt_json_str)) in entity_cfg_map {
            if let Some(json_str) = opt_json_str {
                if file_name == jp.f_n_assemble {
                    let assembles = Assemble::from_v1(&json_str)?;
                    AssembleRepository::save_all_merged_book_ids(&assembles).await?;
                    self.assembles = assembles;
                } else if file_name == jp.f_n_book {
                    let books = Book::from_v1(&json_str)?;
                    BookRepository::save_all(&books).await?;
                    // 生成书本参数过滤集合数据
                    BookFilter::add_books(bs_set_file_path, &books).await?;
                    self.books = books;
                } else if file_name == jp.f_n_catalogue {
                    let catalogues = Catalogue::from_v1(&json_str)?;
                    CatalogueRepository::save_all(&catalogues).await?;
                } else if file_name == jp.f_n_card {
                    let cards = Card::from_v1(&json_str)?;
                    CardRepository::save_all(&cards).await?;
                    self.cards = cards;
                } else if file_name == jp.f_n_letter {
                    let letters = Letter::from_v1(&json_str)?;
                    LetterRepository::save_all(&letters).await?;
                    // 更新汉字过滤器实例
                    LetterFilter::update(&letters).await?;
                } else if file_name == jp.f_n_pinyin {
                    let pinyin_list = Pinyin::from_v1(&json_str)?;
                    PinyinRepository::save_all(&pinyin_list).await?;
                } else if file_name == jp.f_n_radical {
                    let radicals = Radical::from_v1(&json_str)?;
                    RadicalRepository::save_all(&radicals).await?;
                } else if file_name == jp.f_n_vocabulary {
                    let vocabularies = Vocabulary::from_v1(&json_str)?;
                    VocabularyRepository::save_all(&vocabularies).await?;
                    // 更新词语过滤器实例
                    VocabularyFilter::update(&vocabularies).await?;
                } else if file_name == jp.f_n_map {
                    let maps = Maps::from_v1(&json_str)?;
                    MapRepository::save_all(&maps).await?;
                } else if file_name == jp.f_n_law {
                    let law_list = Law::from_v1(&json_str)?;
                    LawRepository::save_all(&law_list).await?;
                } else {
                    return Err(Box::from(anyhow::Error::msg("找不到实体")));
                }
            } else if required {
                return Err(Box::from(anyhow::Error::msg("找不到实体")));
            }
        }

        Ok(())
    }

    async fn parse_image(&mut self, static_dir: &Path) -> Result<(), Box<dyn std::error::Error>> {
        for file_idx in 0..self.archive.len() {
            let mut file = self.archive.by_index(file_idx)?;
            if file.is_file() && file.name().starts_with("cms_illustration_image") {
                // 读取文件内容
                let mut file_content = Vec::new();
                file.read_to_end(&mut file_content)?;
                let dist_path = static_dir.join(file.name());
                let dist_dir = dist_path.parent().ok_or("找不到父文件夹路径")?;
                if !dist_dir.exists() {
                    fs::create_dir_all(dist_dir)?;
                }
                fs::write(dist_path, file_content)?;
            }
        }
        Ok(())
    }

    async fn parse_feats(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let mut book_map = HashMap::new();
        for book in self.books.iter() {
            book_map.insert(book.book_id, book.clone());
        }
        let book_ids: Vec<u64> = self.books.iter().map(|b| b.book_id).collect();
        let mut book_id_assembles_map: HashMap<u64, Vec<&Assemble>> = HashMap::new();
        let assembles = AssembleRepository::find_all_by_book_id_in(&book_ids).await?;
        for asm in assembles.iter() {
            for bid in asm.books.iter() {
                if book_map.contains_key(bid) {
                    book_id_assembles_map.entry(*bid).or_insert(Vec::new()).push(asm);
                }
            }
        }
        training_book(&self.books, &book_id_assembles_map).await?;
        training_card(&self.cards, &book_map, &book_id_assembles_map).await?;
        Ok(())
    }

    async fn count_statistic(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        for temp_assemble in self.assembles.iter() {
            let mut opt_assemble = AssembleRepository::find_by_id(temp_assemble.assemble_id).await?;
            if let Some(assemble) = opt_assemble.as_mut() {
                let book_ids = &assemble.books;
                let opt_book = BookRepository::find_by_book_id_in_order_by_publish_date(
                    book_ids, true
                ).await?;
                if let Some(book) = opt_book {
                    assemble.earliest_publish_date = book.publish_date.clone();
                }
                let opt_book = BookRepository::find_by_book_id_in_order_by_publish_date(
                    book_ids, false
                ).await?;
                if let Some(book) = opt_book {
                    assemble.recent_publish_date = book.publish_date.clone();
                }
                AssembleRepository::save(assemble).await?;
            }
        }
        Ok(())
    }

}
use crate::database::{maps};
use crate::models::maps::{GetMaps, GetMapsBreadCrumb, GetMapsFilters, QueryMaps, QueryMapsFilter};
use crate::models::ResponseVO;

pub async fn get_maps(
    params: QueryMaps
) -> Result<ResponseVO<GetMaps>, ResponseVO<()>> {
    match maps::get_maps(params).await {
        Ok(result) => Ok(ResponseVO::success(Some(result), None)),
        Err(error) => Err(ResponseVO::error(Some(error.to_string()))),
    }
}
pub async fn get_maps_bread_crumb() -> Result<ResponseVO<Vec<GetMapsBreadCrumb>>, ResponseVO<()>> {
    match maps::get_maps_bread_crumb().await {
        Ok(list) => Ok(ResponseVO::success(Some(list), None)),
        Err(error) => Err(ResponseVO::error(Some(error.to_string()))),
    }
}
pub async fn get_maps_filters(
    query: QueryMapsFilter
) -> Result<ResponseVO<GetMapsFilters>, ResponseVO<()>> {
    match maps::get_maps_filters(query).await {
        Ok(result) => Ok(ResponseVO::success(Some(result), None)),
        Err(error) => Err(ResponseVO::error(Some(error.to_string()))),
    }
}
pub async fn get_maps_total() -> Result<ResponseVO<i32>, ResponseVO<()>> {
    match maps::get_maps_total().await {
        Ok(result) => Ok(ResponseVO::success(Some(result), None)),
        Err(error) => Err(ResponseVO::error(Some(error.to_string()))),
    }
}
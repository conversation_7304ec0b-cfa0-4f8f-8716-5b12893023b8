use std::io::SeekFrom;
use std::path::PathBuf;
use lazy_static::lazy_static;
use reqwest::Client;
use tauri::Emitter;
use tokio::fs::File;
use tokio::io::{AsyncReadExt, AsyncSeekExt};
use tokio::sync::mpsc::{Receiver, Sender};
use tokio::sync::{mpsc, RwLock};
use crate::controller::file_upload::FileMergingResult;
use crate::controller::increment::ImportIncQueueParam;
use crate::database::inc_file_upd_record::IncFileUpdRecordRepository;
use crate::models::inc_file_upd_record::IncFileUpdRecord;
use crate::models::ResponseVO;
use crate::utils::app_handle::get_app_handle;
use crate::utils::time_util::now_datetime_local;

lazy_static! {
    static ref UPD_RECORD_TX: RwLock<Option<Sender<IncFileUpdRecord>>> = RwLock::new(None);
    static ref UPD_RECORD_RX: RwLock<Option<Receiver<IncFileUpdRecord>>> = RwLock::new(None);
    static ref REMOTE_URL: RwLock<Option<String>> = RwLock::new(None);
}


pub async fn init_inc_file_upd_channel(capacity: usize) {
    {
        let (tx, rx) = mpsc::channel(capacity);
        let mut opt_tx = UPD_RECORD_TX.write().await;
        let mut opt_rx = UPD_RECORD_RX.write().await;
        *opt_tx = Some(tx);
        *opt_rx = Some(rx);
    }

    tokio::spawn(upload_record_consumer());
}

async fn upload_record_producer(rcd: IncFileUpdRecord) -> Result<(), Box<dyn std::error::Error>> {
    let guard_tx = UPD_RECORD_TX.read().await;
    let tx = guard_tx.as_ref().unwrap();
    tx.send(rcd).await?;
    Ok(())
}

async fn upload_record_consumer() {
    let mut rx = UPD_RECORD_RX.write().await.take().unwrap();
    while let Some(mut rcd) = rx.recv().await {
        println!("Consumed: {:#?}", rcd);

        // 执行上传操作
        IncFileUpdService::upload_one_file(&mut rcd).await;
    }
}

pub struct IncFileUpdService;


impl IncFileUpdService {
    pub async fn get_inc_file_upd_records(

    ) -> Result<ResponseVO<Vec<IncFileUpdRecord>>, ResponseVO<()>> {
        let records = IncFileUpdRecordRepository::find_all_order_by_update_time_desc().await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        Ok(ResponseVO::success(Some(records), None))
    }

    pub async fn upload_files_to_remote(
        remote_url: String, file_paths: Vec<String>
    ) -> Result<ResponseVO<()>, ResponseVO<()>> {
        {
            let mut opt_remote_url = REMOTE_URL.write().await;
            *opt_remote_url = Some(remote_url);
        }

        let mut records = vec![];
        for p in file_paths.iter() {
            let rcd = IncFileUpdRecord::new(PathBuf::from(p))
                .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
            records.push(rcd);
        }

        IncFileUpdRecordRepository::save_all(&records).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;

        for rcd in records {
            upload_record_producer(rcd).await
                .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        }

        Ok(ResponseVO::success(None, None))
    }

    /// 用于软件初始化时，将数据库中所有状态为正在上传和未上传的记录标记为异常
    pub async fn cancel_all_files_uploading() -> Result<(), Box<dyn std::error::Error>> {
        // 检测并创建必要的索引
        IncFileUpdRecordRepository::create_indexes().await?;

        let statuses = vec![0, 1];
        let mut records = IncFileUpdRecordRepository::find_all_by_status_in(&statuses).await?;
        for rcd in records.iter_mut() {
            let now = now_datetime_local();
            rcd.update_time = now.clone();
            rcd.opt_handled_time = Some(now);
            rcd.status = 3;
            rcd.opt_msg = Some("应用意外被关闭，请重新导入".to_string());
        }
        if !records.is_empty() {
            IncFileUpdRecordRepository::save_all(&records).await?;
        }
        Ok(())
    }

    pub async fn unfinished_uploading_count() -> usize {
        let statuses = vec![0, 1];
        IncFileUpdRecordRepository::count_by_status_in(&statuses).await
            .map_err(|e| e.to_string())
            .unwrap_or(0)
    }

    async fn upload_one_file(rcd: &mut IncFileUpdRecord) {
        let opt_remote_url = {REMOTE_URL.read().await.clone()};
        if opt_remote_url.is_none() { return; }
        let url = opt_remote_url.unwrap();

        async fn notify() {
            let result = get_app_handle();
            if result.is_err() { return; }
            let app_handle = result.unwrap();
            let _ = app_handle.emit("inc-file-upd-record-changed", "");
        }

        let chunk_size = 2 * 1024 * 1024;
        let retry_count = 5;

        rcd.update_time = now_datetime_local();
        rcd.status = 1;

        let _ = IncFileUpdRecordRepository::save(rcd).await;
        // 通知状态发生变化
        notify().await;

        let result = File::open(&rcd.local_file_path).await;
        if let Err(e) = result {
            rcd.update_time = now_datetime_local();
            rcd.status = 3;
            rcd.opt_msg = Some(e.to_string());

            let _ = IncFileUpdRecordRepository::save(rcd).await;
            // 通知状态发生变化
            notify().await;
            return;
        }

        let mut file = result.unwrap();

        // 获取文件大小
        let result = file.metadata().await;
        if let Err(e) = result {
            rcd.update_time = now_datetime_local();
            rcd.status = 3;
            rcd.opt_msg = Some(e.to_string());

            let _ = IncFileUpdRecordRepository::save(rcd).await;
            // 通知状态发生变化
            notify().await;
            return;
        }
        let metadata = result.unwrap();
        let file_size = metadata.len();

        // 分片总数
        let num_chunks = (file_size as f64 / chunk_size as f64).ceil() as usize;

        // 初始化 HTTP 客户端
        let client = Client::new();

        // 分片上传
        for chunk_index in 0..num_chunks {
            let mut buffer = vec![0u8; chunk_size];

            // 将文件游标移动到当前分片的开始位置
            let result = file.seek(SeekFrom::Start((chunk_index * chunk_size) as u64)).await;
            if let Err(e) = result {
                rcd.update_time = now_datetime_local();
                rcd.status = 3;
                rcd.opt_msg = Some(e.to_string());
                break;
            }

            let result = file.read(&mut buffer).await;
            if let Err(e) = result {
                rcd.update_time = now_datetime_local();
                rcd.status = 3;
                rcd.opt_msg = Some(e.to_string());
                break;
            }
            let bytes_read = result.unwrap();

            // 截取有效的分片数据
            buffer.truncate(bytes_read);

            // 构建请求 URL，并将当前分片的索引和总分片数作为查询参数传递
            let upd_url = format!(
                "{}/api/upload/chunk?identifier={}&chunkIndex={}",
                url,
                rcd.r_id,
                chunk_index
            );

            let mut chunk_upd_flag = false;
            for t in 0..retry_count {
                // 发送分片数据
                let request = client
                    .post(&upd_url)
                    .body(buffer.clone());  // 直接将分片数据放在请求体中

                let result = request.send().await;
                if let Err(e) = result {
                    // 如果不是最后一次重试上传失败，就继续
                    if t < retry_count - 1 {
                        continue;
                    } else {
                        // 但如果是最后一次上传失败，那这整个文件上传就失败了
                        rcd.update_time = now_datetime_local();
                        rcd.status = 3;
                        rcd.opt_msg = Some(e.to_string());
                        break;
                    }
                }
                let res = result.unwrap();
                if res.status().is_success() {
                    chunk_upd_flag = true;
                    break;
                } else {
                    // 如果不是最后一次重试上传失败，就继续
                    if t < retry_count - 1 {
                        continue;
                    } else {
                        // 但如果是最后一次上传失败，那这整个文件上传就失败了
                        rcd.update_time = now_datetime_local();
                        rcd.status = 3;
                        rcd.opt_msg = Some("分片上传失败".to_string());
                        println!("分片 {} 上传失败，状态码: {}", chunk_index, res.status());
                        break;
                    }
                }
            }

            if chunk_upd_flag {
                rcd.upd_percentage = chunk_index as f32 / num_chunks as f32 * 100.0;

                // 每隔60个分片通知一次
                if chunk_index % 60 == 0 {
                    rcd.update_time = now_datetime_local();
                    let _ = IncFileUpdRecordRepository::save(rcd).await;
                    // 通知状态发生变化
                    notify().await;
                }

                println!("分片 {} 上传成功", chunk_index);
            } else {
                break;
            }
        }

        let _ = IncFileUpdRecordRepository::save(rcd).await;
        // 通知状态发生变化
        notify().await;

        // 如果上传失败就不用后续流程了
        if rcd.status == 3 {
            return;
        }

        // 开始合并分片
        let merge_url = format!(
            "{}/api/upload/chunks-merge?identifier={}&fileName={}&numChunks={}",
            url,
            rcd.r_id,
            rcd.file_name,
            num_chunks
        );

        let result = client
            .post(&merge_url)
            .send()
            .await;

        if let Err(e) = result {
            rcd.update_time = now_datetime_local();
            rcd.status = 3;
            rcd.opt_msg = Some(e.to_string());

            let _ = IncFileUpdRecordRepository::save(rcd).await;
            // 通知状态发生变化
            notify().await;
            return;
        }

        let res = result.unwrap();
        if !res.status().is_success() {
            println!("分片合并失败，状态码: {}", res.status());
            rcd.update_time = now_datetime_local();
            rcd.status = 3;
            rcd.opt_msg = Some("分片合并失败".to_string());

            let _ = IncFileUpdRecordRepository::save(rcd).await;
            // 通知状态发生变化
            notify().await;
            return;
        }

        let result = res.json::<FileMergingResult>().await;
        if let Err(e) = result {
            rcd.status = 3;
            rcd.opt_msg = Some(e.to_string());

            let _ = IncFileUpdRecordRepository::save(rcd).await;
            // 通知状态发生变化
            notify().await;
            return;
        }
        let merge_rst = result.unwrap();

        println!("分片合并成功");
        rcd.update_time = now_datetime_local();
        rcd.opt_handled_time = Some(now_datetime_local());
        rcd.opt_remote_file_path = Some(merge_rst.remote_file_path.clone());
        rcd.upd_percentage = 100.0;
        rcd.status = 2;

        let _ = IncFileUpdRecordRepository::save(rcd).await;
        // 通知状态发生变化
        notify().await;

        // 请求远程服务器导入增量包
        let import_inc_remote_url = format!(
            "{}/api/increment/import-increments-queue",
            url
        );

        let payload = ImportIncQueueParam {
            file_paths: vec![merge_rst.remote_file_path],
            opt_delete_files: Some(true),
        };

        let _ = client
            .post(&import_inc_remote_url)
            .json(&payload)// 直接将分片数据放在请求体中
            .send()
            .await;
    }
}
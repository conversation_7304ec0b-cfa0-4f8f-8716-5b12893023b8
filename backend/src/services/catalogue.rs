use crate::database::catalogue;
use crate::database::catalogue::CatalogueRepository;
use crate::models::catalogue::{Catalogue, NewspaperNode};
use crate::models::ResponseVO;

pub async fn get_newspaper_catalogue_by_book_id(
    book_id: String
) -> Result<ResponseVO<Vec<NewspaperNode>>, ResponseVO<()>> {
    match catalogue::get_newspaper_catalogue_by_book_id(book_id).await {
        Ok(list) => Ok(ResponseVO::success(Some(list), None)),
        Err(error) => Err(ResponseVO::error(Some(error.to_string()))),
    }
}
pub async fn get_catalogues_by_book_id(
    book_id: String
) -> Result<ResponseVO<Vec<Catalogue>>, ResponseVO<()>> {
    // let book_id: u64 = book_id.parse().map_err(|_| "Invalid book ID")?;
    match CatalogueRepository::find_all_by_book_id(book_id).await {
        Ok(list) => Ok(ResponseVO::success(Some(list), None)),
        Err(error) => Err(ResponseVO::error(Some(error.to_string()))),
    }
}
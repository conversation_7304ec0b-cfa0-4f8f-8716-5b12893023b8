use crate::models::ResponseVO;
use crate::services::system_feature::{SystemFeature, UsageMode};
use crate::utils::path::get_static_dir;

pub(crate) struct FileUrlService;

impl FileUrlService {
    /// 获取返给前端文件url的前缀
    pub(crate) fn get_fe_file_url_prefix() -> Result<ResponseVO<String>, ResponseVO<()>> {
        let usage_mode: UsageMode = SystemFeature::get_sys_feat_owned()
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        if usage_mode.is_client() {
            return Ok(ResponseVO::success(Some(format!("{}/static", usage_mode.url)), None));
        }
        let static_dir = get_static_dir()
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        let file_stem = static_dir
            .to_str()
            .ok_or(ResponseVO::error(Some(String::from("找不到static_dir"))))?;
        #[cfg(target_os = "windows")]
        let prefix = format!("http://asset.localhost/{}", file_stem);
        #[cfg(not(target_os = "windows"))]
        let prefix = format!("asset://localhost/{}", file_stem);
        Ok(ResponseVO::success(Some(prefix), None))
    }
}
use crate::database::assemble::AssembleRepository;
use crate::models::ResponseVO;
use crate::vo::assemble::AssembleVo;

pub(crate) struct AssembleService;

impl AssembleService {
    pub async fn get_assembles_by_type(
        book_type: String
    ) -> Result<ResponseVO<Vec<AssembleVo>>, ResponseVO<()>> {
        match AssembleRepository::find_all_by_type(book_type).await {
            Ok(ref list) => Ok(ResponseVO::success(Some(AssembleVo::from_list(list)), None)),
            Err(error) => Err(ResponseVO::error(Some(error.to_string()))),
        }
    }
}
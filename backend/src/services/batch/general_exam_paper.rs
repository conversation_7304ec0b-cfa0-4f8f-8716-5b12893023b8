use crate::models::surreal::{
    deserialize_id_u64, get_db, get_dynamic_sql_string, get_update_map, FieldValue, QueryOption,
    Record,
};
use crate::services::batch::batch::GeneralExamPaperBatch;
use crate::services::question_card_configuration::QuestionCardConfiguration;
use crate::services::question_configuration::QuestionConfiguration;
use crate::services::subject::{get_subject_code, get_subject_name, Subject};
use crate::services::task;
use crate::services::task::Task;
use crate::services::task::TaskPaper;
use crate::services::task_paper_question::TaskPaperQuestion;
use crate::utils::md5::calculate_md5;
use chrono::prelude::*;
use chrono::Utc;
// use futures::TryFutureExt;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::error::Error;
use std::path::{Path, PathBuf};
use std::{fs, thread};
use surrealdb::engine::local::Db;
use surrealdb::Surreal;
use tauri::{Emitter, Manager};
use tauri::path::BaseDirectory;
use tokio::runtime::Runtime;

use crate::services::question_issue::QuestionIssue;
use crate::services::system_feature::{PaperUpdBatchFeature, SystemFeature};
use crate::services::task_paper_docx::TaskPaperDocx;
use crate::utils::snowflake_generator::SnowflakeGeneratorUtil;
use crate::vo::batch::general_exam_paper::GeneralExamPaperVo;
use tokio::time::{sleep, Duration};
use crate::parser::parse_paper_content;
use crate::services::textbook::Textbook;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct GeneralExamPaper {
    #[serde(rename = "id", deserialize_with = "deserialize_id_u64")]
    pub id: u64,
    #[serde(rename = "batchId")]
    pub batch_id: u64,
    pub name: String,
    #[serde(rename = "fileName")]
    pub file_name: String,
    #[serde(rename = "subjectName")]
    pub subject_name: String,
    #[serde(rename = "subjectCode")]
    pub subject_code: String,
    #[serde(rename = "isAnswer")]
    pub is_answer: bool,
    #[serde(rename = "answerId")]
    pub answer_id: Option<u64>,
    #[serde(rename = "isDeleted")]
    pub is_deleted: u32,
    // 状态枚举：100：初始化；200：绑定任务；300：解析中；310：解析完成；400：异常
    pub status: u32,
    #[serde(rename = "updateTime")]
    pub update_time: u64,
    #[serde(rename = "createTime")]
    pub create_time: u64,
    #[serde(rename = "taskId")]
    pub task_id: u64,
    #[serde(rename = "taskPaperId")]
    pub task_paper_id: u64,
    pub md5: String,
    pub remark: String,
}

impl GeneralExamPaper {
    fn new(file: String, batch_id: u64) -> GeneralExamPaper {
        let path = Path::new(&file);
        let file_name = path
            .file_name()
            .and_then(|name| name.to_str())
            .unwrap_or("")
            .to_string();
        let name = path
            .file_stem()
            .and_then(|stem| stem.to_str())
            .unwrap_or("")
            .to_string();
        let file_content = fs::read(&path).unwrap_or_default();
        let md5_val = calculate_md5(&file_content);

        let now = Utc::now().timestamp_millis() as u64;
        let id = SnowflakeGeneratorUtil::next();

        GeneralExamPaper {
            id,
            name: name.clone(),
            file_name,
            md5: md5_val,
            batch_id,
            subject_name: "".to_string(),
            subject_code: "".to_string(),
            is_answer: name.contains("答案"),
            answer_id: None,
            is_deleted: 0,
            status: 100,
            update_time: now,
            create_time: now,
            task_id: 0,
            task_paper_id: 0,
            remark: "".to_string(),
        }
    }

    pub fn extract_subject_code_from_file_name(&self) -> (String, i8) {
        let paper_name = self.name.clone();
        let chars = paper_name.chars();
        let chars: Vec<String> = chars
            .filter(|c| c != &' ')
            .take(5)
            .map(|c| c.to_string())
            .collect();
        let code;
        let result = if !chars.is_empty() {
            code = 0;
            chars.join("")
        } else {
            code = 1;
            "上传的试卷文件名为空，请重命名".to_string()
        };
        (result, code)
    }

    pub async fn by_id(db: &Surreal<Db>, id: u64) -> Result<Option<Self>, surrealdb::Error> {
        let query_vec = vec![QueryOption::new(
            "id".to_string(),
            FieldValue::Num(id),
            false,
            true,
        )];

        let new_sql = get_dynamic_sql_string("GeneralExamPaper".to_string(), Some(query_vec), None);
        let mut result = db.query(new_sql).await?;
        let mut return_vec: Vec<Self> = result.take(0)?;
        if !return_vec.is_empty() {
            Ok(Some(return_vec.remove(0)))
        } else {
            Ok(None)
        }
    }

    pub async fn by_answer_id(
        db: &Surreal<Db>,
        id: u64,
    ) -> Result<Option<Self>, surrealdb::Error> {
        let query_vec = vec![QueryOption::new(
            "answerId".to_string(),
            FieldValue::Num(id),
            false,
            false,
        )];

        let new_sql = get_dynamic_sql_string("GeneralExamPaper".to_string(), Some(query_vec), None);
        let mut result = db.query(new_sql).await?;
        let mut return_vec: Vec<GeneralExamPaper> = result.take(0)?;
        if !return_vec.is_empty() {
            Ok(Some(return_vec.remove(0)))
        } else {
            Ok(None)
        }
    }

    pub async fn delete(
        db: &Surreal<Db>,
        data_root_path: &Path,
        id: u64,
    ) -> Result<(), surrealdb::Error> {
        let o_paper: Option<GeneralExamPaper> = db.delete(("GeneralExamPaper", id)).await?;
        match o_paper {
            None => {}
            Some(paper) => {
                let path = GeneralExamPaper::get_file_path(data_root_path, &paper);
                let _ = fs::remove_dir_all(path);
            }
        };
        Ok(())
    }

    pub async fn delete_all_by_paper_name_batch(
        db: &Surreal<Db>,
        batch_id: u64,
        data_root_path: &Path,
        file_names: &Vec<String>,
    ) -> Result<(), Box<dyn Error>> {
        let temp = file_names
            .iter()
            .map(|pn| format!("\"{}\"", pn))
            .collect::<Vec<String>>()
            .join(",");
        let sql = format!(
            "SELECT * FROM GeneralExamPaper WHERE batchId = $batchId AND fileName IN [{}]",
            temp
        );
        let mut result = db.query(sql).bind(("batchId", batch_id)).await?;
        let geps: Vec<GeneralExamPaper> = result.take(0)?;
        for gep in geps {
            delete_paper(db, data_root_path, gep.id).await?;
        }
        Ok(())
    }

    pub async fn by_batch(
        db: &Surreal<Db>,
        batch_id: u64,
        filename_option: Option<String>,
    ) -> Result<Vec<GeneralExamPaper>, surrealdb::Error> {
        let filename = match filename_option {
            Some(value) => value,
            _ => "".to_string(),
        };
        let query_vec = vec![
            QueryOption::new(
                "batchId".to_string(),
                FieldValue::Num(batch_id),
                false,
                false,
            ),
            QueryOption::new(
                "fileName".to_string(),
                FieldValue::Str(filename),
                true,
                false,
            ),
        ];

        let mut new_sql =
            get_dynamic_sql_string("GeneralExamPaper".to_string(), Some(query_vec), None);
        new_sql.push_str(" order by updateTime desc");

        let mut result = db.query(new_sql).await?;
        let return_vec: Vec<GeneralExamPaper> = result.take(0)?;
        Ok(return_vec)
    }

    async fn save(db: &Surreal<Db>, paper: &mut GeneralExamPaper) -> Result<(), Box<dyn Error>> {
        let existing_record: Option<Record> = db.select(("GeneralExamPaper", paper.id)).await?;

        if existing_record.is_none() {
            // 如果数据不存在，则创建新记录
            let _: Vec<Record> = db.create("GeneralExamPaper").content(paper).await?;
        } else {
            let now = Utc::now().timestamp_millis() as u64;
            paper.update_time = now;
            let value = serde_json::to_value(&paper)?;
            let update_map = get_update_map(value);
            let _: Option<Record> = db
                .update(("GeneralExamPaper", paper.id))
                .content(update_map)
                .await?;
        }
        Ok(())
    }

    fn get_file_path(data_root_path: &Path, paper: &GeneralExamPaper) -> PathBuf {
        data_root_path
            .join("batch")
            .join(paper.batch_id.to_string())
            .join(paper.id.to_string())
    }

    async fn get_same_name(db: &Surreal<Db>, file: String, batch_id: u64) -> GeneralExamPaper {
        let path = Path::new(&file);
        let file_name = path
            .file_name()
            .and_then(|name| name.to_str())
            .unwrap_or("")
            .to_string();

        let is_answer = file_name.contains("答案");

        let query_vec = vec![
            QueryOption::new(
                "batchId".to_string(),
                FieldValue::Num(batch_id),
                false,
                false,
            ),
            QueryOption::new(
                "fileName".to_string(),
                FieldValue::Str(file_name),
                false,
                false,
            ),
        ];

        let new_sql = get_dynamic_sql_string("GeneralExamPaper".to_string(), Some(query_vec), None);

        match db.query(new_sql).await {
            Ok(mut response) => {
                let mut return_vec: Vec<GeneralExamPaper> = response.take(0).unwrap_or_default();

                if return_vec.is_empty() {
                    let mut paper = GeneralExamPaper::new(file, batch_id);
                    paper.is_answer = is_answer;
                    paper
                } else {
                    let mut paper = return_vec.remove(0);
                    let file_content = fs::read(&path).unwrap_or_default();
                    let md5_val = calculate_md5(&file_content);
                    paper.md5 = md5_val;
                    paper.is_answer = is_answer;
                    paper
                }
            }
            Err(_) => {
                let mut paper = GeneralExamPaper::new(file, batch_id);
                paper.is_answer = is_answer;
                return paper;
            }
        }
    }
}

fn sort_paper_list(paper_list: &Vec<String>) -> Vec<String> {
    let mut sorted_paper_list = vec![];
    for pl in paper_list.iter() {
        if !pl.contains("答案") {
            sorted_paper_list.push(pl.clone());
        }
    }
    for pl in paper_list.iter() {
        if pl.contains("答案") {
            sorted_paper_list.push(pl.clone());
        }
    }
    sorted_paper_list
}

pub async fn add_papers(
    app_handle: tauri::AppHandle,
    window: tauri::Window,
    db: &Surreal<Db>,
    schema_name: &str,
    data_root_path: &Path,
    paper_list: Vec<String>,
    batch_id: u64,
) -> Result<(), Box<dyn Error>> {
    let p_u_b_feat: PaperUpdBatchFeature = SystemFeature::get_sys_feat_owned(db).await?;
    let generate_qt_desc = p_u_b_feat.opt_generate_qt_desc.unwrap_or(false);
    let enable_check_subject = p_u_b_feat.opt_checking_subject_in_db.unwrap_or(false);
    if p_u_b_feat.extracting_paper_struct {
        add_papers_with_extracting_paper_struct(
            app_handle,
            window,
            db,
            schema_name,
            data_root_path,
            paper_list,
            batch_id,
            generate_qt_desc,
            enable_check_subject,
        )
        .await
    } else {
        add_papers_with_exists_que_config(
            app_handle,
            window,
            db,
            data_root_path,
            paper_list,
            batch_id,
            enable_check_subject,
        )
        .await
    }
}

/// 通过batchid生成task，对内部所有试卷进行同学科分组创建task，并构建和保存TaskPaperQuestion和题卡配置
pub async fn general_batch_create_task(
    db: &Surreal<Db>,
    window: &tauri::Window,
    id: u64,
    opt_filename_subject_config_map: Option<HashMap<String, (Subject, QuestionConfiguration)>>,
) -> Result<(), String> {
    let batch = GeneralExamPaperBatch::by_id(&db, id)
        .await
        .map_err(|e| e.to_string())?;
    let mut ge_papers: Vec<GeneralExamPaper> = GeneralExamPaper::by_batch(&db, id, None)
        .await
        .map_err(|e| e.to_string())?;

    // 按照 task_id 进行排序，确保 task_id != 0 的排在前面
    ge_papers.sort_by_key(|paper| !paper.task_id);

    // 需要保存的gen_paper的id集合
    let mut need_to_save_gp_id_set: HashSet<u64> = HashSet::new();

    // 获取试题配置学科映射
    let mut tp_id_que_config_map: HashMap<u64, QuestionConfiguration> = HashMap::new();

    let mut subject_task_map: HashMap<String, Task> = HashMap::new();

    // 遍历所有的 ge_papers
    for paper in ge_papers.iter_mut() {
        // 如果学科是 -1，跳过该 paper
        if paper.subject_code == "-1" {
            continue;
        }

        if paper.is_answer {
            continue;
        }

        // 如果 task_id 为 0，表示该 paper 没有任务
        if paper.task_id == 0 {
            // 检查 Map 中是否已经存在相同学科的任务
            if let Some(task) = subject_task_map.get_mut(&paper.subject_code) {
                // 如果存在，将 paper 的 task_id 设置为已存在的 task_id

                let new_task_paper = TaskPaper {
                    deleted: false,
                    paper_name: paper.name.clone(),
                    task_paper_docxes: vec![],
                    id: SnowflakeGeneratorUtil::next(),
                    acceptance_status: 0,
                    status: 100,
                };
                paper.task_id = task.id;
                paper.task_paper_id = new_task_paper.id;

                task.task_papers.push(new_task_paper);
            } else {
                // all paper if without taskId and taskPaperId, create or add to same subject task, create new taskPaper, add to task
                // 如果不存在，创建一个新任务
                // 将新任务 ID 存入 Map，并更新 paper 的 task_id

                let now: DateTime<Local> = Local::now();
                let formatted_date = now.format("%b %d, %Y, %-I:%M:%S %p").to_string();

                let mut textbook_vec = vec![];
                let mut provide_text_book = false;
                let textbook_result = Textbook::find_by_subject_code(db, &*paper.subject_code.clone()).await;
                match textbook_result {
                    Ok(textbook) => {
                        let tbid = textbook.id;
                        textbook_vec.push(task::Textbook { id: 0, book_id: tbid });
                    }
                    Err(_) => {}
                }
                if !textbook_vec.is_empty() {
                    provide_text_book = true;
                }

                let mut new_task = Task {
                    update_date: formatted_date.clone(),
                    created_date: formatted_date.clone(),
                    task_papers: vec![],
                    id: SnowflakeGeneratorUtil::next(),
                    name: format!(
                        "批次 {} 上传：{}",
                        batch.name,
                        now.format("%Y%m%d").to_string()
                    ),
                    subject_code: paper.subject_code.clone(),
                    subject_name: Some(paper.subject_name.clone()),
                    status: 410,
                    from_batch_upload: Some(true),

                    create_user_id: -1,
                    attachments: vec![],
                    cutoff_time: "".to_string(),
                    provide_text_book,
                    provide_exam_syllabus: false,
                    task_exam_syllabuses: vec![],
                    has_teacher_terminal: false,
                    textbooks: textbook_vec,
                    create_step: 0,
                    task_proposition_resources: vec![],
                    paper_section: 0,
                };

                let new_task_paper = TaskPaper {
                    deleted: false,
                    paper_name: paper.name.clone(),
                    task_paper_docxes: vec![],
                    id: SnowflakeGeneratorUtil::next(),
                    acceptance_status: 0,
                    status: 100,
                };

                paper.task_id = new_task.id;
                paper.task_paper_id = new_task_paper.id;

                new_task.task_papers.push(new_task_paper);
                subject_task_map.insert(paper.subject_code.clone(), new_task);
            }
            // gen_paper有更新，需要保存
            need_to_save_gp_id_set.insert(paper.id);
        } else {
            //  find out all task with a map, key is task subject, value is task
            // 如果已经有 task_id，确保 map 中的 task_id 是最新的
            let task = Task::find_by_task_id(&db, paper.task_id)
                .await
                .map_err(|e| e.to_string())?
                .ok_or(format!("找不到指定命题任务，task_id: {}", paper.task_id))?;
            subject_task_map.insert(paper.subject_code.clone(), task);
        }

        // 设置试题配置映射
        let mut opt_target_config = None;
        if let Some(ref filename_subject_config_map) = opt_filename_subject_config_map {
            if let Some((_, config)) = filename_subject_config_map.get(&paper.file_name) {
                opt_target_config = Some(config.clone());
            }
        }
        if opt_target_config.is_none() {
            let r = QuestionConfiguration::get_default_config(db, paper.subject_code.as_str())
                .await
                .map_err(|e| e.to_string())?;
            if let Some(qc) = r {
                opt_target_config = Some(qc);
            }
        }
        if let Some(qc) = opt_target_config {
            tp_id_que_config_map.insert(paper.task_paper_id, qc);
        }

        sleep(Duration::from_millis(1)).await;
    }

    // 收集所有需要更新答案卷的映射 (answer_id, task_id, task_paper_id)
    let mut answer_updates = Vec::new();

    for paper in ge_papers.iter() {
        if !paper.is_answer {
            if let Some(answer_id) = paper.answer_id {
                answer_updates.push((answer_id, paper.task_id, paper.task_paper_id));
            }
        }
    }

    // 遍历 ge_papers 更新答案卷的 task_id 和 task_paper_id
    for paper in ge_papers.iter_mut() {
        if paper.is_answer {
            if let Some((_, task_id, task_paper_id)) =
                answer_updates.iter().find(|(id, _, _)| *id == paper.id)
            {
                paper.task_id = *task_id;
                paper.task_paper_id = *task_paper_id;
            } else {
                paper.status = 400;
                paper.remark = "找不到对应的试题卷，请确保试题卷先于答案卷上传".to_string();
            }
            need_to_save_gp_id_set.insert(paper.id);
        }
    }

    // 这里应该取到的所有paper和task都是需要储存的
    let tasks_to_save: Vec<Task> = subject_task_map.values().cloned().collect();
    for task in tasks_to_save {
        Task::save(&db, &task).await.expect("TODO: panic message");
    }

    // @lizhe
    // taskPaperQuestion 和试题配置
    for (subject_code, task) in subject_task_map.iter() {
        for paper in task.task_papers.iter() {
            if let Some(que_config) = tp_id_que_config_map.get(&paper.id) {
                // todo 后续可能需要支持当默认的试题配置结构变化时，重新构建和保存TaskPaperQuestion和题卡
                let old_questions = TaskPaperQuestion::find_all_by_paper_id(db, paper.id)
                    .await
                    .map_err(|e| e.to_string())?;
                if old_questions.is_empty() {
                    // 生成新的taskPaperQuestion
                    let questions = TaskPaperQuestion::generate_questions_by_que_config(
                        task.id,
                        paper.id,
                        subject_code.as_str(),
                        que_config,
                    )
                    .await
                    .map_err(|e| e.to_string())?;
                    TaskPaperQuestion::save_all(db, questions)
                        .await
                        .map_err(|e| e.to_string())?;
                }
                let opt_old_qcc = QuestionCardConfiguration::find_by_id(db, paper.id)
                    .await
                    .map_err(|e| e.to_string())?;
                if opt_old_qcc.is_none() {
                    // 生成新的题卡配置
                    let que_card_config = QuestionCardConfiguration::generate_by_que_config(
                        task.id, paper.id, que_config,
                    )
                    .map_err(|e| e.to_string())?;
                    QuestionCardConfiguration::save(db, &que_card_config)
                        .await
                        .map_err(|e| e.to_string())?;
                }
            }
        }
    }

    for mut paper in ge_papers {
        if need_to_save_gp_id_set.contains(&paper.id) {
            GeneralExamPaper::save(&db, &mut paper)
                .await
                .map_err(|e| format!("ge_paper save error: {}", e))?;
        }
        if paper.status == 400 {
            let _ = window.emit(
                "general-exam-paper-changed",
                serde_json::to_string(&GeneralExamPaperVo::from(&paper)).unwrap(),
            );
        }
    }

    Ok(())
}

/// 解析上传的试卷文件
pub async fn parse_uploaded_papers(
    app_handle: tauri::AppHandle,
    window: tauri::Window,
    db: &Surreal<Db>,
    batch_id: u64,
    paper_paths: &Vec<String>,
    exact_paper_struct: bool,
    enable_check_subject: bool,
    is_async: bool,
) -> Result<(), Box<dyn Error>> {
    async fn check_subject_in_db(
        file_path: &Path, resource_path: &Path, app_data_dir: &PathBuf, subject_name: &str, subject_code: &str
    ) -> Result<(), Box<dyn Error>> {
        let content = parse_paper_content(file_path, resource_path, app_data_dir)?;
        let (subject, _) = QuestionConfiguration::extract_subject_by_que_paper_content(&content)?;
        if subject_name != subject.name {
            return Err(Box::from(anyhow::Error::msg(format!("课程名称与数据库不一致，应该是：{}，检测到：{}", subject_name, subject.name))));
        }
        if subject_code != subject.code {
            return Err(Box::from(anyhow::Error::msg(format!("课程代码与数据库不一致，应该是：{}，检测到：{}", subject_code, subject.code))));
        }
        Ok(())
    }
    // 仅获取当前上传的试卷信息，过滤掉无法解析的试卷
    let mut gen_papers = vec![];
    for pl in paper_paths.iter() {
        let gen_paper = GeneralExamPaper::get_same_name(&db, pl.clone(), batch_id.clone()).await;
        // 如果这份卷先前步骤有异常，就忽略
        if gen_paper.status == 400 {
            continue;
        }
        // 如果课程不存在或者指定的task、taskPaper不存在，就忽略
        if gen_paper.subject_code.is_empty()
            || gen_paper.subject_code == "-1"
            || gen_paper.task_id == 0
            || gen_paper.task_paper_id == 0
        {
            continue;
        }
        gen_papers.push(gen_paper);
    }
    // 修改状态为解析中并保存
    for p in gen_papers.iter_mut() {
        if p.status != 300 {
            p.status = 300;
            GeneralExamPaper::save(db, p).await?;
        }
    }

    // 保证试题卷永远在答案卷前面
    gen_papers.sort_by(|a, b| {
        let a_v = if !a.is_answer { 0 } else { 1 };
        let b_v = if !b.is_answer { 0 } else { 1 };
        if a_v == b_v {
            a.id.cmp(&b.id)
        } else {
            a_v.cmp(&b_v)
        }
    });

    // 解析当前上传的试卷
    let handle = thread::spawn(move || {
        let rt = Runtime::new().unwrap();
        rt.block_on(async {
            let app_data_dir = app_handle.path().app_data_dir().unwrap();
            let resources_path = app_handle.path().resolve("", BaseDirectory::Resource).unwrap();
            let db = get_db().await;
            for gen_paper in gen_papers.iter_mut() {
                let target_dir = GeneralExamPaper::get_file_path(&app_data_dir, gen_paper);
                let target_file_path = target_dir.join(&gen_paper.file_name);
                let tex_path_opt = if (exact_paper_struct || enable_check_subject)
                    && gen_paper.file_name.ends_with(".docx")
                {
                    let fno = gen_paper.file_name.strip_suffix(".docx").unwrap();
                    Some(target_dir.join(fno).join(format!("{}.tex", fno)))
                } else {
                    None
                };
                let mut result = Ok(());
                if !exact_paper_struct && enable_check_subject {
                    result = check_subject_in_db(
                        target_file_path.as_path(), resources_path.as_path(),
                        &app_data_dir, &gen_paper.subject_name, &gen_paper.subject_code,
                    ).await;
                }
                if result.is_ok() {
                    result = TaskPaperDocx::upload_no_std_paper(
                        &app_handle,
                        db.clone(),
                        window.clone(),
                        false,
                        target_file_path
                            .canonicalize()
                            .unwrap()
                            .to_str()
                            .unwrap()
                            .to_string(),
                        tex_path_opt,
                        gen_paper.task_paper_id,
                        gen_paper.is_answer,
                    ).await;
                }
                if let Err(e) = result {
                    gen_paper.status = 400;
                    gen_paper.remark = e.to_string();
                    println!("Parsing paper batches Error: {}", e.to_string());
                } else {
                    gen_paper.remark = "".to_string();
                    gen_paper.status = 310;
                }
                let result = GeneralExamPaper::save(&db, gen_paper).await;
                if let Err(e) = result {
                    gen_paper.status = 400;
                    gen_paper.remark = e.to_string();
                    println!("Saving GeneralExamPaper Error: {}", e.to_string());
                }
                let _ = window.clone().emit(
                    "general-exam-paper-changed",
                    serde_json::to_string(&GeneralExamPaperVo::from(&gen_paper)).unwrap(),
                );
            }
        });
    });

    if !is_async {
        handle.join().unwrap();
    }

    Ok(())
}

pub async fn delete_paper(
    db: &Surreal<Db>,
    data_root_path: &Path,
    id: u64,
) -> Result<(), Box<dyn Error>> {
    //     @lizhe
    //     4. 删除taskPaperQuestion和后续的和试题配置
    let d_paper_opt = GeneralExamPaper::by_id(&db, id).await?;
    if d_paper_opt.is_none() {
        return Ok(());
    }
    let d_paper = d_paper_opt.unwrap();
    if d_paper.is_answer {
        // 是答案卷就删除单独一份答案，没有后续处理。
        GeneralExamPaper::delete(&db, &data_root_path, id).await?;
        // 删除原卷答案id
        let o_paper_opt = GeneralExamPaper::by_answer_id(&db, id).await?;
        if let Some(mut o_paper) = o_paper_opt {
            o_paper.answer_id = None;
            GeneralExamPaper::save(&db, &mut o_paper).await?;
        }
    } else {
        //     普通试卷，删除task下相关内容，判断是否删除task，删除试卷
        let answer_id = d_paper.answer_id;
        if answer_id.is_some() {
            GeneralExamPaper::delete(&db, &data_root_path, answer_id.unwrap()).await?;
        }
        GeneralExamPaper::delete(&db, &data_root_path, id).await?;
        let task_paper_id = d_paper.task_paper_id;
        let task_id = d_paper.task_id;
        if task_id != 0 {
            Task::delete_paper(&db, task_id, task_paper_id).await?;
            TaskPaperQuestion::delete_all_by_paper_id(db, d_paper.task_paper_id).await?;
            QuestionCardConfiguration::delete_by_id(db, d_paper.task_paper_id).await?;
            QuestionIssue::delete_all_by_paper_id(db, d_paper.task_paper_id).await?;
        }
    }
    GeneralExamPaperBatch::recount(&db, d_paper.batch_id).await?;
    Ok(())
}

pub async fn by_batch(
    db: &Surreal<Db>,
    batch_id: u64,
    filename_option: Option<String>,
) -> Result<Vec<GeneralExamPaper>, String> {
    let papers = GeneralExamPaper::by_batch(&db, batch_id, filename_option)
        .await
        .map_err(|e| format!("SurrealDB error: {}", e))?;
    Ok(papers)
}

pub async fn handle_save_general_exam_paper(
    db: &Surreal<Db>,
    data_root_path: &Path,
    paper_list: &Vec<String>,
    batch_id: u64,
) -> Result<(), Box<dyn Error>> {
    // 全局paper映射，不管保存
    let mut paper_map: HashMap<String, GeneralExamPaper> = HashMap::new();
    // 需要保存的paper映射
    let mut need_to_save_gp_id_set: HashSet<u64> = HashSet::new();
    // 获取所有旧书卷，用以上传时处理新答案id
    let old_ge_papers = GeneralExamPaper::by_batch(&db, batch_id, None).await?;
    for ogep in old_ge_papers {
        paper_map.insert(ogep.name.clone(), ogep.clone());
    }

    for paper in paper_list.iter() {
        let mut ge_paper =
            GeneralExamPaper::get_same_name(&db, paper.clone(), batch_id.clone()).await;
        ge_paper.subject_code = "-1".to_string();
        ge_paper.subject_name = "空".to_string();
        ge_paper.status = 100;

        let paper_path = Path::new(paper);
        let target_dir = GeneralExamPaper::get_file_path(data_root_path, &ge_paper);
        fs::create_dir_all(&target_dir).map_err(|e| e.to_string())?;
        let target_file_path = target_dir.join(&ge_paper.file_name);
        if target_file_path.exists() {
            fs::remove_file(&target_file_path)?;
        }
        fs::copy(&paper_path, &target_file_path)?;
        paper_map.insert(ge_paper.name.clone(), ge_paper.clone());
        need_to_save_gp_id_set.insert(ge_paper.id);
    }

    // 创建一个临时映射来存储答案卷的 ID
    let answer_map: HashMap<String, u64> = paper_map
        .iter()
        .filter_map(|(name, paper)| {
            if paper.is_answer {
                Some((name.clone(), paper.id))
            } else {
                None
            }
        })
        .collect();

    // 第二轮遍历：更新试卷的答案 ID
    for paper in paper_map.values_mut() {
        if !paper.is_answer {
            let answer_name = format!("{}答案", paper.name);
            if let Some(&answer_id) = answer_map.get(&answer_name) {
                paper.answer_id = Some(answer_id);
                need_to_save_gp_id_set.insert(paper.id);
            }
        }
    }

    // 第三轮遍历：保存试卷
    for paper in paper_map.values_mut() {
        // Save the paper object here
        if need_to_save_gp_id_set.contains(&paper.id) {
            GeneralExamPaper::save(&db, paper).await?;
        }
    }

    Ok(())
}

pub async fn handle_subject_with_exists_que_config(
    db: &Surreal<Db>,
    paper_list: &Vec<String>,
    batch_id: u64,
) -> Result<(), Box<dyn Error>> {
    for paper in paper_list.iter() {
        let mut ge_paper =
            GeneralExamPaper::get_same_name(&db, paper.clone(), batch_id.clone()).await;
        let (result, code) = ge_paper.extract_subject_code_from_file_name();
        if code != 0 {
            ge_paper.status = 400;
            ge_paper.remark = result;
        } else {
            let subject_code = result;
            let opt_subject_name = get_subject_name(&subject_code, db).await;
            if let Some(sn) = opt_subject_name {
                ge_paper.subject_name = sn;
                ge_paper.subject_code = subject_code;
            } else {
                ge_paper.status = 400;
                ge_paper.remark = format!("在数据库中找不到对应的课程信息，课程代码：{}", subject_code);
            }
        }
        GeneralExamPaper::save(db, &mut ge_paper).await?;
    }

    Ok(())
}

pub async fn add_papers_with_exists_que_config(
    app_handle: tauri::AppHandle,
    window: tauri::Window,
    db: &Surreal<Db>,
    data_root_path: &Path,
    paper_list: Vec<String>,
    batch_id: u64,
    enable_check_subject: bool,
) -> Result<(), Box<dyn Error>> {
    handle_save_general_exam_paper(db, data_root_path, &paper_list, batch_id).await?;

    GeneralExamPaperBatch::recount(&db, batch_id).await?;

    handle_subject_with_exists_que_config(&db, &paper_list, batch_id).await?;

    general_batch_create_task(&db, &window, batch_id, None).await?;

    let sorted_paper_list = sort_paper_list(&paper_list);
    parse_uploaded_papers(app_handle, window, &db, batch_id, &sorted_paper_list, false, enable_check_subject, true).await?;

    Ok(())
}

pub async fn add_papers_with_extracting_paper_struct(
    app_handle: tauri::AppHandle,
    window: tauri::Window,
    db: &Surreal<Db>,
    schema_name: &str,
    data_root_path: &Path,
    paper_list: Vec<String>,
    batch_id: u64,
    generate_qt_desc: bool,
    enable_check_subject: bool,
) -> Result<(), Box<dyn Error>> {
    let schema_name = schema_name.to_string();
    // todo 删除所有同名卷
    let file_names: Vec<String> = paper_list
        .iter()
        .map(|pl| {
            Path::new(pl)
                .file_name()
                .and_then(|name| name.to_str())
                .unwrap_or("")
                .to_string()
        })
        .collect();
    GeneralExamPaper::delete_all_by_paper_name_batch(db, batch_id, data_root_path, &file_names)
        .await?;

    // todo 新增每份卷的上传记录
    handle_save_general_exam_paper(db, data_root_path, &paper_list, batch_id).await?;

    GeneralExamPaperBatch::recount(&db, batch_id).await?;

    // 更改状态
    for pl in paper_list.iter() {
        let mut gen_paper =
            GeneralExamPaper::get_same_name(&db, pl.clone(), batch_id.clone()).await;
        if gen_paper.name.contains(" ") {
            gen_paper.status = 400;
            gen_paper.subject_name = "未知课程".to_string();
            gen_paper.remark = "文件名不可包含空格！".to_string();
            GeneralExamPaper::save(db, &mut gen_paper).await?;
        } else {
            gen_paper.status = 300;
            gen_paper.subject_name = "识别中...".to_string();
            GeneralExamPaper::save(db, &mut gen_paper).await?;
        }
    }

    // todo 依次提取每份卷的课程和试卷结构
    let sorted_paper_list = sort_paper_list(&paper_list);

    let resources_path = app_handle.path().resolve("", BaseDirectory::Resource).unwrap();
    let app_data_dir = app_handle.path().app_data_dir().unwrap();

    thread::spawn(move || {
        let rt = Runtime::new().unwrap();
        let _: Result<(), _> = rt.block_on(async {
            let db = get_db().await;
            let mut filename_subject_config_map: HashMap<String, (Subject, QuestionConfiguration)> =
                HashMap::new();
            for pl in sorted_paper_list.iter() {
                let mut gen_paper =
                    GeneralExamPaper::get_same_name(&db, pl.clone(), batch_id.clone()).await;
                if gen_paper.status == 400 {
                    continue;
                }
                let target_dir = GeneralExamPaper::get_file_path(&app_data_dir, &gen_paper);
                let target_file_path = target_dir.join(&gen_paper.file_name);

                if !gen_paper.is_answer {
                    let result = QuestionConfiguration::extract_and_save_que_config_from_paper(
                        &db,
                        &schema_name,
                        target_file_path.as_path(),
                        resources_path.as_path(),
                        &app_data_dir,
                        generate_qt_desc,
                        enable_check_subject,
                    )
                    .await;
                    if let Err(e) = result {
                        gen_paper.status = 400;
                        gen_paper.remark = e.to_string();
                    } else {
                        let (subject, config) = result.unwrap();
                        println!("subject: {:#?}", subject);
                        println!("config: {:#?}", config);
                        gen_paper.subject_name = subject.name.clone();
                        gen_paper.subject_code = subject.code.clone();
                        filename_subject_config_map
                            .insert(gen_paper.file_name.clone(), (subject, config));
                    }
                } else {
                    let result = QuestionConfiguration::extract_subject_name_from_answer_paper(
                        &db,
                        target_file_path.as_path(),
                        resources_path.as_path(),
                        &app_data_dir,
                    )
                    .await;
                    if let Err(e) = result {
                        gen_paper.status = 400;
                        gen_paper.remark = e.to_string();
                    } else {
                        let subject_name = result.unwrap();
                        println!("subject name: {}", subject_name);
                        let opt_subject_code = get_subject_code(subject_name.as_str(), &db).await;
                        if let Some(subject_code) = opt_subject_code {
                            gen_paper.subject_name = subject_name.clone();
                            gen_paper.subject_code = subject_code;
                        } else {
                            gen_paper.status = 400;
                            gen_paper.remark = format!(
                                "课程名称“{}”找不到课程代码，请检查试卷中课程名称是否正确",
                                subject_name
                            );
                        }
                    }
                }

                let _ = GeneralExamPaper::save(&db, &mut gen_paper).await;
                sleep(Duration::from_millis(1)).await;
                let _ = window.clone().emit(
                    "general-exam-paper-changed",
                    serde_json::to_string(&GeneralExamPaperVo::from(&gen_paper)).unwrap(),
                );
            }

            // todo 更新试卷解析记录状态
            general_batch_create_task(&db, &window, batch_id, Some(filename_subject_config_map))
                .await?;

            parse_uploaded_papers(app_handle, window, &db, batch_id, &sorted_paper_list, true, enable_check_subject, false).await?;

            Ok::<(), Box<dyn Error>>(())
        });
    });

    Ok(())
}

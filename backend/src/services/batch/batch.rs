use crate::models::surreal::{
    deserialize_id_u64, get_dynamic_count_sql_string, get_dynamic_sql_string, get_update_map,
    FieldValue, OrderOption, QueryOption, Record,
};
use crate::utils::snowflake_generator::SnowflakeGeneratorUtil;
use chrono::{Utc};
use serde_derive::{Deserialize, Serialize};
use std::option::Option;
use surrealdb::engine::local::Db;
use surrealdb::Surreal;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct GeneralExamPaperBatch {
    #[serde(rename = "id", deserialize_with = "deserialize_id_u64")]
    pub id: u64,
    pub name: String,
    #[serde(rename = "paperCount")]
    pub paper_count: u32,
    #[serde(rename = "answerCount")]
    pub answer_count: u32,
    #[serde(rename = "isDeleted")]
    pub is_deleted: u32,
    #[serde(rename = "updateTime")]
    pub update_time: u64,
    #[serde(rename = "createTime")]
    pub create_time: u64,
    pub status: u64,
}
impl GeneralExamPaperBatch {
    pub async fn by_id(
        db: &Surreal<Db>,
        batch_id: u64,
    ) -> Result<GeneralExamPaperBatch, surrealdb::Error> {
        let mut query_vec: Vec<QueryOption> = Vec::new();
        query_vec.push(QueryOption::new(
            "id".parse().unwrap(),
            FieldValue::Num(batch_id),
            false,
            true,
        ));

        let new_sql = get_dynamic_sql_string(
            "GeneralExamPaperBatch".parse().unwrap(),
            Some(query_vec),
            None,
        );
        let mut result = db.query(&new_sql).await?;
        let mut list: Vec<GeneralExamPaperBatch> = result.take(0)?;
        let batch = list.remove(0);
        Ok(batch)
    }

    pub async fn list(
        db: &Surreal<Db>,
        batch_name: String,
    ) -> Result<Vec<GeneralExamPaperBatch>, surrealdb::Error> {
        let mut query_vec: Vec<QueryOption> = Vec::new();
        query_vec.push(QueryOption::new(
            "name".parse().unwrap(),
            FieldValue::Str(batch_name),
            true,
            false,
        ));

        let mut order_vec: Vec<OrderOption> = Vec::new();
        order_vec.push(OrderOption::new("createTime".parse().unwrap(), true));

        let new_sql = get_dynamic_sql_string(
            "GeneralExamPaperBatch".parse().unwrap(),
            Some(query_vec),
            Some(order_vec),
        );

        let mut result = db.query(&new_sql).await?;
        let list: Vec<GeneralExamPaperBatch> = result.take(0)?;
        Ok(list)
    }

    fn new(name: String) -> GeneralExamPaperBatch {
        let now = Utc::now().timestamp_millis() as u64;
        let id = SnowflakeGeneratorUtil::next();
        GeneralExamPaperBatch {
            id,
            name,
            paper_count: 0,
            answer_count: 0,
            is_deleted: 0,
            update_time: now,
            create_time: now,
            status: 0,
        }
    }

    async fn save(
        db: &Surreal<Db>,
        batch: &mut GeneralExamPaperBatch,
    ) -> Result<(), surrealdb::Error> {
        batch.update_time = Utc::now().timestamp() as u64;
        let value = serde_json::to_value(&batch).unwrap();
        let update_map = get_update_map(value);
        let result: Result<Option<GeneralExamPaperBatch>, surrealdb::Error> = db
            .update(("GeneralExamPaperBatch", batch.id))
            .merge(update_map)
            .await;
        match result {
            Ok(_) => Ok(()),
            Err(err) => Err(err),
        }
    }

    pub async fn recount(db: &Surreal<Db>, batch_id: u64) -> Result<(), surrealdb::Error> {
        let mut query_vec: Vec<QueryOption> = Vec::new();
        query_vec.push(QueryOption::new(
            "id".parse().unwrap(),
            FieldValue::Num(batch_id),
            false,
            true,
        ));
        let new_sql = get_dynamic_sql_string(
            "GeneralExamPaperBatch".parse().unwrap(),
            Some(query_vec),
            None,
        );
        let mut result = db.query(&new_sql).await?;
        let mut list: Vec<GeneralExamPaperBatch> = result.take(0)?;
        let mut batch = list.remove(0);

        let mut count_vec1: Vec<QueryOption> = Vec::new();
        count_vec1.push(QueryOption::new(
            "batchId".parse().unwrap(),
            FieldValue::Num(batch_id),
            false,
            false,
        ));
        let count_sql1 = get_dynamic_count_sql_string(
            "GeneralExamPaper".parse().unwrap(),
            Some(count_vec1),
            None,
        );
        let mut result2 = db.query(&count_sql1).await?;
        let paper_count: Option<u32> = result2.take(0)?;
        batch.paper_count = paper_count.unwrap();

        let mut count_vec2: Vec<QueryOption> = Vec::new();
        count_vec2.push(QueryOption::new(
            "batchId".parse().unwrap(),
            FieldValue::Num(batch_id),
            false,
            false,
        ));
        count_vec2.push(QueryOption::new(
            "isAnswer".parse().unwrap(),
            FieldValue::Bool(true),
            false,
            false,
        ));
        let count_sql2 = get_dynamic_count_sql_string(
            "GeneralExamPaper".parse().unwrap(),
            Some(count_vec2),
            None,
        );
        let mut result3 = db.query(&count_sql2).await?;
        let answer_count: Option<u32> = result3.take(0)?;
        batch.answer_count = answer_count.unwrap();

        GeneralExamPaperBatch::save(&db, &mut batch).await?;
        Ok(())
    }
}

pub async fn create(db: &Surreal<Db>, name: String) -> Result<(), String> {
    let batch = GeneralExamPaperBatch::new(name);
    let _record: Vec<Record> = db
        .create("GeneralExamPaperBatch")
        .content(batch)
        .await
        .map_err(|e| format!("SurrealDB error: {}", e))?;
    Ok(())
}

pub async fn update(db: &Surreal<Db>, name: String, id: u64) -> Result<(), String> {
    let existing_record: Option<GeneralExamPaperBatch> = db
        .select(("GeneralExamPaperBatch", id))
        .await
        .map_err(|e| format!("SurrealDB error: {}", e))?;
    let mut batch = existing_record.unwrap();
    batch.name = name;
    GeneralExamPaperBatch::save(&db, &mut batch)
        .await
        .map_err(|e| format!("SurrealDB error: {}", e))?;
    Ok(())
}

pub async fn list(
    db: &Surreal<Db>,
    batch_name: String,
) -> Result<Vec<GeneralExamPaperBatch>, String> {
    let list = GeneralExamPaperBatch::list(&db, batch_name)
        .await
        .map_err(|e| format!("SurrealDB error: {}", e))?;
    Ok(list)
}

use std::collections::HashSet;
use std::fs;
use std::io::Read;
use std::fs::File;
use std::io::Write;
use std::path::PathBuf;
use std::time::{Duration, Instant};
use lazy_static::lazy_static;
use serde_derive::{Deserialize, Serialize};
use tauri::{AppHandle, Emitter};
use tokio::sync::RwLock;
use tokio::time;
use walkdir::WalkDir;
use zip::write::SimpleFileOptions;
use zip::{CompressionMethod, ZipArchive, ZipWriter};
use crate::models::ResponseVO;
use crate::utils::app_handle::get_app_handle;
use crate::utils::path::{get_app_data_dir_if_custom, get_retrieval_dir, get_static_dir, get_surreal_db_dir, UserDataDirCustomConfig};
use crate::utils::time_util::{datetime_local_to_string, now_datetime_local};

lazy_static! {
    static ref G_STATUS_INFO: RwLock<BackupRecoverStatusInfo> = RwLock::new(Default::default());
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct BackupRecoverStatusInfo {
    // 状态枚举，0：当前没有备份和恢复；1：备份中；2：备份完成；3：备份出错；4：恢复中；5：恢复成功：6：恢复失败
    pub status: i8,
    #[serde(rename = "handledFileCount")]
    pub handled_file_count: usize,
    #[serde(rename = "fileCount")]
    pub file_count: usize,
    #[serde(rename = "timeSpendInSec")]
    pub time_spend_in_sec: u64,
    #[serde(rename = "msg")]
    pub opt_msg: Option<String>,
}

impl Default for BackupRecoverStatusInfo {
    fn default() -> Self {
        Self {
            status: 0,
            handled_file_count: 0,
            file_count: 0,
            time_spend_in_sec: 0,
            opt_msg: None,
        }
    }
}

pub struct BackupRecoverService;

impl BackupRecoverService {
    pub async fn get_status_info() -> Result<ResponseVO<BackupRecoverStatusInfo>, ResponseVO<()>> {
        let info = G_STATUS_INFO.read().await;
        Ok(ResponseVO::success(Some(info.clone()), None))
    }

    pub async fn backup(backup_file_path: &str) -> Result<ResponseVO<()>, ResponseVO<()>> {
        let backup_file_path = PathBuf::from(backup_file_path);
        let opt_bak_parent_dir = backup_file_path.parent();
        if opt_bak_parent_dir.is_none() {
            return Ok(ResponseVO::new(201, "找不到备份文件的父文件夹".to_string(), None));
        }
        let bak_parent_dir = opt_bak_parent_dir.unwrap();
        if !bak_parent_dir.exists() {
            let result = fs::create_dir_all(bak_parent_dir);
            if let Err(e) = result {
                return Ok(ResponseVO::new(201, e.to_string(), None));
            }
        }

        let result = get_surreal_db_dir();
        if let Err(e) = result {
            return Ok(ResponseVO::new(201, format!("获取surrealDB文件夹路径失败，msg: {}", e.to_string()), None));
        }
        let surreal_db_dir = result.unwrap();

        let result = get_retrieval_dir();
        if let Err(e) = result {
            return Ok(ResponseVO::new(201, format!("获取索引文件夹路径失败，msg: {}", e.to_string()), None));
        }
        let retrieval_dir = result.unwrap();

        let result = get_static_dir();
        if let Err(e) = result {
            return Ok(ResponseVO::new(201, format!("获取静态资源文件夹路径失败，msg: {}", e.to_string()), None));
        }
        let static_dir = result.unwrap();

        let result = get_app_handle();
        if let Err(_) = result {
            return Ok(ResponseVO::new(201, "无法获取到app_handle对象".to_string(), None));
        }
        let app_handle = result.unwrap();

        {
            let mut info = G_STATUS_INFO.write().await;
            if info.status == 1 || info.status == 4 {
                return Ok(ResponseVO::new(201, "当前正在备份或还原中，请耐心等候".to_string(), None));
            }
            // 修改状态为备份中，并初始化其他字段
            info.status = 1;
            info.file_count = 0;
            info.handled_file_count = 0;
            info.opt_msg = None;
        }

        // 开始计算已用时间
        let _ = Self::calculate_time_spend().await;

        let mut dir_list = vec![surreal_db_dir, retrieval_dir];
        if static_dir.exists() {
            dir_list.push(static_dir);
        }

        let mut excluded_file_path_set = HashSet::new();
        excluded_file_path_set.insert("surrealDB/LOCK");
        excluded_file_path_set.insert("surrealDB/LOG");

        std::thread::spawn(move || {
            // 通知开始统计文件数
            Self::modify_info_and_notify(&app_handle, None, Some("正在统计需要备份的文件数".to_string()), None, None);
            // 获取文件总数
            let mut file_count = 0usize;
            for dir in dir_list.iter() {
                let opt_parent_dir = dir.parent();
                if opt_parent_dir.is_none() {
                    Self::modify_info_and_notify(&app_handle, Some(3), Some(format!("找不到父文件夹路径：{}", dir.display())), None, None);
                    return;
                }
                let parent_dir = opt_parent_dir.unwrap();

                for result in WalkDir::new(dir).min_depth(0) {
                    if let Err(e) = result {
                        Self::modify_info_and_notify(&app_handle, Some(3), Some(format!("遍历文件时出错，msg: {}", e.to_string())), None, None);
                        return;
                    }
                    let entry = result.unwrap();
                    let path = entry.path();
                    // println!("备份文件：{}", path.display());

                    // 获取相对路径并处理分隔符
                    let result = path.strip_prefix(parent_dir);
                    if let Err(e) = result {
                        Self::modify_info_and_notify(&app_handle, Some(3), Some(format!("遍历文件获取相对路径时出错，msg: {}", e.to_string())), None, None);
                        return;
                    }
                    let relative_path = result.unwrap();
                    let zip_path = relative_path.to_string_lossy().replace("\\", "/");

                    // 跳过排除文件
                    if excluded_file_path_set.contains(zip_path.as_str()) {
                        continue;
                    }
                    // 文件数自增
                    file_count += 1;
                }
            }
            Self::modify_info_and_notify(&app_handle, None, None, Some(file_count), Some(0));

            // 创建ZIP文件
            let result = File::create(&backup_file_path);
            if let Err(e) = result {
                Self::modify_info_and_notify(&app_handle, Some(3), Some(format!("创建目标备份文件失败，msg: {}", e.to_string())), None, None);
                return;
            }
            let file = result.unwrap();

            let mut zip = ZipWriter::new(file);

            // 配置压缩选项（仅存储不压缩）
            let options = SimpleFileOptions::default()
                .compression_method(CompressionMethod::Stored)
                .large_file(true)
                .unix_permissions(0o755);

            let mut handled_file_count = 0usize;

            for dir in dir_list.iter() {
                let opt_parent_dir = dir.parent();
                if opt_parent_dir.is_none() {
                    Self::modify_info_and_notify(&app_handle, Some(3), Some(format!("找不到父文件夹路径：{}", dir.display())), None, None);
                    return;
                }
                let parent_dir = opt_parent_dir.unwrap();

                // 遍历源目录，不能跳过根目录本身（min_depth(0)）
                for result in WalkDir::new(dir).min_depth(0) {
                    if let Err(e) = result {
                        Self::modify_info_and_notify(&app_handle, Some(3), Some(format!("遍历文件时出错，msg: {}", e.to_string())), None, None);
                        return;
                    }
                    let entry = result.unwrap();
                    let path = entry.path();
                    // println!("备份文件：{}", path.display());

                    // 获取相对路径并处理分隔符
                    let result = path.strip_prefix(parent_dir);
                    if let Err(e) = result {
                        Self::modify_info_and_notify(&app_handle, Some(3), Some(format!("遍历文件获取相对路径时出错，msg: {}", e.to_string())), None, None);
                        return;
                    }
                    let relative_path = result.unwrap();
                    let mut zip_path = relative_path.to_string_lossy().replace("\\", "/");

                    // 跳过排除文件
                    if excluded_file_path_set.contains(zip_path.as_str()) {
                        continue;
                    }

                    let rt = tokio::runtime::Runtime::new().unwrap();
                    let canceled = rt.block_on(async {
                        G_STATUS_INFO.read().await.status == 0
                    });
                    // 如果状态突然变为0，说明是取消备份或取消恢复了
                    if canceled {
                        let _ = zip.finish();
                        let _ = fs::remove_file(&backup_file_path);
                        return;
                    }
                    drop(rt);

                    // 处理目录和文件
                    if entry.file_type().is_dir() {
                        zip_path.push('/'); // 目录路径以/结尾
                        let result = zip.add_directory(zip_path, options);
                        if let Err(e) = result {
                            Self::modify_info_and_notify(&app_handle, Some(3), Some(format!("写入文件夹路径时出错，msg: {}", e.to_string())), None, None);
                            return;
                        }
                    } else {
                        let result = zip.start_file(zip_path, options);
                        if let Err(e) = result {
                            Self::modify_info_and_notify(&app_handle, Some(3), Some(format!("写入文件时出错，msg: {}", e.to_string())), None, None);
                            return;
                        }
                        let result = File::open(path);
                        if let Err(e) = result {
                            Self::modify_info_and_notify(&app_handle, Some(3), Some(format!("写入文件时出错，msg: {}", e.to_string())), None, None);
                            return;
                        }
                        let mut f = result.unwrap();
                        let mut buffer = Vec::new();
                        let result = f.read_to_end(&mut buffer);
                        if let Err(e) = result {
                            Self::modify_info_and_notify(&app_handle, Some(3), Some(format!("写入文件时出错，msg: {}", e.to_string())), None, None);
                            return;
                        }
                        let result = zip.write_all(&buffer);
                        if let Err(e) = result {
                            Self::modify_info_and_notify(&app_handle, Some(3), Some(format!("写入文件时出错，msg: {}", e.to_string())), None, None);
                            return;
                        }
                    }
                    // 已处理文件数自增
                    handled_file_count += 1;

                    // 避免频繁通知前端
                    if handled_file_count % 20 == 0 {
                        Self::modify_info_and_notify(&app_handle, None, None, None, Some(handled_file_count));
                    }
                }
            }

            // 文件处理完成通知一次
            Self::modify_info_and_notify(&app_handle, None, None, None, Some(handled_file_count));

            let result = zip.finish();
            if let Err(e) = result {
                Self::modify_info_and_notify(&app_handle, Some(3), Some(format!("写入文件时出错，msg: {}", e.to_string())), None, None);
                return;
            }

            Self::modify_info_and_notify(&app_handle, Some(2), None, None, None);
        });

        Ok(ResponseVO::success(None, None))
    }

    pub async fn cancel_backup() -> Result<ResponseVO<()>, ResponseVO<()>> {
        let mut info = G_STATUS_INFO.write().await;
        info.status = 0;
        Ok(ResponseVO::success(None, None))
    }

    pub async fn recover(
        backup_file_path: &str, opt_user_data_custom_dir: Option<String>
    ) -> Result<ResponseVO<()>, ResponseVO<()>> {
        // 检测备份文件是否合法
        let backup_file_path = PathBuf::from(backup_file_path);
        if !backup_file_path.exists() || !backup_file_path.is_file() {
            return Ok(ResponseVO::new(201, "不是一个合法的备份文件".to_string(), None));
        }

        let udc_dir = opt_user_data_custom_dir.map(|s| PathBuf::from(s)).unwrap_or(
            get_app_data_dir_if_custom().map_err(|e| ResponseVO::error(Some(e.to_string())))?
        );
        let udc_parent_dir = udc_dir.parent().unwrap().to_path_buf();
        let now = now_datetime_local();
        let output_temp_dir_name = format!("BookGuard_UserData_{}", datetime_local_to_string(now));
        let output_temp_dir = udc_parent_dir.join(&output_temp_dir_name);
        if output_temp_dir.exists() {
            let result = fs::remove_dir_all(&output_temp_dir);
            if let Err(e) = result {
                return Ok(ResponseVO::new(201, format!("清空临时文件夹失败，msg: {}", e.to_string()), None));
            }
        }
        let result = fs::create_dir_all(&output_temp_dir);
        if let Err(e) = result {
            return Ok(ResponseVO::new(201, format!("创建临时文件夹失败，msg: {}", e.to_string()), None));
        }

        let result = get_app_handle();
        if let Err(_) = result {
            return Ok(ResponseVO::new(201, "无法获取到app_handle对象".to_string(), None));
        }
        let app_handle = result.unwrap();

        {
            let mut info = G_STATUS_INFO.write().await;
            if info.status == 1 || info.status == 4 {
                return Ok(ResponseVO::new(201, "当前正在备份或还原中，请耐心等候".to_string(), None));
            }
            // 修改状态为备份中，并初始化其他字段
            info.status = 4;
            info.file_count = 0;
            info.handled_file_count = 0;
            info.opt_msg = None;
        }

        // 开始计算已用时间
        let _ = Self::calculate_time_spend().await;

        std::thread::spawn(move || {
            Self::modify_info_and_notify(&app_handle, None, Some("正在校验备份文件格式".to_string()), None, None);

            let result = File::open(&backup_file_path);
            if let Err(e) = result {
                Self::modify_info_and_notify(&app_handle, Some(6), Some(format!("读取备份文件时出错，msg: {}", e.to_string())), None, None);
                return;
            }
            let file = result.unwrap();

            let result = ZipArchive::new(file);
            if let Err(e) = result {
                Self::modify_info_and_notify(&app_handle, Some(6), Some(format!("读取备份文件时出错，msg: {}", e.to_string())), None, None);
                return;
            }
            let mut zip = result.unwrap();

            // 简单检测下该文件是否合法
            let found_surreal_db = zip.by_name("surrealDB/").map(|_| true).unwrap_or(false);
            let found_retrieval = zip.by_name("retrieval/").map(|_| true).unwrap_or(false);
            if !found_surreal_db || !found_retrieval {
                Self::modify_info_and_notify(&app_handle, Some(6), Some("不是一个合法的备份文件".to_string()), None, None);
                return;
            }

            let file_count = zip.len();
            Self::modify_info_and_notify(&app_handle, None, None, Some(file_count), None);

            let mut handle_file_count = 0usize;

            for index in 0..zip.len() {
                let rt = tokio::runtime::Runtime::new().unwrap();
                let canceled = rt.block_on(async {
                    G_STATUS_INFO.read().await.status == 0
                });
                // 如果状态突然变为0，说明是取消备份或取消恢复了
                if canceled {
                    let _ = fs::remove_dir_all(&output_temp_dir);
                    return;
                }
                drop(rt);

                let result = zip.by_index(index)
                    .map_err(|e| e.to_string());
                if let Err(e) = result {
                    Self::modify_info_and_notify(&app_handle, Some(6), Some(format!("读取备份文件时出错，msg: {}", e)), None, None);
                    return;
                }
                let mut file = result.unwrap();

                let path = file.name();
                // println!("恢复文件：{}", file.name());
                let file_temp_path = output_temp_dir.join(path);

                if file.is_dir() {
                    let result = fs::create_dir_all(&file_temp_path);
                    if let Err(e) = result {
                        Self::modify_info_and_notify(&app_handle, Some(6), Some(e.to_string()), None, None);
                        return;
                    }
                } else if file.is_file() {
                    // 读取文件内容
                    let mut file_content = Vec::new();
                    let result = file.read_to_end(&mut file_content)
                        .map_err(|e| e.to_string());
                    if let Err(e) = result {
                        Self::modify_info_and_notify(&app_handle, Some(6), Some(format!("读取备份文件时出错，msg: {}", e.to_string())), None, None);
                        return;
                    }

                    let result = file_temp_path.parent()
                        .ok_or("找不到父文件夹".to_string());
                    if let Err(e) = result {
                        Self::modify_info_and_notify(&app_handle, Some(6), Some(e), None, None);
                        return;
                    }
                    let file_temp_parent_path = result.unwrap();
                    if !file_temp_parent_path.exists() {
                        let result = fs::create_dir_all(&file_temp_parent_path);
                        if let Err(e) = result {
                            Self::modify_info_and_notify(&app_handle, Some(6), Some(e.to_string()), None, None);
                            return;
                        }
                    }

                    let result = fs::write(&file_temp_path, &file_content);
                    if let Err(e) = result {
                        Self::modify_info_and_notify(&app_handle, Some(6), Some(format!("写入文件时出错，msg: {}", e.to_string())), None, None);
                        return;
                    }
                }

                // 自增1
                handle_file_count += 1;
                // 避免频繁通知前端
                if handle_file_count % 20 == 0 {
                    Self::modify_info_and_notify(&app_handle, None, None, None, Some(handle_file_count));
                }
            }

            // 再通知一次文件同步数量
            Self::modify_info_and_notify(&app_handle, None, None, None, Some(handle_file_count));

            // 设置自定义用户数据目录
            let config = UserDataDirCustomConfig { enable: true, path: output_temp_dir.display().to_string() };
            let result = crate::utils::path::set_user_data_dir_custom_config(config);
            if let Err(e) = result {
                Self::modify_info_and_notify(&app_handle, Some(6), Some(format!("更改用户数据文件夹时出错，msg: {}", e.to_string())), None, None);
                return;
            }

            Self::modify_info_and_notify(&app_handle, Some(5), None, None, None);
        });

        Ok(ResponseVO::success(None, None))
    }

    pub async fn cancel_recover() -> Result<ResponseVO<()>, ResponseVO<()>> {
        let mut info = G_STATUS_INFO.write().await;
        info.status = 0;
        Ok(ResponseVO::success(None, None))
    }

    pub async fn in_backup_or_recover() -> bool {
        let info = G_STATUS_INFO.read().await;
        info.status == 1 || info.status == 4
    }

    async fn calculate_time_spend() -> Result<(), Box<dyn std::error::Error>> {
        let app_handle = get_app_handle()?;
        tokio::spawn(async move {
            // 记录开始时间
            let start = Instant::now();
            loop {
                {
                    let mut info = G_STATUS_INFO.write().await;
                    if info.status != 1 && info.status != 4 {
                        break;
                    }
                    // 计算耗时
                    let duration = start.elapsed();
                    info.time_spend_in_sec = duration.as_secs();
                }
                let _ = app_handle.emit("bak-rcv-info-changed", "");
                time::sleep(Duration::from_secs(1)).await;
            }
        });

        Ok(())
    }

    async fn modify_info_and_notify_async(
        app_handle: &AppHandle,
        opt_status: Option<i8>, opt_msg: Option<String>,
        opt_file_count: Option<usize>, opt_h_f_count: Option<usize>
    ) {
        let mut info = G_STATUS_INFO.write().await;
        if let Some(status) = opt_status {
            info.status = status;
        }
        info.opt_msg = opt_msg;
        if let Some(file_count) = opt_file_count {
            info.file_count = file_count;
        }
        if let Some(h_f_count) = opt_h_f_count {
            info.handled_file_count = h_f_count;
        }
        let _ = app_handle.emit("bak-rcv-info-changed", "");
    }

    fn modify_info_and_notify(
        app_handle: &AppHandle,
        opt_status: Option<i8>, opt_msg: Option<String>,
        opt_file_count: Option<usize>, opt_h_f_count: Option<usize>
    ) {
        let rt = tokio::runtime::Runtime::new().unwrap();
        rt.block_on(async {
            Self::modify_info_and_notify_async(app_handle, opt_status, opt_msg, opt_file_count, opt_h_f_count).await;
        });
    }
}
use regex::Regex;
use std::collections::HashMap;
use serde_derive::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Sentence {
    pub content: String,
    pub book_id: String,
    pub book_name: String,
    pub page: i32,
    pub card_id: Option<u64>, // 添加card_id字段
    pub start_pos: usize,
    pub end_pos: usize,
}

#[derive(Clone)]
pub struct SentenceExtractor {
    min_length: usize,
    sentence_regex: Regex,
}

impl SentenceExtractor {
    pub fn new(min_length: usize) -> Self {
        // 中文句子分割正则表达式，匹配句号、问号、感叹号等
        let sentence_regex = Regex::new(r"[。！？；\.\!\?;]+").unwrap();
        
        Self {
            min_length,
            sentence_regex,
        }
    }
    /// 从单页内容中提取句子
    fn extract_sentences_from_page(&self, book_id: &str, book_name: &str, content: &str, page: i32, card_id: Option<u64>) -> Vec<Sentence> {
        let mut sentences = Vec::new();
        let mut current_pos = 0;

        // 使用正则表达式分割句子
        for mat in self.sentence_regex.find_iter(content) {
            let sentence_end = mat.end();
            let sentence_content = content[current_pos..sentence_end].trim();

            // 过滤掉太短的句子和只包含标点符号的句子
            if sentence_content.len() >= self.min_length && self.is_valid_sentence(sentence_content) {
                sentences.push(Sentence {
                    content: sentence_content.to_string(),
                    book_id: book_id.to_string(),
                    book_name: book_name.to_string(),
                    page,
                    card_id,
                    start_pos: current_pos,
                    end_pos: sentence_end,
                });
            }

            current_pos = sentence_end;
        }

        // 处理最后一个句子（如果没有以标点符号结尾）
        if current_pos < content.len() {
            let sentence_content = content[current_pos..].trim();
            if sentence_content.len() >= self.min_length && self.is_valid_sentence(sentence_content) {
                sentences.push(Sentence {
                    content: sentence_content.to_string(),
                    book_id: book_id.to_string(),
                    book_name: book_name.to_string(),
                    page,
                    card_id,
                    start_pos: current_pos,
                    end_pos: content.len(),
                });
            }
        }

        sentences
    }

    fn is_valid_sentence(&self, sentence: &str) -> bool {
        // 检查句子是否包含足够的汉字或字母
        let char_count = sentence.chars()
            .filter(|c| c.is_alphabetic() || self.is_chinese_char(*c))
            .count();
        
        // 至少包含5个有效字符
        char_count >= 5
    }

    /// 判断是否为中文字符
    fn is_chinese_char(&self, c: char) -> bool {
        matches!(c, '\u{4e00}'..='\u{9fff}')
    }

    /// 从单个卡片中提取句子
    pub fn extract_sentences_from_card(&self, book_id: &str, book_name: &str, card: &crate::models::card::Card) -> Vec<Sentence> {
        let page = card.page.unwrap_or(0);
        let card_id = Some(card.card_id);

        self.extract_sentences_from_page(
            book_id,
            book_name,
            &card.content,
            page,
            card_id,
        )
    }

    /// 从卡片数据中批量提取多本书的句子
    pub fn extract_sentences_from_cards(&self, books_cards: &HashMap<String, (String, Vec<crate::models::card::Card>)>) -> HashMap<String, Vec<Sentence>> {
        let mut all_sentences = HashMap::new();

        for (book_id, (book_name, cards)) in books_cards {
            let mut book_sentences = Vec::new();

            for card in cards {
                let card_sentences = self.extract_sentences_from_card(book_id, book_name, card);
                book_sentences.extend(card_sentences);
            }

            all_sentences.insert(book_id.clone(), book_sentences);
        }

        all_sentences
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sentence_extraction() {
        let extractor = SentenceExtractor::new(10);
        let content = "这是第一个句子。这是第二个句子！这是第三个句子？这是一个很短的句子。";
        
        let sentences = extractor.extract_sentences_from_page("book1", "测试书籍", content, 1, None);
        
        assert_eq!(sentences.len(), 4);
        assert_eq!(sentences[0].content, "这是第一个句子。");
        assert_eq!(sentences[1].content, "这是第二个句子！");
    }
    }

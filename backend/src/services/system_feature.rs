use lazy_static::lazy_static;
use serde::de;
use serde_derive::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::sync::RwLock;
use crate::models::ResponseVO;
use crate::utils::path::{get_app_data_dir, get_app_data_dir_custom_config};
use crate::vo::path::UserDataDirCustomConfigVo;

pub trait SysFeatUtil {
    fn get_id() -> String;
    fn to_db_entity(&self) -> SystemFeature;
}

// app用法模式：分为：离线版、客户端、服务器
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct UsageMode {
    // 枚举类型：0：离线版；1：客户端；2：服务器
    pub mode: i8,
    pub url: String,
    pub port: u16,
}

impl UsageMode {
    #[allow(dead_code)]
    pub fn is_offline(&self) -> bool { self.mode == 0 }
    pub fn is_client(&self) -> bool { self.mode == 1 }
    pub fn is_server(&self) -> bool { self.mode == 2 }
}

impl SysFeatUtil for UsageMode {
    fn get_id() -> String {
        "UsageMode".to_string()
    }

    fn to_db_entity(&self) -> SystemFeature {
        SystemFeature {
            feat_id: Self::get_id(),
            value: serde_json::to_string(self).unwrap(),
            note: "作为服务端，允许局域网访问配置".to_string(),
        }
    }
}

impl Default for UsageMode {
    fn default() -> Self {
        Self {
            mode: 0,
            url: "".to_string(),
            port: 2697,
        }
    }
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct SystemFeatureDetails {
    #[serde(rename = "userDataDirCustomConfig")]
    pub user_data_dir_custom_config: UserDataDirCustomConfigVo,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct SystemFeature {
    #[serde(rename = "featId")]
    pub feat_id: String,
    pub value: String,
    pub note: String,
}

lazy_static! {
    static ref G_SYS_FEAT_MAP: RwLock<Option<HashMap<String, SystemFeature>>> =
        RwLock::new(None);
}

impl SystemFeature {
    pub fn get_system_feat_details() -> Result<ResponseVO<SystemFeatureDetails>, ResponseVO<()>> {
        let addcc = get_app_data_dir_custom_config()
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        Ok(ResponseVO::success(Some(SystemFeatureDetails {
            user_data_dir_custom_config: UserDataDirCustomConfigVo::from(&addcc),
        }), None))
    }

    fn find_by_id(id: &str) -> Result<Option<Self>, Box<dyn std::error::Error>> {
        let opt_sys_feat_map = { G_SYS_FEAT_MAP.read()?.clone() };
        if opt_sys_feat_map.is_none() {
            let app_data_dir = get_app_data_dir()?;
            let json_path = app_data_dir.join(".sys_feats.json");
            let sys_feats = if json_path.exists() {
                let json_content = fs::read_to_string(json_path)?;
                let sys_feats: Vec<SystemFeature> = serde_json::from_str(&json_content)?;
                sys_feats
            } else {
                let temp_feats: Vec<SystemFeature> = vec![];
                let json_content = serde_json::to_string_pretty(&temp_feats)?;
                fs::write(json_path, &json_content)?;
                temp_feats
            };
            let mut temp_map = HashMap::new();
            for feat in sys_feats {
                temp_map.insert(feat.feat_id.clone(), feat);
            }
            let mut opt_sys_feats_map_writer = G_SYS_FEAT_MAP.write()?;
            *opt_sys_feats_map_writer = Some(temp_map);
            Ok(opt_sys_feats_map_writer.as_ref().unwrap().get(id).cloned())
        } else {
            let sys_feat_map = opt_sys_feat_map.as_ref().unwrap();
            Ok(sys_feat_map.get(id).cloned())
        }
    }

    pub fn set_sys_feat_owned<T: Default + SysFeatUtil + de::DeserializeOwned>(
        sys_feat_owned: T,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let new_feat = sys_feat_owned.to_db_entity();
        let mut opt_sys_feat_map = G_SYS_FEAT_MAP.write()?;
        if opt_sys_feat_map.is_none() {
            let mut temp_map = HashMap::new();
            temp_map.insert(new_feat.feat_id.clone(), new_feat);
            *opt_sys_feat_map = Some(temp_map);
        } else {
            opt_sys_feat_map.as_mut().unwrap().insert(new_feat.feat_id.clone(), new_feat);
        };
        let app_data_dir = get_app_data_dir()?;
        let sys_feats: Vec<SystemFeature> = opt_sys_feat_map.as_ref().unwrap()
            .values()
            .map(|feat| feat.clone())
            .collect();
        let json_path = app_data_dir.join(".sys_feats.json");
        let json_content = serde_json::to_string_pretty(&sys_feats)?;
        fs::write(json_path, &json_content)?;
        Ok(())
    }

    pub fn get_sys_feat_owned<T: Default + SysFeatUtil + de::DeserializeOwned>() -> Result<T, Box<dyn std::error::Error>> {
        let id = T::get_id();
        let opt_sys_feat = Self::find_by_id(&id)?;
        return if let Some(sys_feat) = opt_sys_feat {
            Ok(serde_json::from_str::<T>(sys_feat.value.as_str())?)
        } else {
            let mut opt_sys_feat_map = G_SYS_FEAT_MAP.write()?;
            if opt_sys_feat_map.is_none() {
                *opt_sys_feat_map = Some(HashMap::new());
            }
            let new_feat = T::default().to_db_entity();
            opt_sys_feat_map.as_mut().unwrap().insert(new_feat.feat_id.clone(), new_feat.clone());
            let sys_feats: Vec<SystemFeature> = opt_sys_feat_map.as_ref().unwrap()
                .values()
                .map(|feat| feat.clone())
                .collect();
            let app_data_dir = get_app_data_dir()?;
            let json_path = app_data_dir.join(".sys_feats.json");
            let json_content = serde_json::to_string_pretty(&sys_feats)?;
            fs::write(json_path, &json_content)?;
            Ok(serde_json::from_str::<T>(new_feat.value.as_str())?)
        };
    }
}

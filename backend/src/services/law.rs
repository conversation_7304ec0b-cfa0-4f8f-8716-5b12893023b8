use crate::database::law;
use crate::models::law::{GetLaws, QueryLaws};
use crate::models::ResponseVO;

pub async fn get_law(
    params: QueryLaws
) -> Result<ResponseVO<GetLaws>, ResponseVO<()>> {
    match law::get_law(params).await {
        Ok(result) => Ok(ResponseVO::success(Some(result), None)),
        Err(error) => Err(ResponseVO::error(Some(error.to_string()))),
    }
}
pub async fn get_law_total() -> Result<ResponseVO<i32>, ResponseVO<()>> {
    match law::get_law_total().await {
        Ok(result) => Ok(ResponseVO::success(Some(result), None)),
        Err(error) => Err(ResponseVO::error(Some(error.to_string()))),
    }
}
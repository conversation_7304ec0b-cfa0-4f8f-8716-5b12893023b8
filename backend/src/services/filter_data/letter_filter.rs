use std::collections::HashMap;
use indexmap::IndexMap;
use lazy_static::lazy_static;
use tokio::sync::RwLock;
use crate::database::letter::LetterRepository;
use crate::models::letter::Letter;


lazy_static! {
    static ref G_LETTER_FILTER: RwLock<LetterFilter> = RwLock::new(LetterFilter::new());
}

pub(crate) struct LetterFilter {
    book_id_letter_map: HashMap<u64, IndexMap<u64, Letter>>,
}


impl LetterFilter {
    fn new() -> Self {
        Self {
            book_id_letter_map: HashMap::new(),
        }
    }

    pub(crate) async fn load() -> Result<(), Box<dyn std::error::Error>> {
        let letters = LetterRepository::find_all().await?;
        let mut book_id_letter_map: HashMap<u64, IndexMap<u64, Letter>> = HashMap::new();
        for letter in letters {
            book_id_letter_map.entry(letter.book_id)
                .or_insert(IndexMap::new())
                .insert(letter.letter_id, letter);
        }
        let mut filter = G_LETTER_FILTER.write().await;
        filter.book_id_letter_map = book_id_letter_map;
        Ok(())
    }

    pub(crate) async fn update(letters: &Vec<Letter>) -> Result<(), Box<dyn std::error::Error>> {
        let mut filter = G_LETTER_FILTER.write().await;
        for letter in letters {
            filter.book_id_letter_map.entry(letter.book_id)
                .or_insert(IndexMap::new())
                .insert(letter.letter_id, letter.clone());
        }
        Ok(())
    }

    pub(crate) async fn filter_letters<P>(
        book_id: u64, predict: P
    ) -> Vec<Letter>
    where
        P: Fn(&Letter) -> bool,
    {
        let filter = G_LETTER_FILTER.read().await;
        if let Some(letter_map ) = filter.book_id_letter_map.get(&book_id) {
            letter_map.values().filter(|le| predict(*le)).map(|le| le.clone()).collect()
        } else {
            vec![]
        }
    }
}
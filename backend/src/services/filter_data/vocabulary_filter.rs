use std::collections::HashMap;
use indexmap::IndexMap;
use lazy_static::lazy_static;
use tokio::sync::RwLock;
use crate::database::vocabulary::VocabularyRepository;
use crate::models::vocabulary::Vocabulary;

lazy_static! {
    static ref G_VOS_FILTER: RwLock<VocabularyFilter> = RwLock::new(VocabularyFilter::new());
}

pub(crate) struct VocabularyFilter {
    book_id_voc_map: HashMap<u64, IndexMap<u64, Vocabulary>>,
}


impl VocabularyFilter {
    fn new() -> Self {
        Self {
            book_id_voc_map: HashMap::new(),
        }
    }

    pub(crate) async fn load() -> Result<(), Box<dyn std::error::Error>> {
        let vos = VocabularyRepository::find_all().await?;
        let mut book_id_voc_map: HashMap<u64, IndexMap<u64, Vocabulary>> = HashMap::new();
        for v in vos {
            book_id_voc_map.entry(v.book_id)
                .or_insert(IndexMap::new())
                .insert(v.v_id, v);
        }
        let mut filter = G_VOS_FILTER.write().await;
        filter.book_id_voc_map = book_id_voc_map;
        Ok(())
    }

    pub(crate) async fn update(vos: &Vec<Vocabulary>) -> Result<(), Box<dyn std::error::Error>> {
        let mut filter = G_VOS_FILTER.write().await;
        for v in vos {
            filter.book_id_voc_map.entry(v.book_id)
                .or_insert(IndexMap::new())
                .insert(v.v_id, v.clone());
        }
        Ok(())
    }

    pub(crate) async fn filter_voc_list<P>(
        book_id: u64, predict: P
    ) -> Vec<Vocabulary>
    where
        P: Fn(&Vocabulary) -> bool,
    {
        let filter = G_VOS_FILTER.read().await;
        if let Some(voc_map ) = filter.book_id_voc_map.get(&book_id) {
            voc_map.values().filter(|v| predict(*v)).map(|v| v.clone()).collect()
        } else {
            vec![]
        }
    }
}
use crate::database::book;
use crate::models::book::{Book, QueryDate, QueryNews};
use serde_derive::{Deserialize, Serialize};
use crate::database::book::BookRepository;
use crate::database::Pagination;
use crate::models::ResponseVO;
use crate::services::PageResult;
use crate::vo::book::BookVo;

pub(crate) struct BookService;

#[derive(Deserialize, Serialize, Clone, Debug)]
pub(crate) struct BookPageByTypesParams {
    #[serde(rename = "searchText")]
    pub search_text: String,
    #[serde(rename = "bookTypes")]
    pub book_types: Vec<String>,
    #[serde(rename = "pageNo")]
    pub opt_page_no: Option<usize>,
    #[serde(rename = "pageSize")]
    pub opt_page_size: Option<usize>,
}

impl BookService {
    pub async fn get_book_page_by_types(params: BookPageByTypesParams) -> Result<ResponseVO<PageResult<BookVo>>, ResponseVO<()>> {
        let page = Pagination::new(params.opt_page_no, params.opt_page_size);
        BookRepository::page_by_type_in(&params.search_text, &params.book_types, page).await
            .map(|r| ResponseVO::success(Some(PageResult::new(r.totals, r.page_no, r.page_size, BookVo::from_list(&r.list))), None))
            .map_err(|e| ResponseVO::error(Some(e.to_string())))
    }

    pub async fn get_book_vo_by_id(book_id: String) -> Result<ResponseVO<BookVo>, ResponseVO<()>> {
        let books = book::get_book_by_book_id(book_id).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        books.first().map(|b| ResponseVO::success(Some(BookVo::from(b)), None))
            .ok_or(ResponseVO::error(Some("Not found".to_string())))
    }
}

pub async fn get_book_by_book_id(
    book_id: String
) -> Result<ResponseVO<Vec<Book>>, ResponseVO<()>> {
    match book::get_book_by_book_id(book_id).await {
        Ok(result) => Ok(ResponseVO::success(Some(result), None)),
        Err(error) => Err(ResponseVO::error(Some(error.to_string()))),
    }
}

pub async fn get_book_news_info_by_brand_pub_date(
    params: QueryNews
) -> Result<ResponseVO<Vec<QueryDate>>, ResponseVO<()>> {
    match book::get_book_news_info_by_brand_pub_date(params).await {
        Ok(result) => Ok(ResponseVO::success(Some(result), None)),
        Err(error) => Err(ResponseVO::error(Some(error.to_string()))),
    }
}
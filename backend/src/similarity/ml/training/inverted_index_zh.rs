use std::{fs, u64};
use std::collections::{HashMap, HashSet};
use std::path::PathBuf;
use std::time::Instant;
use mongodb::Database;
use serde_pickle;
use crate::app::ml::retrieval::retrieval4math::utils::{is_number, tokenize_text};
use crate::app::ml::training::inverted_index::InvertedIndex;
use crate::app::models::dataloader::question_loader::QuestionLoader;
use crate::app::service::paper_filter::FilterItem;

pub struct InvertedIndexZh {
    subject_code: String,
    model: String,
    root: PathBuf,
    im: InvertedIndex,
}

impl InvertedIndexZh {
    pub async fn new(subject_code: String, model: String, batch: Option<String>, root: &str, db:Database) -> Self {
        let stop_words_path = format!("{}/{}", root, "stop_words.txt");
        let stop_words = fs::read_to_string(stop_words_path).unwrap_or_else(|_| String::new()).lines().map(|line| line.to_string()).collect::<HashSet<String>>();
        let data_loader = QuestionLoader::new(subject_code.clone(), Some(model.clone()), batch, false,db.clone()).await;
        let mut root_path = PathBuf::from(root);
        root_path.push("model_files");
        root_path.push(subject_code.to_string());
        root_path.push(model.clone());
        let im = InvertedIndex::new(stop_words, root_path.clone(), data_loader,db);
        InvertedIndexZh { subject_code, model, root: root_path, im }
    }

    pub fn training(&self) {
        self.im.construct_postings_lists(gen_features);
    }

    pub async fn predict(&mut self, question: &str, filter_item: Option<FilterItem>) -> (Vec<(u32, f64, u32, i32, u32)>, Vec<String>) {
        let start = Instant::now();
        self.im.load_param().await;
        println!("load_param elapse {:?}", start.elapsed());
        let start = Instant::now();
        let result= self.im.search_for_query(question, gen_features, true, filter_item, 0).await;
        println!("search elapse {:?}", start.elapsed());
        result
    }

    fn similarity(&self, question: &str, filter_item: Option<&str>) -> Vec<String> {
        self.im.similar_question(question, gen_features, filter_item)
    }
}

fn gen_features(text: &str, stop_words: &HashSet<String>) -> HashMap<String, u64> {
    let feature_list = tokenize_text(text, true);
    let mut cleaned_dict = HashMap::new();
    for mut f in feature_list {
        let i = f.trim().to_lowercase();
        if i != "" && !is_number(&i) && !stop_words.contains(&i.to_string()) {
            *cleaned_dict.entry(i.to_string()).or_insert(0) += 1;
        }
    }

    cleaned_dict
}
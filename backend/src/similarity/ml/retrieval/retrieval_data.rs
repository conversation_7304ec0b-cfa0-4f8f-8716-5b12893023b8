use std::fs;
use std::collections::HashMap;
use std::path::PathBuf;

use futures::AsyncWriteExt;
// use hdf5::File;
use log::info;
use ndarray::{Array, Array2};
use serde::{Deserialize, Serialize};
use serde_pickle::DeOptions;

#[derive(Deserialize, Serialize)]
pub struct RetrievalData {
    feats_path: PathBuf,
    h5_path: PathBuf,
}

// impl RetrievalData {
//     pub fn new(path: PathBuf) -> Result<Self, &'static str> {
//         if !path.to_string_lossy().ends_with(".pickle") {
//             return Err("Provided path does not end with '.pickle'");
//         }
//
//         let mut h5_path = path.clone();
//         h5_path.set_extension("h5");
//
//         Ok(Self {
//             feats_path: path,
//             h5_path,
//         })
//     }
//
//     pub fn load_inverted_map(&self) -> (HashMap<String, Vec<(u64, u32, u32)>>, f64, f64) {
//         let file = fs::File::open(&self.feats_path).expect("Unable to open file");
//         let mut feats_map: HashMap<u64, HashMap<String, u32>> = serde_pickle::from_reader(file, DeOptions::default()).expect("Failed to deserialize the pickle data");
//         RetrievalData::feats_to_inverted_map(&mut feats_map)
//     }
//
//     pub fn feats_to_inverted_map(feats_map: &mut HashMap<u64, HashMap<String, u32>>) -> (HashMap<String, Vec<(u64, u32, u32)>>, f64, f64) {
//         let n = feats_map.len() as f64;
//         let mut total_ld = 0.0;
//         let mut inverted_map = HashMap::new();
//
//         for key in feats_map.keys().cloned().collect::<Vec<_>>() {
//             if let Some(mut cleaned_dict) = feats_map.remove(&key) {
//                 cleaned_dict.remove("\x00");
//                 let ld: u32 = cleaned_dict.values().sum();
//                 total_ld += ld as f64;
//                 for (term, &value) in &cleaned_dict {
//                     inverted_map.entry(term.clone()).or_insert_with(Vec::new).push((key, value, ld));
//                 }
//             }
//         }
//
//         (inverted_map, n, total_ld / n)
//     }
//
//     pub fn store_to_h5(&self) {
//         let file = fs::File::open(&self.feats_path).expect("Unable to open file");
//         let feats_map: HashMap<u64, HashMap<String, u32>> = serde_pickle::from_reader(file, DeOptions::default()).expect("Failed to deserialize the pickle data");
//         self.feats_map_to_h5(feats_map).expect("TODO: panic message");
//     }
//
//     pub fn feats_map_to_h5(&self, mut feats_map: HashMap<u64, HashMap<String, u32>>) -> Result<(), Box<dyn std::error::Error>> {
//         let (mut inverted_map, n, avg) = Self::feats_to_inverted_map(&mut feats_map);
//
//         if self.h5_path.exists() {
//             fs::remove_file(&self.h5_path).unwrap();
//         }
//
//         // let h5_file = File::create(&self.h5_path).unwrap();
//         // Create the dataset and write data
//         // h5_file.new_dataset::<usize>().shape((2, )).create("qc_ld").unwrap().write(&Array::from(vec![n, avg])).expect("TODO: panic message");
//
//         info!("store inverted_map to {:?}, length {}", self.h5_path, inverted_map.len());
//
//         // Iterate over the inverted_map
//         for term in inverted_map.keys().cloned().collect::<Vec<String>>() {
//             let v = inverted_map.remove(&term).unwrap();
//             // Process the term to make it a valid HDF5 key
//             let key = term.replace("/", " ");
//             // Convert the value to a 2D ndarray with u32 type
//             let feats: Vec<_> = v.iter().flat_map(|&(a, b, c)| vec![a as u32, b as u32, c as u32]).collect();
//             let feats_array = Array2::from_shape_vec((v.len(), 3), feats).unwrap();
//             // Create a dataset with the processed key and store the ndarray
//             // h5_file.new_dataset::<u32>().shape((v.len(), 3)).create(key.as_str()).unwrap().write(&feats_array).unwrap();
//         }
//         Ok(())
//     }
// }

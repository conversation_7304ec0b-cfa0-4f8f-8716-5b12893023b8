use std::clone::<PERSON>lone;
use std::collections::HashSet;
use std::iter::Iterator;
use std::string::ToString;
use jieba_rs::Jieba;
use lazy_static::lazy_static;
use regex::{Captures, Regex};
lazy_static! {
    pub static ref PUNCTUATIONS: HashSet<char> = {
        let punctuations = "` `` \" — ‘ … : ; ? . 。 ， , 、 & ! # ？ ！   \n Ⅰ Ⅱ ⅰ ⅱ Ⅲ Ⅳ ⅲ ⅳ";
        punctuations.chars().collect()
    };

    pub static ref FILTER_WORDS: &'static str = r"abcdefghijklmnopqrstuvwxyz0123456789";
    pub static ref SPECIAL_SIGNS: &'static str = r"∩＜⊥∈△πθβγα∠ω→←⩽";
    pub static ref EXPAND_SIGNS: String = format!("{}{}{}", *FILTER_WORDS, r"\'@#$%^_\[\]|{}()=+-*\\/.,<> ", *SPECIAL_SIGNS);
}


pub fn tokenize_text(text: &str, if_clean: bool) -> Vec<String> {
    let mut text = text.to_string();
    if if_clean {
        text = clean_string(&text);
    }

    let pre_sentence = find_all_sentence(&text, " ");
    let (candidate_latex, sentence) = translate_char2latex(&text, Some(pre_sentence));
    let sentence = clean_sentence(&sentence);
    let jie_ba = Jieba::new();
    let sent_jie_ba = sentence.clone();
    let sent_list = jie_ba.cut_for_search(sent_jie_ba.as_str(), true);
    let latex = findall_latex(&text);

    let mut split_latex_list = Vec::new();
    for l in &latex {
        let (_, nl) = split_text_and_latex(l);
        split_latex_list.extend(nl);
    }

    let mut latex = split_latex_list;
    let expand_latex = bidirection_expand_latex(&text);
    for exp_lat in expand_latex {
        if !latex.contains(&exp_lat) {
            latex.push(exp_lat);
        }
    }

    let lat_list = tokenize_latex(latex);
    let candidate_lat_list = if candidate_latex.len() > 0 {
        tokenize_latex(candidate_latex)
    } else {
        Vec::new()
    };

    let mut token_list:Vec<String> = Vec::new();
    token_list.extend(sent_list.iter().map(|s|s.to_string()).collect::<Vec<_>>());
    token_list.extend(lat_list.iter().map(|s|s.to_string()).collect::<Vec<_>>());
    token_list.extend(candidate_lat_list.iter().map(|s|s.to_string()).collect::<Vec<_>>());
    token_list
}

pub fn clean_string(line: &str) -> String {
    let mut line = str_q_2_b(line);
    line = normalized_punctuations(&line);
    line = line.trim().to_lowercase();
    line = line.replace("&nbsp;", "");
    line = remove_html_tag(&line);
    line = handle_parse_error(&line);
    line = line.replace("\n", " ");
    line = line.trim().to_string();
    line = line.split_whitespace().collect::<Vec<&str>>().join(" ");
    return line;
}

fn str_q_2_b(ustring: &str) -> String {
    let mut rstring = String::new();

    for uchar in ustring.chars() {
        let mut inside_code = uchar as u32;

        if inside_code == 12288 {  // 全角空格直接转换
            inside_code = 32;
        } else if inside_code >= 65281 && inside_code <= 65374 {  // 全角字符（除空格）根据关系转化
            inside_code -= 65248;
        }

        rstring.push(char::from_u32(inside_code).unwrap());
    }
    rstring
}

fn normalized_punctuations(text: &str) -> String {
    let text = Regex::new(r"﹣").unwrap().replace_all(text, "-").to_string();
    let text = Regex::new(r"＋").unwrap().replace_all(&text, "+").to_string();
    text
}

fn remove_html_tag(line: &str) -> String {
    let patterns = [
        r"<sub.*?>", r"</sub>",
        r"<em.*?>", r"</em>",
        r"<span.*?>", r"</span>",
        r"<img.*?>",
        r"<strong.*?>", r"</strong>",
        r"<sup.*?>", r"</sup>",
        r"<div.*?>", r"</div>",
        r"\[math_type_convert_fail_.*?bin\]",
    ];

    let mut result = line.to_string();
    for pattern in patterns.iter() {
        let re = Regex::new(pattern).unwrap();
        result = re.replace_all(&result, "").to_string();
    }
    result
}

fn handle_parse_error(line: &str) -> String {
    let replacements = [
        (r"([0-9a-zA-Z]*)α([0-9a-zA-Z]*)", r"\1\\alpha \2"),
        (r"([0-9a-zA-Z]*)β([0-9a-zA-Z]*)", r"\1\\beta \2"),
        // Uncomment the line below if you also want to replace △ with \triangle
        // (r"([0-9a-zA-Z]*)△([0-9a-zA-Z]*)", r"\1\\triangle \2"),
    ];

    let mut result = line.to_string();
    for (pattern, replacement) in replacements.iter() {
        let re = Regex::new(pattern).unwrap();
        result = re.replace_all(&result, *replacement).to_string();
    }
    result
}

fn find_all_sentence(line: &str, tag: &str) -> String {
    let mut line = Regex::new(r"\d+(\.\d*)?")
        .unwrap()
        .replace_all(line, "@")
        .to_string();

    let latex = findall_latex(&line);
    let binding = latex.join("");
    let text: Vec<&str> = Regex::new(r"[\u4e00-\u9fa5]+")
        .unwrap()
        .captures_iter(&binding)
        .map(|cap| cap.get(0).map_or("", |m| m.as_str()))
        .collect();
    let flag = if text.is_empty() { 0 } else { 1 };

    line = filter_formula(&line);
    line = Regex::new(r"\([a-zA-z0-9]\)|[a-zA-Z0-9]\.")
        .unwrap()
        .replace_all(&line, " ")
        .to_string();
    line = Regex::new(r"[ \n\t]+")
        .unwrap()
        .replace_all(&line, " ")
        .to_string();
    line = line.replace("_", " ");
    line = line.chars().filter(|&c| !PUNCTUATIONS.contains(&c)).collect();

    if flag == 1 {
        line += &(" ".to_owned() + &text.join(" "));
    }

    line
}

fn findall_latex(line: &str) -> Vec<String> {
    let line = checkout_latex(line);  // NOTE: Provide the Rust implementation for checkout_latex
    let latex_re = Regex::new(r"\$.+?\$").unwrap();

    latex_re.captures_iter(&line)
        .filter_map(|cap| cap.get(0))
        .map(|m| m.as_str().to_string())
        .collect()
}

fn checkout_latex(line: &str) -> String {
    let line = checkout_substring(line, "arcsin|arccos|arctan", true);
    let line = checkout_substring(&line, "sin|cos|tan", true);
    let line = checkout_substring(&line, "ln|lg", true);
    let line = checkout_substring(&line, "mathbf|mathrm", false);
    let line = checkout_substring(&line, "langle|rangle", true);
    identify_formula(line.as_str())
}

fn checkout_substring(line: &str, pattern: &str, blank: bool) -> String {
    let re = Regex::new(pattern).unwrap();
    let mut result = String::with_capacity(line.len());

    let mut last_end = 0;
    for cap in re.find_iter(line) {
        let start = cap.start();
        let end = cap.end();

        result.push_str(&line[last_end..start]);

        if start == 0 {
            result.push('\\');
        } else {
            let prefix1 = &line[start.saturating_sub(1)..start];
            let prefix2 = if start > 2 { &line[start.saturating_sub(3)..start] } else { &line[..start] };
            if prefix1 != "\\" && prefix2 != "arc" {
                result.push('\\');
            }
        }
        result.push_str(&cap.as_str());

        if end < line.len() && blank && line.chars().nth(end) != Some(' ') {
            result.push(' ');
        }

        last_end = end;
    }
    result.push_str(&line[last_end..]);

    result
}

fn identify_formula(text: &str) -> String {
    fn dollor_formula(caps: &Captures) -> String {
        format!("${}$", &caps["value"])
    }

    let re_brackets = Regex::new(r"\\\[(?P<value>.+?)\\\]").unwrap();
    let re_parentheses = Regex::new(r"\\\((?P<value>.+?)\\\)").unwrap(); // new
    let re_double_dollor = Regex::new(r"\$\$(?P<value>.+?)\$\$").unwrap();

    let text = re_brackets.replace_all(text, dollor_formula);
    let text = re_parentheses.replace_all(&text, dollor_formula);
    let text = re_double_dollor.replace_all(&text, dollor_formula);
    text.to_string()
}

fn filter_formula(text: &str) -> String {
    let re_brackets = Regex::new(r"\\\[(.+?)\\\]").unwrap();
    let re_parentheses = Regex::new(r"\\\((.+?)\\\)").unwrap(); // new
    let re_double_dollor = Regex::new(r"\$\$(.+?)\$\$").unwrap();
    let re_single_dollor = Regex::new(r"\$(.+?)\$").unwrap();

    let text = re_brackets.replace_all(text, " ");
    let text = re_parentheses.replace_all(&text, " ");
    let text = re_double_dollor.replace_all(&text, " ");
    let text = re_single_dollor.replace_all(&text, " ");

    text.into_owned()
}

fn translate_char2latex(text: &str, mut sentence: Option<String>) -> (Vec<String>, Option<String>) {
    let text = filter_formula(text);
    // 第一步: 使用正则表达式来查找所有可能的LaTeX候选字符串
    let pattern = format!("{}{}{}",r"[a-zA-Z0-9=+\-.{{}}()><,^_\\|/{",*SPECIAL_SIGNS,"}]+");
    let re = Regex::new(&pattern).unwrap();
    let matches: Vec<&str> = re.find_iter(&text).map(|m|m.as_str()).collect();

    let mut candidate_list = Vec::new();

    for mat in matches {
        if check_is_useful_latex(mat) {
            let cleaned = clean_candidate_latex(mat);
            let normalized = normalized_symbol(cleaned);

            if let Some(sent) = &mut sentence {
                *sent = sent.replace(mat, "");
            }

            candidate_list.push(format!("${}$", normalized));
        }
    }
    (candidate_list, sentence)
}

fn clean_sentence(line: &Option<String>) -> String {
    let mut cleaned = line.clone().unwrap();

    // 过滤选择题中的标号,例如a. (b)等
    let re1 = Regex::new(r"\([a-zA-Z0-9]\)|[a-zA-Z0-9]\.").unwrap();
    cleaned = re1.replace_all(&cleaned, " ").to_string();

    // 过滤多余的空格
    let re2 = Regex::new(r"[ \n\t]+").unwrap();
    cleaned = re2.replace_all(&cleaned, " ").to_string();

    cleaned = cleaned.replace("_", " ");

    // 过滤标点符号
    cleaned = cleaned.chars().filter(|&c| !PUNCTUATIONS.contains(&c)).collect();

    cleaned
}

fn split_text_and_latex(text: &str) -> (Vec<String>, Vec<String>) {
    let cleaned_text = remove_aligned_symbol(text);

    let pattern = r"\\text\s*\{.*?\}";
    let re = Regex::new(pattern).unwrap();

    let mut text_res: Vec<String> = Vec::new();
    let mut latex_res: Vec<String> = Vec::new();
    let mut start: usize = 0;

    for cap in re.find_iter(&cleaned_text) {
        let ts = &cleaned_text[cap.start()..cap.end()];
        let ls = &cleaned_text[start..cap.start()];
        if !ts.is_empty() {
            text_res.push(ts.to_string());
        }
        if !ls.is_empty() {
            let cleaned_ls = clean_local_latex(ls);
            latex_res.push(make_latex(&cleaned_ls));
        }
        start = cap.end();
    }

    let ls = &cleaned_text[start..];
    if !ls.is_empty() {
        latex_res.push(make_latex(ls));
    }

    (text_res, latex_res)
}

fn clean_local_latex(mut text: &str) -> String {
    text = text.trim();

    if text.starts_with(',') || text.starts_with('.') {
        text = &text[1..];
    }

    if text.ends_with(',') {
        text = &text[..text.len() - 1];
    }

    if text.ends_with('.') && !text.ends_with("right.") {
        text = &text[..text.len() - 1];
    }

    text.to_string()
}

fn make_latex(text: &str) -> String {
    if text.len() >= 2 && text.starts_with('$') && text.ends_with('$') {
        text.to_string()
    } else if text.starts_with('$') {
        format!("{}$", text)
    } else if text.ends_with('$') {
        format!("${}", text)
    } else {
        format!("${}$", text)
    }
}

fn remove_aligned_symbol(text: &str) -> String {
    let re_outer = Regex::new(r"\\begin\{aligned\}(?P<v>.*?)\\end\{aligned\}").unwrap();
    let re_inner = Regex::new(r"&|\\\\").unwrap();

    re_outer.replace_all(text, |caps: &regex::Captures| {
        let mut mtext = caps["v"].to_string();
        mtext = re_inner.replace_all(&mtext, "").to_string();
        mtext
    }).to_string()
}

fn bidirection_expand_latex(text: &str) -> Vec<String> {
    let text = checkout_latex(&text);
    let latex_regex = Regex::new(r"\$.+?\$").unwrap();
    let mut expand_latexs: Vec<String> = Vec::new();

    for cap in latex_regex.captures_iter(&text) {
        let mut start = cap.get(0).unwrap().start();
        let mut end = cap.get(0).unwrap().end();

        while start > 0 && EXPAND_SIGNS.contains(text.chars().nth(start - 1).unwrap()) {
            start -= 1;
        }
        while end < text.len() && EXPAND_SIGNS.contains(text.chars().nth(end).unwrap()) {
            end += 1;
        }

        let mut expand_lat = format!("{}{}{}", &text[start..cap.get(0).unwrap().start()],
                                     &cap[0], &text[cap.get(0).unwrap().end()..end]);

        expand_lat = expand_lat.trim_matches(|c| c == '.' || c == ',').to_string();
        expand_lat = format!("${}$", filter_dollar(&expand_lat));

        if !expand_latexs.contains(&expand_lat) {
            expand_latexs.push(expand_lat);
        }
    }

    expand_latexs
}

fn filter_dollar(text: &str) -> String {
    text.replace("$", "")
}

fn tokenize_latex(latlist: Vec<String>) -> Vec<String> {
    let mut rep_latlist = Vec::new();
    let mut src_latlist = Vec::new();
    let mut tokenizes = Vec::new();

    for lat in latlist.iter() {
        let mut lat = clean_latex(lat.clone());

        if is_singlechar(&lat) {
            continue;
        }

        if let Some(flag) = is_question_number(&lat) {
            if flag.is_empty() {
                continue;
            }
        }
        let colon_result = split_colon(&lat);
        if colon_result.len() == 2 {
            // Assuming colon_result is a tuple or a Vec
            lat = colon_result[1].clone();
            if colon_result[0].len() > 4 {
                tokenizes.push(colon_result[0].clone());
            }
        }

        let cond_result = split_suffix_condition(&lat);
        for lat in cond_result.iter() {
            let rep_lat = eliminate_constant(lat.clone().as_str());

            if rep_lat != *lat {
                tokenizes.push(rep_lat.clone());

                let (flag, temp) = eliminate_variable(&rep_lat, "\\[a-zA-Z]+","#");
                if flag == 1 {
                    tokenizes.push(temp);
                }

                let extractions = extract_array_expression(&rep_lat);
                tokenizes.extend_from_slice(&extractions);
                src_latlist.push(lat.clone());
            }

            rep_latlist.push(rep_lat.clone());
            tokenizes.push(lat.clone());

            let (flag, temp) = eliminate_variable(&lat, "\\[a-zA-Z]+","#");
            if flag == 1 {
                tokenizes.push(temp);
            }

            let extractions = extract_array_expression(&lat);
            tokenizes.extend_from_slice(&extractions);

            let (flag, exchange_lat) = exchange_add_operation(rep_lat.clone().as_str());
            if flag {
                tokenizes.push(exchange_lat);
            }
        }
    }

    let line = rep_latlist.join(" ");
    let src_line = src_latlist.join(" ");

    let mut src_tokenizes = Vec::new();
    let extracted = extract_substructure(&line);
    tokenizes.extend_from_slice(&extracted);

    let src_sub = extract_substructure(&src_line);
    for sub in src_sub.iter() {
        if !tokenizes.contains(sub) {
            src_tokenizes.push(sub.clone());
        }
    }

    tokenizes.extend_from_slice(&src_tokenizes);
    tokenizes.iter().map(|x| remove_comma(x,r"\\begin\{array\}.+?\\end\{array\}")).collect()
}

fn clean_latex(mut text: String) -> String {
    text = normalized_symbol(text);
    text = remove_useless_symbol(text.as_str());
    text = remove_redundant_brace(text.as_str());
    text = remove_left_right_symbol(text.as_str());
    text = clean_array_expression(text.as_str());
    text = remove_last_comma(text.as_str());
    text = remove_latex_qid(text.as_str());
    text
}

fn normalized_symbol(mut text: String) -> String {
    let substitutions: Vec<(&str, &str)> = vec![
        (r"ω", r"\\omega "),
        (r"∠", r"\\angle "),
        (r"α",r"\\alpha "),
        (r"β",r"'\\beta "),
        (r"γ",r"\\gamma "),
        (r"θ",r"\\theta "),
        (r"π",r"\\pi "),
        (r"△",r"\\triangle"),
        (r"\\overline",r"\\bar"),  //将上划线统一替换为bar
        (r"→",r"\\rightarrow "),
        (r"←",r"\\leftarrow "),
        (r"⩽|≤",r"\\leqslant "),
        (r"⩾|≥",r"\\geqslant "),
        (r"≈",r"\\approx "),
        (r"∞",r"\\infty "),
        (r"∑",r"\\sum"),
        (r"",r""),
        (r"μ",r"\\mu "),
        (r"σ",r"\\sigma "),
        (r"∂",r"\\partial "),
        (r"⋅",r"\\cdot "),
        (r"η",r"\\eta "),
        (r"ξ",r"\\xi "),
        (r"²",r"^2 "),
        (r"³",r"^3 ")
    ];

    for (pattern, replacement) in substitutions {
        let re = Regex::new(pattern).unwrap();
        text= re.replace_all(&text, replacement).to_string();
    }

    let re_underset = Regex::new(r"\\underset[ ]*\{(?P<v1>.+?)\}[ ]*\{[ ]*(?P<v2>\\[i|o]*int)[ ]*\}").unwrap();
    text = re_underset.replace_all(&text, |caps: &regex::Captures| {
        format!("{}_{{{}}}", &caps["v2"], &caps["v1"])
    }).to_string();
    let re_overset = Regex::new(r"\\overset[ ]*\{[ ]*\\(?P<v>.+?)\}").unwrap();
    text = re_overset.replace_all(&text, |caps: &regex::Captures| {
        format!("\\over{}", &caps["v"])
    }).to_string();

    let re_underset = Regex::new(r"\\underset[ ]*\{[ ]*\\(?P<v>.+?)\}").unwrap();
    text = re_underset.replace_all(&text, |caps: &regex::Captures| {
        format!("\\under{}", &caps["v"])
    }).to_string();

    let substitutions: Vec<(&str, &str)> = vec![
        (r"\\geqslant", r"\\ge "),
        (r"\\leqslant", r"\\le "),
        (r"\\geq", r"\\ge"),
        (r"\\leq", r"\\le"),
        (r"\\neq", r"\\ne"),
        (r"\\owns", r"\\ni"),
        (r"\\prime", r"'"),
        (r"\\mid", r"|"),
        (r"\\[a-zA-Z]*triangle[a-zA-Z]*", r"\\triangle"),
    ];

    for (pattern, replacement) in substitutions {
        let re = Regex::new(pattern).unwrap();
        text = re.replace_all(&text, replacement).to_string();
    }

    let re_big = Regex::new(r"\\big(?P<v>[a-zA-Z]+)").unwrap();
    text = re_big.replace_all(&text, |caps: &regex::Captures| {
        format!("\\{}", &caps["v"])
    }).to_string();

    text
}

fn check_is_useful_latex(text: &str) -> bool {
    let tlen = text.len();

    if tlen == 1 {
        return false;
    }

    let patterns: [&str; 3] = [
        r"\([0-9]+\)|[0-9]+\.",
        r"\([a-zA-Z]+\)|[a-zA-Z]+\.",
        r"[a-zA-Z]+|[0-9]+"
    ];

    for pattern in &patterns {
        let re = Regex::new(pattern).unwrap();
        if re.replace_all(text, "") == "" {
            return false;
        }
    }

    true
}

fn clean_candidate_latex(text: &str) -> String {
    let trimmed_text = text.trim();
    if trimmed_text.ends_with(',') || trimmed_text.ends_with('(') || trimmed_text.ends_with('{') {
        String::from(&trimmed_text[..trimmed_text.len() - 1])
    } else {
        String::from(trimmed_text)
    }
}

fn is_singlechar(line: &str) -> bool {
    let stripped_line: String = line.chars().filter(|&c| !c.is_whitespace()).collect();

    let mut modified_line = stripped_line.clone();
    if stripped_line.len() >= 2 && stripped_line.starts_with('$') && stripped_line.ends_with('$') {
        modified_line = stripped_line[1..stripped_line.len() - 1].to_string();
    }

    FILTER_WORDS.contains(&modified_line.as_str())
}

fn is_question_number(line: &str) -> Option<String> {
    let mut modified_line: String = line.chars().filter(|&c| !c.is_whitespace()).collect();

    if modified_line.len() >= 2 && modified_line.starts_with('$') && modified_line.ends_with('$') {
        modified_line = modified_line[1..modified_line.len() - 1].to_string();
    }

    let re1 = Regex::new(r"[.]*[a-zA-Z0-9]{1,4}[.]*").unwrap();
    modified_line = re1.replace_all(&modified_line, "").to_string();

    let re2 = Regex::new(r"\([a-zA-Z0-9]{1,4}\)").unwrap();
    modified_line = re2.replace_all(&modified_line, "").to_string();

    Some(modified_line)
}

fn split_colon(line: &str) -> Vec<String> {
    let mut result: Vec<String> = Vec::new();

    for sl in line.trim().split(':') {
        if sl.starts_with('$') {
            result.push(format!("{}:$", sl));
        } else if sl.ends_with('$') {
            result.push(format!("${}", sl));
        }
    }

    result
}


fn split_suffix_condition(text: &str) -> Vec<String> {
    let condition_re = fancy_regex::Regex::new(r"\(.+\)[ ]*(?=[$])").unwrap();
    if let Some(condition_mat) = condition_re.find(text).unwrap() {
        let condition = format!("${}$", condition_mat.as_str());
        let formal = condition_re.replace(text, "").to_string();
        // Check for the $(x,y)$ case
        if formal.replace(" ", "") == "$$" {
            return vec![text.to_string()];
        } else {
            return vec![formal, condition];
        }
    }
    vec![text.to_string()]
}

fn eliminate_constant(text: &str) -> String {
    let num_re = Regex::new(r"\d+(\.\d*)?").unwrap();
    num_re.replace_all(text, "@").to_string()
}

fn eliminate_variable(line: &str, keep_pattern: &str, sign: &str) -> (i32, String) {
    let mut new_line = String::new();
    let re = Regex::new(keep_pattern).unwrap();
    let var_re = Regex::new(r"[a-zA-Z]+").unwrap();
    let mut last_end = 0;

    for cap in re.find_iter(line) {
        let start = cap.start();
        let end = cap.end();

        new_line.push_str(&var_re.replace_all(&line[last_end..start], sign));
        new_line.push_str(&line[start..end]);

        last_end = end;
    }

    new_line.push_str(&var_re.replace_all(&line[last_end..], sign));

    if &new_line == line {
        (0, new_line)
    } else {
        (1, new_line)
    }
}

pub(crate) fn is_number(s: &str) -> bool {
    // 检查是否为数字的实现
    s.parse::<f64>().is_ok()
}

fn extract_array_expression(line: &str) -> Vec<String> {
    let mut array_exp = Vec::new();
    let pattern = r"\\begin\{array\}(.+?)\\end\{array\}|\\begin\{align\}(.+?)\\end\{align\}";
    let re = Regex::new(pattern).unwrap();

    let mut temp: Vec<&str> = Vec::new();
    let binding = line.replace(" ", "");
    for cap in re.captures_iter(&binding) {
        temp.push(cap.get(1).map_or("", |m| m.as_str()));
        temp.push(cap.get(2).map_or("", |m| m.as_str()));
    }

    for array in temp {
        if array.trim().is_empty() {
            continue;
        }
        for se in array.split(',') {
            let se = se.trim().to_string();
            if se.is_empty() {
                continue;
            } else if se.starts_with("{") && se.ends_with("}") {
                array_exp.push(se[1..se.len()-1].to_string());
            } else if se.starts_with("{") {
                array_exp.push(se[1..].to_string());
            } else if se.ends_with("}") {
                array_exp.push(se[..se.len()-1].to_string());
            } else {
                array_exp.push(se);
            }
        }
    }

    array_exp
}

fn exchange_add_operation(line: &str) -> (bool, String) {
    let vaild_exp = r"[@\\a-z\^\_\{\}\-ω ]";
    let suffix_pattern = format!(r"(?<=\+){}+?(?=[=\+\)$])|(?<=\+){}*?[\(]{{1}}{}+?[\)]{{1}}{}*?(?=[=$\+])", vaild_exp, vaild_exp, vaild_exp, vaild_exp);
    let prefix_pattern = format!(r"(?<=\+){}+?(?=[=\+\($])|(?<=\+){}*?[\)]{{1}}{}+?[\(]{{1}}{}*?(?=[=$\+])", vaild_exp, vaild_exp, vaild_exp, vaild_exp);

    let mut flag = false;
    let mut new_line = line.to_string();

    let re_suffix = fancy_regex::Regex::new(&suffix_pattern).unwrap();
    let re_prefix = fancy_regex::Regex::new(&prefix_pattern).unwrap();

    for suffix_cap in re_suffix.captures_iter(&line) {
        flag = true;
        let suffix_mat = suffix_cap.unwrap().get(0).unwrap();
        let suffix_start = suffix_mat.start();
        let suffix_end = suffix_mat.end();

        for prefix_cap in re_prefix.captures_iter(&line.chars().rev().collect::<String>()) {
            let prefix_mat = prefix_cap.unwrap().get(0).unwrap();
            let prefix_start = line.len() - prefix_mat.end();
            let prefix_end = line.len() - prefix_mat.start();

            new_line = format!("{}{}+{}{}",
                               &line[0..prefix_start],
                               &line[suffix_start..suffix_end],
                               &line[prefix_end..suffix_start],
                               &line[suffix_end..]
            );
            break;  // Assuming we're swapping only once
        }
    }

    (flag, new_line)
}

fn extract_substructure(line: &str) -> Vec<String> {
    let mut tokenizes = Vec::new();

    let patterns = [
        r"\\[@#a-zA-Z0-9]+",
        r"\\[@#a-zA-Z0-9]+[ ]*\{.+?[\}]?[ ]*\}",
        r"\\[@#a-zA-Z0-9]+[ ]*\{.+?[\}]?[ ]*\}[ ]*\{.+?\}",
        r"[\{ ]*[.,\\@#a-zA-Zω0-9 ]+[\} ]*[\_\^][\{ ]*[.,\\@#a-zA-Zω0-9 ]+[\} ]*",
        r"[0-9@\\a-z\^\_\{\}ω ]*?\([0-9@\\a-z\^\_\{\}\-\+ω ]+?\)[0-9@\\a-z\^\_\{\}ω ]*?"
    ];

    for pattern in &patterns {
        let re = Regex::new(pattern).unwrap();
        for cap in re.captures_iter(line) {
            if let Some(mat) = cap.get(0) {
                tokenizes.push(mat.as_str().to_string());
            }
        }
    }

    tokenizes
}

fn remove_comma(line: &str, pattern: &str) -> String {
    let re = Regex::new(pattern).unwrap();
    if re.is_match(line) {
        return line.replace(",", "").replace("，", "");
    }
    line.to_string()
}

fn remove_useless_symbol(text: &str) -> String {
    let result = text.to_string();
    let re = Regex::new(r"\\operatorname[ ]*").unwrap();
    let mut result = re.replace_all(&result, |caps: &Captures| {
        if caps.get(0).unwrap().as_str().ends_with("{") {
            caps.get(0).unwrap().as_str().to_string()
        } else {
            " ".to_string()
        }
    }).to_string();
    let patterns = vec![
        (r"\\text[ ]*\{[ ]*\}", " "),
        (r"(\\\!)+", " "),
        (r"\\text[ ]*\{[ ]*(.+?)[ ]*\}", "$1"),
        (r"\\operatorname\{[ ]*(.+?)[ ]*\}", "$1"),
        (r"\\displaystyle", ""),
        (r"\\mathbf\s*\{(.+?)\}", "$1"),
        (r"\\mathrm\s*\{(.+?)\}", "$1"),
        (r"\\mathit\s*\{(.+?)\}", "$1"),
        (r"([+-,. ])\\[ ]", "$1 "),
        (r"\\rm\s*\{(.+?)\}", "$1"),
        (r"\\boldsymbol\s*\{(.+?)\}", "$1"),
        (r"\\underline\s*\{(.+?)\}", "$1"),
        (r"\\quad", ""),
        (r"\\limits", ""),
        (r"\\nolimits", ""),
        (r"\{[ ]*\}", " ")
    ];

    for (pattern, replace) in patterns.iter() {
        result = Regex::new(pattern).unwrap().replace_all(&result, *replace).to_string();
    }

    result
}

fn remove_redundant_brace(text: &str) -> String {

    let mut text = text.to_string();
    let patterns = vec![
        (r"(?<!\})[ ]*\{[ ]*(\\[a-zA-Z]+?)[ ]*\}", "$1"),
        (r"(?<=[\^\_ ])\{([0-9a-zA-Z\'])\}", "$1"),
        (r"(?<!\})\{([0-9a-zA-Z])\}(?=[\^\_ ])", "$1"),
        (r"\$[ ]*\{(.+)\}[ ]*\$", "$1"),
        (r"\{[ ]*(\(.+?\))[ ]*\}", "$1"),
        (r"\^'", "'"),
    ];

    for (pattern, replacement) in patterns {
        let re = fancy_regex::Regex::new(pattern).unwrap();
        text = re.replace_all(&text, replacement).to_string();
    }

    // The loop replacement
    let re_outer_braces = Regex::new(r"([\$\+\-\=,\{><|0-9a-zA-Z#@ ])\{([ ]*[0-9a-zA-Z#@][ ]*[\^\_][ ]*[0-9a-zA-Z#@][ ]*)\}([\$\+\-\=,\\\}><|0-9a-zA-Z@# ])").unwrap();
    loop {
        let new_text = re_outer_braces.replace_all(&text, "$1$2$3").to_string();
        if new_text == text {
            break;
        }
        text = new_text;
    }

    text
}

fn remove_left_right_symbol(text: &str) -> String {
    let mut result = text.to_string();

    fn replace_brace(caps: &regex::Captures) -> String {
        let front = caps.name("v1").unwrap().as_str();
        let later = caps.name("v2").unwrap().as_str().trim();
        if later == "." {
            front.to_string()
        } else {
            format!("{}{}", front, later)
        }
    }

    let pattern = Regex::new(r"\\left(?P<v1>.+?)\\right(?P<v2>[ ]*.)").unwrap();
    let mut flag = true;

    while flag {
        if pattern.is_match(&result) {
            result = pattern.replace_all(&result, replace_brace).to_string();
        } else {
            flag = false;
        }
    }

    result
}

fn clean_array_expression(text: &str) -> String {
    let mut result = text.to_string();

    // Replace \begin{align} with \begin{array}
    let pattern1 = Regex::new(r"\\begin\{align\}(?P<v>.+?)\\end\{align\}").unwrap();
    result = pattern1.replace_all(&result, |caps: &regex::Captures| {
        format!(r"\\begin{{array}}{}\\end{{array}}", &caps["v"])
    }).to_string();
    let  binding = result.clone();
    let pattern2 = Regex::new(r"\\begin\{array\}(.+?)\\end\{array\}|\\begin\{align\}(.+?)\\end\{align\}").unwrap();
    let arraylist: Vec<&str> = pattern2.captures_iter(&binding)
        .flat_map(|cap| vec![cap.get(1).map_or("", |m| m.as_str()), cap.get(2).map_or("", |m| m.as_str())])
        .filter(|&s| !s.is_empty())
        .collect();

    for array in arraylist {
        if array.chars().next().unwrap() == '{' {
            let substr: String = array.chars().take_while(|&c| c != '}').collect();
            result = result.replace(&substr, "");
        }

        result = result.replace("\\\\", ",");
        result = Regex::new(r"[,\.&。，]").unwrap().replace_all(&result, ",").to_string();
        result = Regex::new(r",[, ]*").unwrap().replace_all(&result, ",").to_string();
        result = Regex::new(r"\\hfill[ ]*[&]*").unwrap().replace_all(&result, "").to_string();
        result = Regex::new(r",[, ]*").unwrap().replace_all(&result, ",").to_string();
    }

    result
}

fn remove_last_comma(text: &str) -> String {
    if text.starts_with('$') && text.ends_with('$') {
        let mut inner_text = text[1..text.len()-1].trim();

        if inner_text.starts_with(',') || inner_text.starts_with('.') {
            inner_text = &&inner_text[1..];
        }

        if inner_text.ends_with(',') || inner_text.ends_with('.') {
            inner_text = &&inner_text[..inner_text.len()-1];
        }

        if inner_text.ends_with('.') && !inner_text.ends_with("right.") {
            inner_text = &&inner_text[..inner_text.len() - 1];
        }

        return format!("${}$", inner_text);
    }

    text.to_string()
}

fn remove_latex_qid(text: &str) -> String {
    if text.starts_with('$') && text.ends_with('$') {
        let inner_text = text[1..text.len()-1].trim();
        let re = Regex::new(r"^\(\d+\)").unwrap();
        let result_text = re.replace(&inner_text, "");
        return format!("${}$", result_text.trim());
    }
    text.to_string()
}


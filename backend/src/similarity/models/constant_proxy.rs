use std::collections::HashMap;
use lazy_static::lazy_static;
use std::sync::RwLock;
use mongodb::Database;
use crate::app::models::orm::paper_type::PaperType;
use crate::app::models::orm::region::Region;
use crate::app::models::orm::subject::Subject;

pub struct ConstantProxy;
lazy_static! {
    static ref SUBJECT_MAP: RwLock<HashMap<u32, String>> = RwLock::new(HashMap::new());
    static ref REGION_MAP: RwLock<HashMap<String, String>> = RwLock::new(HashMap::new());
    static ref PAPER_TYPE_MAP: RwLock<HashMap<String, String>> = RwLock::new(HashMap::new());
}

impl ConstantProxy {
    pub async fn get_subject_name(code: u32,database:Database) -> Option<String> {
        {
            let map_read = SUBJECT_MAP.read().unwrap();
            if let Some(name) = map_read.get(&code) {
                return Some(name.clone());
            }
        }
        // Placeholder for the database fetch operation
        let subject = Subject::get_by_code(code,database).await.unwrap();
        {
            let mut map_write = SUBJECT_MAP.write().unwrap();
            map_write.insert(code, subject.name.clone());
        }

        Some(subject.name)
    }

    pub  async fn get_region_name(code: Option<String>, database:Database) -> String {
        match code {
            None => return "全国".to_string(),
            Some(code) => {
                if code == String::from("000000") {
                    return "全国".to_string()
                }
                {
                    let map_read = REGION_MAP.read().unwrap();
                    if let Some(name) = map_read.get(code.as_str()) {
                        return name.clone();
                    }
                }

                let region = Region::get_by_code(code.to_string(),database).await;

                let region_name = region.map_or_else(String::new, |r| r.name);

                {
                    let mut map_write = REGION_MAP.write().unwrap();
                    map_write.insert(code.to_string(), region_name.clone());
                }

                region_name
            }
        }
    }

    pub(crate) async fn get_paper_type(code: &str, database:Database) -> String {
        let code = code.to_string();
        {
            let map_read = PAPER_TYPE_MAP.read().unwrap();
            if let Some(name) = map_read.get(&code) {
                return name.clone();
            }
        }

        let paper_type = PaperType::get_by_code((&code).to_string(), database).await;

        let paper_type_name = paper_type.map_or_else(String::new, |pt| pt.name);

        {
            let mut map_write = PAPER_TYPE_MAP.write().unwrap();
            map_write.insert(code, paper_type_name.clone());
        }

        paper_type_name
    }
}
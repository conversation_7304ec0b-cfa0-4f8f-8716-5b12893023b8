use mongodb::bson::doc;
use mongodb::Database;

use serde::{Deserialize, Serialize};
#[derive(Debug, Serialize, Deserialize)]
pub struct Subject {
    code: u32,
    pub(crate) name: String,
}
impl Subject {
    pub fn new(code: u32, name: String) -> Self {
        Self { code, name }
    }

    pub async fn get_by_code(code: u32, database:Database) -> Option<Subject> {
        let subject_collection = database.collection("question");
        let subject = subject_collection.find_one(doc! { "code": code}, None).await.unwrap();
        subject
    }
}
use serde::{Serialize, Deserialize};
use mongodb::{bson::doc, Collection, Database};
use mongodb::error::Result;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Image {
    #[serde(rename = "_id")]
    pub(crate) image_id: u32,
    #[serde(rename = "subjectCode")]
    subject_code: Option<u32>,
    #[serde(rename = "questionId")]
    pub question_id: Option<u32>,
    url: String,
    batch: Option<u32>,
}

impl Image {
    pub async fn remove_self(&self, db:Database) -> Result<()> {
        let image_collection = db.collection::<Image>("image");
        image_collection.delete_one(doc! {"_id": self.image_id}, None).await?;
        Ok(())
    }

    pub fn from_v1(dd: &ImageData, batch: u32) -> Self {
        let mut url = dd.url.clone();

        const HEADERS: [&str; 2] = [
            "http://file.eep.qctchina.top/",
            "http://192.168.4.56:9000/question-acquisition-eec/",
        ];

        for header in &HEADERS {
            if url.starts_with(header) {
                url = url[header.len()..].to_string();
            }
        }

        Image {
            image_id: dd.id,
            question_id: dd.parent_question_id.or(dd.question_id),
            subject_code: dd.subject_code,
            url,
            batch: Some(batch),
        }
    }
}

// Intermediate struct to handle the incoming data structure for from_v1 method
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ImageData {
    id: u32,
    parent_question_id: Option<u32>,
    question_id: Option<u32>,
    subject_code: Option<u32>,
    url: String,
}

impl std::fmt::Display for Image {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "[{}] batch {}: {}", self.image_id, self.subject_code.unwrap_or(0), self.url)
    }
}


use mongodb::bson::doc;
use mongodb::Database;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct Book {
    pub(crate) id:u32,
    pub(crate) name: String,
}
impl Book {
    pub async fn get_by_id(id: u32, database: Database) -> Option<Book> {
        let book_collection = database.collection("book");
        let book = book_collection.find_one(doc! { "_id": id }, None).await.unwrap();
        book
    }
}
use nalgebra as na;
pub fn normalization(data: &Vec<f64>) -> Vec<f64> {
    let maxed = *data.iter().max_by(|x, y| x.partial_cmp(y).unwrap()).unwrap();
    let mined = *data.iter().min_by(|x, y| x.partial_cmp(y).unwrap()).unwrap();
    let range = maxed - mined;
    data.iter().map(|&x| (x - mined) / (range + 1e-8)).collect()
}

fn standardization(data: &Vec<f64>) -> Vec<f64> {
    let mu = data.iter().sum::<f64>() / data.len() as f64;
    let sigma = (data.iter().map(|&x| (x - mu).powi(2)).sum::<f64>() / data.len() as f64).sqrt();
    data.iter().map(|&x| (x - mu) / (sigma + 1e-8)).collect()
}

pub fn norm_vector(vector: &Vec<f64>) -> f64 {
    na::DVector::from_vec(vector.clone()).norm()
}
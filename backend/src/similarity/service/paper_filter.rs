use std::collections::{HashMap, HashSet};
use std::fs;
use std::fs::File;
use std::io::BufReader;
use std::path::PathBuf;
use std::time::Instant;
use futures::StreamExt;

use mongodb::{bson, Database};
use mongodb::bson::{Bson, doc};
use serde::{Deserialize, Serialize};
use serde_pickle::{DeOptions, SerOptions};
use crate::app::models::orm::image::Image;

use crate::app::models::orm::paper::Paper;
use crate::app::models::orm::question::Question;
use crate::app::models::vo::paper_vo::PaperVo;

#[derive(Debug, Serialize, Deserialize)]
struct FilterData {
    papers: HashMap<u32, FilterPaper>,
    question_papers: HashMap<u32, Vec<u32>>,
    image_questions: HashMap<u32, u32>,
}

#[derive(Debug, Serialize, Deserialize)]
struct FilterPaper {
    year: u32,
    subject_code: u32,
    region_stack: Option<HashSet<String>>,
    paper_type_code: Option<String>,
    batch: Option<u32>,
}

impl FilterPaper {
  pub fn new(paper:Paper) -> Self {
    Self {
        year: paper.school_year,
        subject_code:paper.subject_code,
        region_stack: paper.region_stack,
        paper_type_code: paper.paper_type_code,
        batch: paper.batch
    }
  }
}

// Placeholder for FilterItem struct
#[derive(Debug)]
pub struct FilterItem {
    years: Option<HashSet<u32>>,
    regions: Option<HashSet<String>>,
    paper_types: Option<HashSet<String>>,
    batches: Option<HashSet<u32>>,
    thr: f64,
}

impl FilterItem {
    pub fn new(thr: f64) -> Self {
        Self {
            years: None,
            regions: None,
            paper_types: None,
            batches: None,
            thr,
        }
    }
    pub fn _is_blank(key: &Option<String>) -> bool {
        match key {
            Some(k) if k.is_empty() => true,
            None => true,
            _ => false,
        }
    }
}

impl FilterData {
    async fn load(batch: Option<&str>, subject_code: Option<&str>, db: Database) -> Self {
        let mut match_doc = doc! {};

        if let Some(b) = batch {
            match_doc.insert("batch", b);
        }
        if let Some(sc) = subject_code {
            match_doc.insert("subjectCode", sc);
        }

        let mut question_papers: HashMap<u32, Vec<u32>> = HashMap::new();
        let mut cursor = db.collection("question").find(Some(match_doc.clone()), None).await.unwrap();
        while let Some(result) = cursor.next().await {
            let question: Question = bson::from_bson(Bson::Document(result.unwrap())).unwrap();
            question_papers.insert(question.id, question.paper_ids);
        }

        let mut image_questions: HashMap<u32, u32> = HashMap::new();
        let mut cursor = db.collection("image").find(Some(match_doc.clone()), None).await.unwrap();
        while let Some(result) = cursor.next().await {
            let image: Image = bson::from_bson(Bson::Document(result.unwrap())).unwrap();
            image_questions.insert(image.image_id, image.question_id.unwrap());
        }

        let mut papers: HashMap<u32, FilterPaper> = HashMap::new();
        let mut cursor = db.collection("paper").find(Some(match_doc), None).await.unwrap();
        while let Some(result) = cursor.next().await {
            let mut paper: Paper = bson::from_bson(Bson::Document(result.unwrap())).unwrap();
            papers.insert(paper.id, FilterPaper::new(paper));
        }

        FilterData {
            question_papers,
            papers,
            image_questions,
        }
    }
}

pub struct PaperFilter {
    filter_data: FilterData,
}

impl PaperFilter {
    pub async fn new(cache: bool, root: PathBuf, db: Database) -> Self {
        // let pickle_path = root.join("filter_data.pickle");
        let bincode_path = root.join("filter_data.bin");
        // if pickle_path.exists() && cache {
        if bincode_path.exists() && cache {
            let start = Instant::now();
            // let content = fs::read(pickle_path).unwrap();
            // let filter_data = serde_pickle::from_slice(&content, DeOptions::new()).unwrap();
            let file = File::open(bincode_path).unwrap();
            let filter_data: FilterData = bincode::deserialize_from(&mut BufReader::new(&file)).unwrap();
            println!("----filter_data from pickle file elapse {:?}", start.elapsed());
            PaperFilter { filter_data }
        } else {
            let filter_data = FilterData::load(None, None, db).await;
            // let data = serde_pickle::to_vec(&filter_data, SerOptions::default()).unwrap();
            // fs::write(pickle_path.as_path(), &data).expect("pickle file write error");
            bincode::serialize_into(&mut std::io::BufWriter::new(File::create(bincode_path).unwrap()), &filter_data).unwrap();
            PaperFilter { filter_data }
        }
    }

    fn valid_paper(&self, filter_item: &FilterItem, paper_id: u32) -> bool {
        if !self.filter_data.papers.contains_key(&paper_id) {
            return false;
        }
        let filter_paper = &self.filter_data.papers[&paper_id];

        if let Some(regions) = &filter_item.regions {
            if filter_paper.region_stack.clone().unwrap().intersection(regions).count() == 0 {
                return false;
            }
        }

        // if let Some(ref batch) = filter_item.batch {
        //     if filter_paper.batch.unwrap_or_default() != *batch {
        //         return false;
        //     }
        // }

        if let Some(years) = &filter_item.years {
            if !years.contains(&filter_paper.year) {
                return false;
            }
        }

        // if let Some(year) = filter_item.year {
        //     if filter_paper.year.unwrap_or_default() != year {
        //         return false;
        //     }
        // }

        // if let Some(paper_types) = &filter_item.paperTypes {
        //     if !paper_types.contains(&filter_paper.paperTypeCode.clone().unwrap_or_default()) {
        //         return false;
        //     }
        // }

        true
    }

    pub fn get_question(&self, filter_item: &FilterItem, question_id: u32, value: f64) -> Option<(u32, f64, u32, i32, u32)> {
        if filter_item.thr > value {
            return None;
        }
        if !self.filter_data.question_papers.contains_key(&question_id) {
            return None;
        }
        let papers = self.filter_data.question_papers.get(&question_id).unwrap();
        let mut years = HashSet::new();
        let mut batches = HashSet::new();
        let mut paper_types = HashSet::new();
        let mut region_stacks = HashSet::new();

        for &paper_id in papers {
            let filter_paper = match self.filter_data.papers.get(&paper_id) {
                Some(p) => p,
                None => continue,
            };
            years.insert(filter_paper.year);
            batches.insert(filter_paper.batch.unwrap_or(0));
            let paper_type_code = filter_paper.paper_type_code.clone().unwrap_or_default();
            paper_types.insert(paper_type_code);
            let region_stack = filter_paper.region_stack.clone().unwrap_or_default();
            region_stacks.extend(region_stack);
        }

        if let Some(fi_batches) = &filter_item.batches {
            if fi_batches.is_disjoint(&batches) {
                return None;
            }
        }

        if let Some(fi_years) = &filter_item.years {
            if fi_years.is_disjoint(&years) {
                return None;
            }
        }

        if let Some(fi_paper_types) = &filter_item.paper_types {
            if fi_paper_types.is_disjoint(&paper_types) {
                return None;
            }
        }

        if let Some(fi_regions) = &filter_item.regions {
            if fi_regions.is_disjoint(&region_stacks) {
                return None;
            }
        }

        let paper_type_order_map = [
            ("9".to_string(), 1),
            ("3".to_string(), 2),
            ("6".to_string(), 3),
            ("1".to_string(), 4),
            ("7".to_string(), 5),
            ("0".to_string(), 6)
        ].iter().cloned().collect::<HashMap<String, i32>>();

        let paper_type_order = paper_types.iter().map(|pt| *paper_type_order_map.get(pt).unwrap_or(&0)).max().unwrap_or(0);

        Some((question_id, value, *years.iter().max().unwrap_or(&0), paper_type_order, *batches.iter().max().unwrap_or(&0)))
    }

    pub(crate) async fn valid_question(&self, filter_item: &FilterItem, question_id: u32) -> bool {
        if !self.filter_data.question_papers.contains_key(&question_id) {
            return false;
        }

        let mut valid_question_type = true;
        let valid_catalogs = true;

        // if let Some(ref question_type) = filter_item.question_type {
        //     valid_question_type = self.valid_question_type(question_type.to_string(), question_id).await;
        // }
        //
        // if let Some(ref catalog_ids) = filter_item.catalogIds {
        //     valid_catalogs = self.valid_catalogs(catalog_ids, question_id).await;
        // }

        if let Some(papers) = self.filter_data.question_papers.get(&question_id) {
            for paper_id in papers {
                if valid_question_type && valid_catalogs && self.valid_paper(filter_item, *paper_id) {
                    return true;
                }
            }
        }

        false
    }

    pub async fn valid_question_type(&self, question_type_code: String, question_id: u32, database: Database) -> bool {
        let question_collection = database.collection::<Question>("question");
        let maybe_question = question_collection.find_one(doc! {
            "question_id": question_id,
            "questionTypeCode": question_type_code
        }, None).await;

        maybe_question.unwrap().is_some()
    }

    pub async fn valid_catalogs(&self, catalog_ids: &HashSet<u32>, question_id: u32, database: Database) -> bool {
        // Use the in operator to check if any of the catalogIds are in the provided list.
        let catalog_ids = Bson::Array(catalog_ids.iter().map(|catalog_id| Bson::from(catalog_id)).collect());
        let filter = doc! {
            "question_id": question_id,
            "catalogs.catalogId": { "$in": catalog_ids }
        };
        // Query the database using the filter.
        let question_collection = database.collection::<Question>("question");
        let result = question_collection.find_one(Some(filter), None).await;

        // If a matching document was found, return true, otherwise false.
        result.is_ok()
    }
}

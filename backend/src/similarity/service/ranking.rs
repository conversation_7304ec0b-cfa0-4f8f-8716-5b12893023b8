use mongodb::Database;
use crate::app::models::orm::question::Question;
use crate::app::models::utils::filter_img_latex;
use crate::app::models::vo::question_vo::QuestionVo;

pub struct Ranking {
}

impl Ranking {
    pub async fn output_questions(database: Database, ro: Vec<(u32, f64, u32, i32, u32)>, hits: Vec<String>, page_size: usize, page: usize) -> (Vec<QuestionVo>, usize) {
        let mut out = Vec::new();
        for item in ro.iter().skip((page - 1) * page_size).take(page_size) {
            let (question_id, score, year, paper_type, batch) = *item;
            let prefix = format!("{}{}▪批次{}", if year > 0 { year.to_string() } else { String::from("") }, match paper_type {
                1 => "▪竞赛",
                2 => "▪联考",
                3 => "▪压轴题",
                4 => "▪模拟",
                5 => "▪真题",
                6 => "▪真题",
                _ => "",
            }, batch);

            if let Some(question) = Question::get_by_id(question_id, database.clone()).await {
                let mut item = QuestionVo::from_question(&question,database.clone()).await;
                // Add further transformations here as needed.
                let content = filter_img_latex(question.output().as_str(),hits.clone());
                item.content = content;
                item.score = (score * 100.0).round() / 100.0;
                item.prefix = prefix.clone();
                out.push(item);
            } else {
                println!("No question found for ID {}", question_id);
            }
        }
        (out, ro.len())
    }
}
use serde_derive::{Deserialize, Serialize};
use serde_json::Value;
use surrealdb::RecordId;

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetTotalResult {
    pub name: i32,
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct QueryMaps {
    #[serde(rename = "currentPage")]
    pub current_page: i32,
    pub subclass: String,  // 大标签
    pub superclass: String, // 分类标签
    pub largeclass: String,
    pub border: Vec<Option<String>>, // 表现形式
    pub size: String, //规格
    pub scale: String, //比例尺
    #[serde(rename = "neighbouringCountry")]
    pub neighbouring_country: i32, // 邻国
    #[serde(rename = "provinceColor")]
    pub province_color: i32, // 分省设色
    pub illustration: i32, // 南海诸岛以附图表示
    #[serde(rename = "searchValue")]
    pub search_value: String
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetMaps {
    pub list: Vec<Maps>,
    pub total: i32
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetMapsFiltersBorder {
    pub border: Vec<String>
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetMapsFiltersSize {
    pub size: Vec<String>
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetMapsFiltersScale {
    pub scale: Vec<String>
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetMapsFiltersNeighbouringCountry {
    #[serde(rename = "neighbouringCountry")]
    pub neighbouring_country: Vec<i32>
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetMapsFiltersProvinceColor {
    #[serde(rename = "provinceColor")]
    pub province_color: Vec<i32>
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetMapsFiltersIllustration {
    pub illustration: Vec<i32>
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetMapsFiltersChild {
    pub label: String,
    pub value: String
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetMapsFiltersChild2 {
    pub label: String,
    pub value: i32
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct QueryMapsFilter {
    pub name: String,
    pub index: i32
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetMapsFilters {
    pub border: Vec<GetMapsFiltersChild>,
    pub size: Vec<GetMapsFiltersChild>,
    pub scale: Vec<GetMapsFiltersChild>,
    #[serde(rename = "neighbouringCountry")]
    pub neighbouring_country: Vec<GetMapsFiltersChild2>,
    #[serde(rename = "provinceColor")]
    pub province_color: Vec<GetMapsFiltersChild2>,
    pub illustration: Vec<GetMapsFiltersChild2>,
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetMapsBreadCrumbChildChild {
    pub name: String,
    pub parent: String
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetMapsBreadCrumbChild {
    pub name: String,
    pub parent: String,
    pub children: Vec<GetMapsBreadCrumbChildChild>
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetMapsBreadCrumb {
    pub name: String,
    pub children: Vec<GetMapsBreadCrumbChild>
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetMapsSubclass {
    pub subclass: Vec<String>
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetMapsSuperclass {
    pub superclass: Vec<String>
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetMapsLargeclass {
    pub largeclass: Vec<String>
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct Maps {
    pub id: Option<RecordId>,
    #[serde(rename = "mapId")]
    pub map_id: String,
    pub name: String,
    pub content: Option<String>,
    pub scale: Option<String>,
    pub size: Option<String>,
    pub superclass: Option<String>,
    pub subclass: Option<String>,
    pub smallclass: Option<String>,
    pub border: String,
    #[serde(rename = "use")]
    pub r#use: String,
    #[serde(rename = "jpgPath")]
    pub jpg_path: String,
    #[serde(rename = "browseNum")]
    pub browse_num: i32,
    #[serde(rename = "downloadNum")]
    pub download_num: i32,
    #[serde(rename = "telBrowseNum")]
    pub tel_browse_num: i32,
    #[serde(rename = "telDownloadNum")]
    pub tel_download_num: i32,
    pub catelogs: Option<String>,
    pub largeclass: Option<String>,
    #[serde(rename = "sequenceNum")]
    pub sequence_num: i32,
    pub midclass: Option<String>,
    #[serde(rename = "isImg")]
    pub is_img: String,
    #[serde(rename = "mapNumber")]
    pub map_number: String,
    #[serde(rename = "mapYear")]
    pub map_year: String,
    #[serde(rename = "orderId")]
    pub order_id: String,
    #[serde(rename = "countyOder")]
    pub county_oder: Option<String>,
    #[serde(rename = "neighbouringCountry")]
    pub neighbouring_country: Option<i32>,
    #[serde(rename = "provinceColor")]
    pub province_color: Option<i32>,
    pub illustration: Option<i32>,
    pub path: String
}

impl Maps {
    pub fn from_v1(json_str: &str) -> Result<Vec<Self>, Box<dyn std::error::Error>> {
        let mut json_value: Value = serde_json::from_str(json_str)?;
        if let Value::Array(ref mut arr) = json_value {
            for val in arr.iter_mut() {
                if let Value::Object(map) = val {
                    let opt_id_val = map.swap_remove("id");
                    map.insert("mapId".to_string(), opt_id_val.unwrap());
                }
            }
        }
        let maps: Vec<Self> = serde_json::from_value(json_value)?;
        Ok(maps)
    }
}


#[test]
fn test01() {
    use std::fs;
    use std::path::Path;

    let json_path = Path::new(r"D:\增量包\地图册和法律法规\map.json");
    let json_str = fs::read_to_string(json_path).unwrap();
    let maps = Maps::from_v1(&json_str).unwrap();
    println!("{:#?}", maps);
}
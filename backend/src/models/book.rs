use std::error::Error;
use serde_derive::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct Snapshot {
    pub page: Option<i32>,
    pub path: String,
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct QueryNews {
    #[serde(rename = "publishDate")]
    pub publish_date: String,
    pub brand: String,
    pub types: i32
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct QueryDate {
    #[serde(rename = "publishDate")]
    pub publish_date: String,
    #[serde(rename = "bookId")]
    pub book_id: u64,
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct QueryDateNum {
    #[serde(rename = "publishDate")]
    pub publish_date: i32,
}
#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct BookNewInfo {
    #[serde(rename = "preDate")]
    pub pre_date: Vec<QueryDate>,
    #[serde(rename = "preNum")]
    pub pre_num: Vec<QueryDateNum>,
    #[serde(rename = "nextDate")]
    pub next_date: Vec<QueryDate>,
    #[serde(rename = "nextNum")]
    pub next_num: Vec<QueryDateNum>,
}
#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct BookNews {
    pub book: Vec<Book>,
    #[serde(rename = "preDate")]
    pub pre_date: Vec<QueryDate>,
    #[serde(rename = "preNum")]
    pub pre_num: Vec<QueryDateNum>,
    #[serde(rename = "nextDate")]
    pub next_date: Vec<QueryDate>,
    #[serde(rename = "nextNum")]
    pub next_num: Vec<QueryDateNum>,
}
#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct Book {
    pub id: Option<RecordId>,
    #[serde(rename = "bookId")]
    pub book_id: u64,
    pub batch: Option<i32>,
    pub author: Option<String>,
    #[serde(rename = "bookName")]
    pub book_name: String,
    #[serde(rename = "institutionCode")]
    pub institution_code: Option<String>,
    pub isbn: String,
    pub publisher: Option<String>,
    pub series: Option<String>,
    pub snapshots: Vec<Snapshot>,
    #[serde(rename = "subjectCode")]
    pub subject_code: String,
    #[serde(rename = "subjectName")]
    pub subject_name: String,
    #[serde(rename = "type")]
    pub r#type: String,
    #[serde(rename = "updateTime")]
    pub update_time: Option<String>,
    pub version: Option<String>,
    pub edition: Option<String>,
    #[serde(rename = "publishDate")]
    pub publish_date: Option<String>,
    pub brand: Option<String>,
    pub period: Option<i32>,
    pub grade: Option<String>,
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct NewspaperNodeChildren {
    #[serde(rename = "cardId")]
    pub card_id: u64,
    pub title: String,
    pub isleaf: bool
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct NewspaperNode {
    #[serde(rename = "catalogueId")]
    pub catalogue_id: u64,
    pub title: String,
    pub children: Vec<NewspaperNodeChildren>,
}
impl Book {
    pub(crate) fn from_v1(json_str: &str) -> Result<Vec<Self>, Box<dyn Error>> {
        let books: Vec<Book> = serde_json::from_str(json_str)?;
        Ok(books)
    }
}
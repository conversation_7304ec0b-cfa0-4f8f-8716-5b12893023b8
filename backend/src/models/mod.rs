pub mod assemble;
pub mod book;
pub mod card;
pub mod catalogue;
pub mod activation;
pub mod letter;
pub mod pinyin;
pub mod radical;
pub mod vocabulary;
pub mod maps;
pub mod increment_record;
pub mod inc_file_upd_record;
pub mod law;
pub mod plagiarism;
pub mod surreal;

use serde::Serialize;
use serde_derive::Deserialize;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ResponseVO<T> {
    pub code: u32,
    pub msg: String,
    pub data: Option<T>,
}

impl<T: Serialize> ResponseVO<T> {
    /// 创建一个新的 `ResponseVO`
    pub fn new(code: u32, msg: String, data: Option<T>) -> Self {
        ResponseVO { code, msg, data }
    }

    /// 创建一个成功的响应
    pub fn success(data: Option<T>, message: Option<String>) -> Self {
        Self::new(200, message.unwrap_or_else(|| "Success".to_string()), data)
    }

    /// 创建一个错误的响应
    pub fn error(message: Option<String>) -> Self {
        Self::new(400, message.unwrap_or_else(|| "Error".to_string()), None)
    }

    pub fn server_error() -> Self {
        Self::new(500, "Server Error".to_string(), None)
    }
    /*
     * 作者：张瀚
     * 说明：未授权错误，页面应该回到登录界面
     */
    #[allow(dead_code)]
    pub fn unauthorized(message: Option<String>) -> Self {
        Self::new(401, message.unwrap_or_else(|| "Error".to_string()), None)
    }
}

// impl<T: Serialize> IntoResponse for ResponseVO<T> {
//     fn into_response(self) -> Response {
//         // 将 `ResponseVO` 序列化为 JSON
//         let body = serde_json::to_string(&self).unwrap_or_else(|_| {
//             serde_json::to_string(&ResponseVO::<()>::error(Some(
//                 "Internal Server Error".to_string(),
//             )))
//                 .unwrap()
//         });
//         // 根据 `code` 设置 HTTP 状态码
//         let status_code =
//             StatusCode::from_u16(self.code as u16).unwrap_or(StatusCode::INTERNAL_SERVER_ERROR);
//         let res = (status_code, body).into_response();
//         res
//     }
// }

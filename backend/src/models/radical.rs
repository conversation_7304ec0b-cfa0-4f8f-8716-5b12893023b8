use std::error::Error;
use serde::{Serialize, Deserialize};
use serde_json::Value;
use surrealdb::RecordId;

// # 部首
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct Radical {
    pub id: Option<RecordId>,
    #[serde(rename = "radicalId")]
    pub radical_id: u64,
    #[serde(rename = "bookId")]
    pub book_id: u64,
    pub name: String,
    #[serde(rename = "strokeCount")]
    // # 笔画数
    pub stroke_count: u16,
}


impl Radical {
    pub fn from_v1(json_str: &str) -> Result<Vec<Self>, Box<dyn Error>> {
        let mut json_value: Value = serde_json::from_str(json_str)?;
        if let Value::Array(ref mut arr) = json_value {
            for val in arr.iter_mut() {
                if let Value::Object(map) = val {
                    let opt_id_val = map.swap_remove("id");
                    map.insert("radicalId".to_string(), opt_id_val.unwrap());
                }
            }
        }
        let radicals: Vec<Self> = serde_json::from_value(json_value)?;
        Ok(radicals)
    }
}
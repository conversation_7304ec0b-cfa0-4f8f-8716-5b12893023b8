use std::error::Error;
use serde::{Serialize, Deserialize};
use serde_json::Value;
use surrealdb::RecordId;

// # 词汇
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct Vocabulary {
    pub id: Option<RecordId>,
    #[serde(rename = "vId")]
    pub v_id: u64,
    pub name: String,
    #[serde(rename = "nameOutBase")]
    pub name_out_base: String,
    #[serde(rename = "bookId")]
    pub book_id: u64,
    // # 对应快照页码
    #[serde(rename = "page")]
    pub opt_page: Option<i32>,
    // # 拼音id
    #[serde(rename = "pinyinId")]
    pub opt_pinyin_id: Option<u64>,
    // # 带声调每个字拼音列表
    #[serde(rename = "pinyinList")]
    pub pinyin_list: Vec<String>,
    // # 带声调拼音字符串
    #[serde(rename = "pinyinWithTone")]
    pub pinyin_with_tone: String,
    // # 不带声调拼音字符串
    #[serde(rename = "pinyinWithoutTone")]
    pub opt_pinyin_without_tone: Option<String>,
    // # 单字id列表
    #[serde(rename = "letterIds")]
    pub letter_ids: Vec<i64>,
    // # 单词字符串列表
    pub letters: Vec<String>,
    // # 例证
    pub illustration: String,
}

impl Vocabulary {
    pub fn from_v1(json_str: &str) -> Result<Vec<Self>, Box<dyn Error>> {
        let mut json_value: Value = serde_json::from_str(json_str)?;
        if let Value::Array(ref mut arr) = json_value {
            for val in arr.iter_mut() {
                if let Value::Object(map) = val {
                    let opt_id_val = map.swap_remove("id");
                    map.insert("vId".to_string(), opt_id_val.unwrap());
                }
            }
        }
        let vocabularies: Vec<Self> = serde_json::from_value(json_value)?;
        Ok(vocabularies)
    }
}

#[test]
fn test_from_v1() {
    use std::fs;
    use std::path::Path;

    let json_str = fs::read_to_string(Path::new(r"C:\Users\<USER>\projects\repository-portal-be\app\console\xiandaihanyucidian_vocabulary.json")).unwrap();
    let _vos = Vocabulary::from_v1(&json_str).unwrap();
    // println!("{:#?}", vos);
}

use std::error::Error;
use serde::{Serialize, Deserialize};
use serde_json::Value;
use surrealdb::RecordId;

// # 单个汉字
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct Letter {
    pub id: Option<RecordId>,
    #[serde(rename = "letterId")]
    pub letter_id: u64,
    pub name: String,
    #[serde(rename = "nameOutBase")]
    pub name_out_base: String,
    #[serde(rename = "bookId")]
    pub book_id: u64,
    #[serde(rename = "page")]
    pub opt_page: Option<i32>,
    // 笔画数
    #[serde(rename = "strokeCount")]
    pub opt_stroke_count: Option<u16>,
    // # 字级
    #[serde(rename = "level")]
    pub opt_level: Option<i32>,
    // # 结构
    #[serde(rename = "structure")]
    pub opt_structure: Option<String>,
    // # 起笔
    #[serde(rename = "startStroke")]
    pub opt_start_stroke: Option<String>,
    // # 规定部首的id
    #[serde(rename = "radicalId")]
    pub opt_radical_id: Option<u64>,
    // # 规定部首的名称
    #[serde(rename = "radical")]
    pub opt_radical: Option<String>,
    // # 传统习用部首id列表
    #[serde(rename = "habitualRadicalIds")]
    pub habitual_radical_ids: Vec<u64>,
    // # 传统习用部首的名称列表
    #[serde(rename = "habitualRadicals")]
    pub habitual_radicals: Vec<String>,
    // # 拼音id列表
    #[serde(rename = "pinyinIds")]
    pub opt_pinyin_ids: Option<Vec<u64>>,
    // # 带声调拼音字符串列表
    #[serde(rename = "pinyinWithTones")]
    pub pinyin_with_tones: Vec<String>,
    // # 无声调拼音字符串列表
    #[serde(rename = "pinyinWithoutTones")]
    pub opt_pinyin_without_tones: Option<Vec<String>>,
    // # 例证
    pub illustration: String,
}


impl Letter {
    pub fn from_v1(json_str: &str) -> Result<Vec<Self>, Box<dyn Error>> {
        let mut json_value: Value = serde_json::from_str(json_str)?;
        if let Value::Array(ref mut arr) = json_value {
            for val in arr.iter_mut() {
                if let Value::Object(map) = val {
                    let opt_id_val = map.swap_remove("id");
                    map.insert("letterId".to_string(), opt_id_val.unwrap());
                }
            }
        }
        let letters: Vec<Self> = serde_json::from_value(json_value)?;
        Ok(letters)
    }
}

#[test]
fn test_from_v1() {
    use std::fs;
    use std::path::Path;

    let json_str = fs::read_to_string(Path::new(r"C:\Users\<USER>\projects\repository-portal-be\app\console\xiandaihanyucidian_letter.json")).unwrap();
    let letters = Letter::from_v1(&json_str).unwrap();
    println!("{:#?}", letters);
}
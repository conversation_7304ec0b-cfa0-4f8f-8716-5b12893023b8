use std::error::Error;
use serde_derive::{Deserialize, Serialize};
use serde_json::Value;
use surrealdb::RecordId;

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct QueryNewspaperContext {
    #[serde(rename = "bookId")]
    pub book_id: u64,
    #[serde(rename = "cardId")]
    pub card_id: u64,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct CardInfo {
    pub page: i32,
    pub text: String,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct Context {
    pub content: String,
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct Illustration {
    // pub url: String, // 如果需要使用url字段，取消注释这行
    pub path: String,
}

// 定义 Cards 结构体
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct Card {
    pub id: Option<RecordId>,
    #[serde(rename = "cardId")]
    pub card_id: u64,
    #[serde(rename = "bookId")]
    pub book_id: u64,
    #[serde(rename = "catalogueId")]
    pub catalogue_id: Option<u64>,
    pub title: Option<String>,
    pub content: String,
    pub illustrations: Vec<Illustration>,
    #[serde(rename = "institutionCode")]
    pub institution_code: Option<String>,
    pub page: Option<i32>,
    pub serial: i64,
    #[serde(rename = "updateTime")]
    pub update_time: Option<String>,
    #[serde(rename = "seriesFlag")]
    pub series_flag: Option<i32>,
    #[serde(rename = "cardType")]
    pub card_type: i32,
    #[serde(rename = "publishDate")]
    pub publish_date: Option<String>,
    pub batch: Option<i32>,
}

impl Card {
    pub(crate) fn from_v1(json_str: &str) -> Result<Vec<Self>, Box<dyn Error>> {
        let mut json_value: Value = serde_json::from_str(json_str)?;
        if let Value::Array(ref mut arr) = json_value {
            for val in arr.iter_mut() {
                if let Value::Object(map) = val {
                    let opt_id_val = map.swap_remove("id");
                    map.insert("cardId".to_string(), opt_id_val.unwrap());
                    let opt_content_value = map.get("content");
                    if opt_content_value.is_none() || opt_content_value.unwrap().is_null() {
                        map.insert("content".to_string(), Value::String("".to_string()));
                    }
                }
            }
        }
        let mut cards: Vec<Card> = serde_json::from_value(json_value)?;
        for c in cards.iter_mut() {
            if c.content.contains("\0") {
                c.content = c.content.replace("\0", "");
                println!("cardId:{}, content包含\\0！", c.card_id);
            }
        }
        Ok(cards)
    }
}
use std::collections::HashMap;
use std::error::Erro<PERSON>;
use serde_derive::{Deserialize, Serialize};
use serde_json::Value;
use surrealdb::RecordId;

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct Catalogue {
    pub id: Option<RecordId>,
    #[serde(rename = "catalogueId")]
    pub catalogue_id: u64,
    pub code: String,
    #[serde(rename = "bookId")]
    pub book_id: u64,
    #[serde(rename = "cardSize")]
    pub card_size: u16,
    #[serde(rename = "institutionCode")]
    pub institution_code: Option<i32>,
    pub page: i32,
    pub serial: i32,
    pub title: String,
    #[serde(rename = "pathName")]
    pub path_name: String,
    #[serde(rename = "parentId")]
    pub parent_id: Option<u64>,
    #[serde(rename = "updateTime")]
    pub update_time: Option<String>,
    #[serde(rename = "publishDate")]
    pub publish_date: Option<String>,
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct NewspaperNodeChildren {
    #[serde(rename = "cardId")]
    pub card_id: u64,
    pub key: u64,
    pub page: u64,
    pub title: Option<String>,
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct NewspaperNode {
    #[serde(rename = "catalogueId")]
    pub catalogue_id: u64,
    pub title: String,
    pub children: Vec<NewspaperNodeChildren>
}
impl Catalogue {
    pub(crate) fn from_v1(json_str: &str) -> Result<Vec<Self>, Box<dyn Error>> {
        fn get_code(c_map: &HashMap<u64, &Catalogue>, c: &Catalogue) -> String {
            let serial = c.serial;
            match c.parent_id {
                Some(parent_id) if parent_id != 0 => {
                    if let Some(parent) = c_map.get(&parent_id) {
                        return format!("{}.{}", get_code(c_map, parent), serial + 1);
                    }
                },
                _ => (),
            }
            format!("{}", serial + 1)
        }

        fn get_path_name(c_map: &HashMap<u64, &Catalogue>, c: &Catalogue) -> String {
            let title = &c.title;
            match c.parent_id {
                Some(parent_id) if parent_id != 0 => {
                    if let Some(parent) = c_map.get(&parent_id) {
                        return format!("{} / {}", get_path_name(c_map, parent), title);
                    }
                },
                _ => (),
            }
            format!("{}", title)
        }

        let mut catalogues = vec![];
        let json_value: Value = serde_json::from_str(json_str)?;
        if let Value::Array(arr) = json_value {
            for value in arr {
                let mut map = value.as_object().cloned().unwrap();
                map.insert("catalogueId".to_string(), map.get("id").cloned().unwrap());
                map.remove("id");
                map.insert("code".to_string(), Value::String("".to_string()));
                map.insert("pathName".to_string(), Value::String("".to_string()));
                let catalogue: Self = serde_json::from_value(Value::Object(map))?;
                catalogues.push(catalogue);
            }
        }

        let mut c_map = HashMap::new();
        for c in catalogues.iter() {
            c_map.insert(c.catalogue_id, c);
        }
        let mut cid_code_pn_map = HashMap::new();
        for c in catalogues.iter() {
            let code = get_code(&c_map, c);
            let path_name = get_path_name(&c_map, c);
            cid_code_pn_map.insert(c.catalogue_id, (code, path_name));
        }
        for c in catalogues.iter_mut() {
            let (code, path_name) = cid_code_pn_map.remove(&c.catalogue_id).unwrap();
            c.code = code;
            c.path_name = path_name;
        }

        Ok(catalogues)
    }
}
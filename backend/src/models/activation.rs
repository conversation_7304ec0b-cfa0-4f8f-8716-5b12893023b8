use std::fs;
use chrono::{Duration, Local, NaiveDate};
use rand::rngs::StdRng;
use rand::{Rng, SeedableRng};
use serde_derive::{Deserialize, Serialize};
use surrealdb::RecordId;
use crate::utils::path::get_licence_path;

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct License {
    pub name: String,
    #[serde(rename = "cdKey")]
    pub cd_key: Option<String>,
    pub activation: Option<String>,
}
/// 面向数据库准备的struct，在业务数据的基础上增加id字段
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct LicenseRecord {
    pub id: Option<RecordId>,

    pub name: String,
    #[serde(rename = "cdKey")]
    pub cd_key: Option<String>,
    pub activation: Option<String>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct Activation {
    pub rnd: u32,
    pub key: String,
    pub expiration: NaiveDate,
}

impl License {
    fn new(cd_key: String, activation: String) -> Self {
        License {
            name: "dup-check".to_string(),
            cd_key: Some(cd_key),
            activation: Some(activation),
        }
    }

    /// 从数据库加载License的数据
    pub async fn load_from_db() -> Self {
        let licence_path = get_licence_path().unwrap();
        println!("licence_path: {}", licence_path.display());
        let license_result: Option<License> = fs::read_to_string(licence_path).ok()
            .map(|t| serde_json::from_str(&t).unwrap());
        // 如果查询失败，返回默认的 License
        let license = license_result.unwrap_or_else(|| License {
            name: "dup-check".to_string(),
            cd_key: None,
            activation: None,
        });
        license
    }

    /// 将License的数据储存，保存到数据库
    pub async fn save(&self) -> Result<(), String> {
        let licence_path = get_licence_path()
            .map_err(|e| e.to_string())?;
        let _ = fs::write(licence_path, serde_json::to_string_pretty(self).unwrap().as_bytes())
            .expect("储存失败");
        Ok(())
    }
}

impl Activation {
    /// 通过License生成Activation
    pub async fn new(key: String, expiration: NaiveDate) -> Self {
        let mut rng = StdRng::from_os_rng(); // 生成一个线程安全的随机数生成器
        let rnd = rng.random_range(0..10001); // 生成 0 到 10000 之间的随机数

        let mut activation = Activation {
            rnd,
            key,
            expiration,
        };
        // Get machine-uid
        let machine_uid = match machine_uid::get() {
            Ok(uid) => uid,
            _ => String::new(),
        };

        if !machine_uid.is_empty() {
            let new_key = activation.machine_uid_to_key(&machine_uid);
            if new_key != activation.key {
                // 本机ID不一致，重置激活码
                activation.key = new_key;
                activation.expiration = Local::now().date_naive();
            }
        }
        activation
    }

    fn machine_uid_to_key(&self, machine_uid: &str) -> String {
        let mut tmp = [0u32, 0u32];
        for (i, byte) in machine_uid.bytes().enumerate() {
            let index = i % 2;
            tmp[index] += u32::from(byte) * (u32::from(i as u32 % 2) + 1);
        }
        format!("{:06}", ((tmp[0] + 1) * (tmp[1] + 1)) % 1_000_000)
    }

    fn mask(&self, code: u32, encode: bool) -> i32 {
        let mask = [3, 2, 5, 7, 9, 1];
        let mut ans = 0;
        let mut code = code;
        for i in 0..6 {
            let v: i32 = if encode { mask[i] } else { -mask[i] };
            let tmp = ((code as i32 % 10) + v + 10) % 10;
            ans += tmp * 10i32.pow(i as u32);
            code /= 10;
        }
        ans
    }

    /// 生成机器码
    pub fn get_key(&self) -> u32 {
        self.key.parse().unwrap_or(0) + self.rnd
    }

    pub fn is_same_key(&self, key: String) -> bool {
        self.key == key
    }

    /// 是否合法时间
    pub fn valid(&self) -> bool {
        Local::now().date_naive() < self.expiration
    }

    /// danger!!! 生成一个本机可用的激活码
    pub fn gen_code(&self, key: u32, days: u32) -> String {
        let v = (key / 1000 + key % 1000) % 1000;
        let raw_code = v + days * 1000;
        let code = self.mask(raw_code, true);
        let mut rng = rand::rng();
        let pre = rng.random_range(0..101);
        let tail = 100 - pre;
        format!("{:02}{:06}{:02}", pre, code, tail)
    }

    /// 解析，激活
    pub async fn parse_code(&mut self, code: &str, save: bool) -> bool {
        let v = (self.get_key() / 1000 + self.get_key() % 1000) % 1000;
        if code.len() != 10 {
            return false;
        }
        let pre: u32 = code[..2].parse().unwrap_or(0);
        let tail: u32 = code[8..].parse().unwrap_or(0);
        if pre + tail != 100 {
            return false;
        }
        if let Ok(code) = code[2..8].parse::<u32>() {
            let raw_code = self.mask(code, false);
            if v != <i32 as TryInto<u32>>::try_into(raw_code % 1000).unwrap() {
                return false;
            }
            let days = raw_code / 1000;
            self.expiration = Local::now().date_naive() + Duration::days(days as i64);
            if save {
                self.save().await;
            }
            true
        } else {
            false
        }
    }

    /// 储存，生成License，储存入数据库
    async fn save(&self) {
        let license = License::new(
            self.key.to_string(),
            self.expiration.format("%Y-%m-%d").to_string(),
        );
        let _ = license.save().await;
    }
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ParseCodeParams{
    pub code:String,
}
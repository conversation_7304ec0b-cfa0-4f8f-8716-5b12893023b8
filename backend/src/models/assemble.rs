use std::error::Error;
use serde_derive::{Deserialize, Serialize};
use serde_json::Value;
use surrealdb::RecordId;

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct Assemble {
    pub id: Option<RecordId>,
    #[serde(rename = "assembleId")]
    pub assemble_id: u64,
    pub books: Vec<u64>,
    pub name: String,
    pub publisher: Option<String>,
    #[serde(rename = "subjectCode")]
    pub subject_code: String,
    #[serde(rename = "subjectName")]
    pub subject_name: String,
    #[serde(rename = "type")]
    pub r#type: String,
    #[serde(rename = "recentPublishDate")]
    pub recent_publish_date: Option<String>,
    #[serde(rename = "earliestPublishDate")]
    pub earliest_publish_date: Option<String>,
    pub batch: Option<i32>,
}

impl Assemble {
    pub(crate) fn from_v1(json_str: &str) -> Result<Vec<Self>, Box<dyn Error>> {
        let mut assembles = vec![];
        let json_value: Value = serde_json::from_str(json_str)?;
        if let Value::Array(arr) = json_value {
            for value in arr {
                if let Value::Object(map) = value {
                    let assemble = Self {
                        id: None,
                        assemble_id: map.get("assembleId").unwrap().as_u64().unwrap(),
                        books: map.get("books").unwrap().as_array().unwrap().iter().map(|i| i.as_u64().unwrap()).collect(),
                        name: map.get("name").unwrap().as_str().unwrap().to_string(),
                        publisher: map.get("publisher").unwrap().as_str().map(|i| i.to_string()),
                        subject_code: map.get("subjectCode").unwrap().as_str().unwrap().to_string(),
                        subject_name: map.get("subjectName").unwrap().as_str().unwrap().to_string(),
                        r#type: map.get("type").unwrap().as_str().unwrap().to_string(),
                        recent_publish_date: None,
                        earliest_publish_date: None,
                        batch: None,
                    };
                    assembles.push(assemble);
                }
            }
        }
        Ok(assembles)
    }
}
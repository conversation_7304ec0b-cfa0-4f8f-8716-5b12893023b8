use serde_derive::{Deserialize, Serialize};
use serde_json::Value;
use surrealdb::RecordId;

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetTotalResultLaw {
    pub title: i32,
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct QueryDate {
    pub gt: String,
    pub lt: String
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct QueryLaws {
    #[serde(rename = "currentPage")]
    pub current_page: i32,
    pub expiry: QueryDate,  // 施行日期
    pub office: String, // 制定机关
    pub publish: QueryDate, // 发布日期
    #[serde(rename = "typeCode")]
    pub type_code: String, // 分类标签
    pub status: String, //时效性
    #[serde(rename = "searchValue")]
    pub search_value: String
}
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct GetLaws {
    pub list: Vec<Law>,
    pub total: i32
}
#[derive(Serialize, Deserialize, Debug, <PERSON>lone)]
pub struct Law {
    pub id: Option<RecordId>,
    #[serde(rename = "lawId")]
    pub law_id: String,
    pub title: String,
    pub expiry: String,
    pub office: String,
    pub path: Option<String>,
    pub publish: String,
    pub status: String,
    #[serde(rename = "type")]
    pub r#type: String,
    #[serde(rename = "typeCode")]
    pub type_code: String,
    pub url: String
}

impl Law {
    pub fn from_v1(json_str: &str) -> Result<Vec<Self>, Box<dyn std::error::Error>> {
        let mut json_value: Value = serde_json::from_str(json_str)?;
        if let Value::Array(ref mut arr) = json_value {
            for val in arr.iter_mut() {
                if let Value::Object(map) = val {
                    let opt_id_val = map.swap_remove("id");
                    map.insert("lawId".to_string(), opt_id_val.unwrap());
                }
            }
        }
        let law_list: Vec<Self> = serde_json::from_value(json_value)?;
        Ok(law_list)
    }
}

#[test]
fn test01() {
    use std::fs;
    use std::path::Path;

    let json_path = Path::new(r"D:\增量包\地图册和法律法规\law(1).json");
    let json_str = fs::read_to_string(json_path).unwrap();
    let law_list = Law::from_v1(&json_str).unwrap();
    println!("{:#?}", law_list);
}
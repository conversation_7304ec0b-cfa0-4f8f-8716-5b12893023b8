use std::path::PathBuf;
use chrono::{DateTime, Local};
use serde_derive::{Deserialize, Serialize};
use surrealdb::RecordId;
use uuid::Uuid;
use crate::utils::time_util::now_datetime_local;

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct IncrementRecord {
    pub id: Option<RecordId>,
    #[serde(rename = "rId")]
    pub r_id: String,
    #[serde(rename = "filePath")]
    pub file_path: PathBuf,
    #[serde(rename = "fileName")]
    pub file_name: String,
    #[serde(rename = "createTime")]
    pub create_time: DateTime<Local>,
    #[serde(rename = "updateTime")]
    pub update_time: DateTime<Local>,
    #[serde(rename = "handledTime")]
    pub opt_handled_time: Option<DateTime<Local>>,
    // 状态枚举，0：未开始导入；1：导入中；2：导入完成；3：导入时出错
    pub status: i8,
    #[serde(rename = "msg")]
    pub opt_msg: Option<String>,
    #[serde(rename = "deleteFile")]
    pub delete_file: bool,
}

impl IncrementRecord {
    pub fn new(
        file_path: PathBuf, delete_file: bool
    ) -> Result<Self, Box<dyn std::error::Error>> {
        let r_id = Uuid::new_v4().to_string();
        let file_name = file_path.file_name()
            .ok_or(anyhow::Error::msg("找不到文件名"))?
            .to_str()
            .ok_or(anyhow::Error::msg("找不到文件名"))?
            .to_string();
        let entity = Self {
            id: None,
            r_id,
            file_path,
            file_name,
            create_time: now_datetime_local(),
            update_time: now_datetime_local(),
            opt_handled_time: None,
            status: 0,
            opt_msg: None,
            delete_file,
        };
        Ok(entity)
    }
}


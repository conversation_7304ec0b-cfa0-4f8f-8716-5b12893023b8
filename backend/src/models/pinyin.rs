use std::error::Error;
use serde::{Serialize, Deserialize};
use serde_json::Value;
use surrealdb::RecordId;


// 单字和词汇的拼音，以withTone唯一表示一条
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct Pinyin {
    pub id: Option<RecordId>,
    #[serde(rename = "pinyinId")]
    pub pinyin_id: u64,
    #[serde(rename = "bookId")]
    pub book_id: u64,
    // # 是否单个汉字的拼音
    #[serde(rename = "isSingleKanji")]
    pub is_single_kanji: bool,
    // # 首字母（大写）
    #[serde(rename = "firstChar")]
    pub first_char: String,
    // # 无声调拼音
    #[serde(rename = "withoutTone")]
    pub without_tone: String,
    // # 有声调拼音
    #[serde(rename = "withTone")]
    pub with_tone: String,
}


impl Pinyin {
    pub fn from_v1(json_str: &str) -> Result<Vec<Self>, Box<dyn Error>> {
        let mut json_value: Value = serde_json::from_str(json_str)?;
        if let Value::Array(ref mut arr) = json_value {
            for val in arr.iter_mut() {
                if let Value::Object(map) = val {
                    let opt_id_val = map.swap_remove("id");
                    map.insert("pinyinId".to_string(), opt_id_val.unwrap());
                }
            }
        }
        let pinyin_list: Vec<Self> = serde_json::from_value(json_value)?;
        Ok(pinyin_list)
    }
}
use std::path::PathBuf;
use chrono::{DateTime, Local};
use serde_derive::{Deserialize, Serialize};
use surrealdb::RecordId;
use uuid::Uuid;
use crate::utils::time_util::now_datetime_local;

#[derive(Debug, <PERSON><PERSON>, Deserialize, Serialize)]
pub struct IncFileUpdRecord {
    pub id: Option<RecordId>,
    #[serde(rename = "rId")]
    pub r_id: String,
    #[serde(rename = "fileName")]
    pub file_name: String,
    #[serde(rename = "localFilePath")]
    pub local_file_path: PathBuf,
    #[serde(rename = "remoteFilePath")]
    pub opt_remote_file_path: Option<String>,
    // 状态枚举，0：未上传；1：上传中；2：上传成功；3：上传失败
    pub status: i8,
    // 上传进度百分比
    #[serde(rename = "updPercentage")]
    pub upd_percentage: f32,
    #[serde(rename = "createTime")]
    pub create_time: DateTime<Local>,
    #[serde(rename = "updateTime")]
    pub update_time: DateTime<Local>,
    #[serde(rename = "handledTime")]
    pub opt_handled_time: Option<DateTime<Local>>,
    #[serde(rename = "msg")]
    pub opt_msg: Option<String>,
}

impl IncFileUpdRecord {
    pub fn new(file_path: PathBuf) -> Result<Self, Box<dyn std::error::Error>> {
        let r_id = Uuid::new_v4().to_string();
        let file_name = file_path.file_name()
            .ok_or(anyhow::Error::msg("找不到文件名"))?
            .to_str()
            .ok_or(anyhow::Error::msg("找不到文件名"))?
            .to_string();
        let entity = Self {
            id: None,
            r_id,
            file_name,
            local_file_path: file_path,
            create_time: now_datetime_local(),
            update_time: now_datetime_local(),
            opt_handled_time: None,
            status: 0,
            opt_msg: None,
            opt_remote_file_path: None,
            upd_percentage: 0.0,
        };
        Ok(entity)
    }
}
use std::error::Error;
use crate::database::{create_surreal_db_index, IndexType};
use crate::database::surreal::get_db;
use crate::models::radical::Radical;

pub(crate) struct RadicalRepository;


impl RadicalRepository {
    pub(crate) async fn create_indexes() -> Result<(), Box<dyn Error>> {
        let db= get_db();
        create_surreal_db_index(&db, "radical", vec![vec!["radicalId"]], IndexType::Unique).await?;
        Ok(())
    }

    pub(crate) async fn find_by_id(id: u64) -> Result<Option<Radical>, Box<dyn Error>> {
        let db= get_db();
        let mut resp = db
            .query("SELECT * FROM radical WHERE radicalId = $id")
            .bind(("id", id))
            .await?;
        let opt_radical: Option<Radical> = resp.take(0)?;
        Ok(opt_radical)
    }

    pub(crate) async fn find_by_id_and_book_id(id: u64, book_id: u64) -> Result<Option<Radical>, Box<dyn Error>> {
        let db= get_db();
        let mut resp = db
            .query("SELECT * FROM radical WHERE radicalId = $id AND bookId = $bookId")
            .bind(("id", id))
            .bind(("bookId", book_id))
            .await?;
        let opt_radical: Option<Radical> = resp.take(0)?;
        Ok(opt_radical)
    }

    pub(crate) async fn find_all_by_book_id(book_id: u64) -> Result<Vec<Radical>, Box<dyn Error>> {
        let db= get_db();
        let mut resp = db
            .query("SELECT * FROM radical WHERE bookId = $bookId")
            .bind(("bookId", book_id))
            .await?;
        let radicals: Vec<Radical> = resp.take(0)?;
        Ok(radicals)
    }

    pub(crate) async fn save(radical: &Radical) -> Result<(), Box<dyn Error>> {
        let existing_record = Self::find_by_id(radical.radical_id).await?;
        let db= get_db();
        if existing_record.is_none() {
            let _: Option<Radical> = db.create("radical").content(radical.clone()).await?;
        } else {
            let id = existing_record.unwrap().id.unwrap();
            let _r: Option<Radical> = db.update(id).content(radical.clone()).await?;
        }
        Ok(())
    }

    pub(crate) async fn save_all(radicals: &Vec<Radical>) -> Result<(), Box<dyn Error>> {
        for radical in radicals {
            Self::save(radical).await?;
        }
        Ok(())
    }
}
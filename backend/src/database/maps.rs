use std::error::Error;
use crate::database::{create_surreal_db_index, IndexType};
use crate::database::surreal::get_db;
use crate::models::maps::{GetMaps, GetMapsBreadCrumb, GetMapsBreadCrumbChild, GetMapsBreadCrumbChildChild,
      GetMapsFilters, GetMapsFiltersBorder, GetMapsFiltersChild, GetMapsFiltersChild2,
      GetMapsFiltersIllustration, GetMapsFiltersNeighbouringCountry, GetMapsFiltersProvinceColor,
      GetMapsFiltersScale, GetMapsFiltersSize, GetMapsLargeclass, GetMapsSubclass, GetMapsSuperclass,
      GetTotalResult, Maps, QueryMaps, QueryMapsFilter};

pub async fn get_total_by_sql(query: &str) -> Result<i32, Box<dyn Error>> {
    let db = get_db();
    let result: Vec<GetTotalResult> = db.query(query).await?.take(0)?;
    match result.get(0) {
        Some(v) => Ok(v.name),
        None => Ok(0),
    }
}
pub async fn get_maps(
    params: QueryMaps
) -> Result<GetMaps, Box<dyn Error>> {
    let QueryMaps {
        current_page,
        subclass,  // 大标签
        superclass, // 分类标签
        largeclass,
        border, // 表现形式
        size, //规格
        scale, //比例尺
        neighbouring_country, // 邻国
        province_color, // 分省设色
        illustration, // 南海诸岛以附图表示
        search_value
    } = params;
    //拼接可选参数
    let mut query = String::from("where");
    if subclass != "" {
        query.push_str(&format!(" subclass == '{}'", subclass));
    }
    if superclass != "" {
        if subclass != "" {
            query.push_str(&format!(" and superclass == '{}'", superclass));
        } else {
            query.push_str(&format!(" superclass == '{}'", superclass));
        }
    }
    if largeclass != "" {
        if (subclass != "")
            || (superclass != "")
        {
            query.push_str(&format!(" and largeclass == '{}'", largeclass));
        } else {
            query.push_str(&format!(" largeclass == '{}'", largeclass));
        }
    }
    if !border.is_empty() {
        let mut first = true; // 用于跟踪是否是第一个年份，以避免在开头添加 "or"
        for maybe_border in border.clone() {
            if let Some(bor) = maybe_border {
                if !first {
                    query.push_str(" or");
                    query.push_str(&format!(" border == {}", bor));
                }
                else {
                    if (subclass != "")
                        || (superclass != "")
                        || (largeclass != "")
                    {
                        query.push_str(&format!(" and (border == '{}'", bor));
                    } else {
                        query.push_str(&format!(" (border == '{}'", bor));
                    }
                    first = false;
                }
            }
        }
        query.push_str(")");
    }
    if size != "" {
        if (subclass != "")
            || (superclass != "")
            || (largeclass != "")
            || (!border.is_empty())
        {
            query.push_str(&format!(" and size == '{}'", size));
        } else {
            query.push_str(&format!(" size == '{}'", size));
        }
    }
    if scale != "" {
        if (subclass != "")
            || (superclass != "")
            || (largeclass != "")
            || (!border.is_empty())
            || (size != "")
        {
            query.push_str(&format!(" and scale == '{}'", scale));
        } else {
            query.push_str(&format!(" scale == '{}'", scale));
        }
    }
    if neighbouring_country !=3 {
        if (subclass != "")
            || (superclass != "")
            || (largeclass != "")
            || (!border.is_empty())
            || (size != "")
            || (scale != "")
        {
            query.push_str(&format!(" and neighbouringCountry == {}", neighbouring_country));
        } else {
            query.push_str(&format!(" neighbouringCountry == {}", neighbouring_country));
        }
    }
    if province_color !=3 {
        if (subclass != "")
            || (superclass != "")
            || (largeclass != "")
            || (!border.is_empty())
            || (size != "")
            || (scale != "")
            || (neighbouring_country !=3)
        {
            query.push_str(&format!(" and provinceColor == {}", province_color));
        } else {
            query.push_str(&format!(" provinceColor == {}", province_color));
        }
    }
    if illustration !=3 {
        if (subclass != "")
            || (superclass != "")
            || (largeclass != "")
            || (!border.is_empty())
            || (size != "")
            || (scale != "")
            || (neighbouring_country !=3)
            || (province_color !=3)
        {
            query.push_str(&format!(" and illustration == {}", illustration));
        } else {
            query.push_str(&format!(" illustration == {}", illustration));
        }
    }
    if search_value != "" {
        if (subclass != "")
            || (superclass != "")
            || (largeclass != "")
            || (!border.is_empty())
            || (size != "")
            || (scale != "")
            || (neighbouring_country !=3)
            || (province_color !=3)
            || (illustration !=3)
        {
            query.push_str(&format!(" and (name ?~ '{}' or mapNumber ?~ '{}')", search_value,search_value));
        } else {
            query.push_str(&format!(" (name ?~ '{}' or mapNumber ?~ '{}')", search_value,search_value));
        }
    }
    let db= get_db();
    let base_query = if query.len() == 5 {
        db.query("select * from map order by mapYear desc limit $limit start $start")
    } else {
        db.query(format!(
            "select * from map {} order by mapYear desc limit $limit start $start",
            query
        ))
    };
    let total_query = if query.len() == 5 {
        "select count() as name from map group all"
    } else {
        &format!("select count() as name from map {} group all", query)
    };

    let list: Vec<Maps> = base_query
        .bind(("limit", 12))
        .bind(("start", (current_page - 1) * 12))
        .await?
        .take(0)?;
    let total = get_total_by_sql(total_query).await?;
    let result: GetMaps = GetMaps {
        list,
        total
    };
    Ok(result)
}
pub async fn get_maps_total() -> Result<i32, Box<dyn Error>> {
    let total_query = "select count() as name from map group all";
    let total = get_total_by_sql(total_query).await?;
    Ok(total)
}
pub async fn get_maps_bread_crumb() -> Result<Vec<GetMapsBreadCrumb>, Box<dyn Error>> {
    let db= get_db();
    let subclass:Vec<GetMapsSubclass> = db.query(
        "select array::group(subclass) AS subclass from map where subclass!=NONE group all")
        .await?
        .take(0)?;
    let mut list:Vec<GetMapsBreadCrumb> = vec![];
    let mut sub_iter = subclass[0].subclass.iter();
    while let Some(sub) = sub_iter.next() {
        let superclass:Vec<GetMapsSuperclass> = db.query(
            format!(
                "select array::group(superclass) AS superclass from map where (superclass!=NONE and subclass='{}') group all",
                sub))
            .await?
            .take(0)?;
        let mut child:Vec<GetMapsBreadCrumbChild> = vec![];
        let mut super_iter = superclass[0].superclass.iter();
        while let Some(sup) = super_iter.next() {
            let largeclass:Vec<GetMapsLargeclass> = db.query(
                format!(
                    "select array::group(largeclass) AS largeclass from map where largeclass!=NONE and largeclass!='' and superclass == '{}' group all",
                    sup
                ))
                .await?
                .take(0)?;
            let mut child_child:Vec<GetMapsBreadCrumbChildChild> = vec![];
            if !largeclass.is_empty() {
                let mut large_iter = largeclass[0].largeclass.iter();
                while let Some(large) = large_iter.next() {
                    child_child.push(GetMapsBreadCrumbChildChild{
                        name: large.to_string().clone(),
                        parent: sup.to_string().clone(),
                    })
                }
            };
            child.push(GetMapsBreadCrumbChild {
                name: sup.to_string().clone(),
                parent: sub.clone(),
                children: child_child
            });
        }
        list.push(GetMapsBreadCrumb{
            name: sub.to_string().clone(),
            children: child
        })
    }
    Ok(list)
}
pub async fn get_maps_filters(
    query: QueryMapsFilter
) -> Result<GetMapsFilters, Box<dyn Error>> {
    let QueryMapsFilter {
        name,
        index
    } = query;
    let mut sql = String::new();
    if index == 1 {
        sql = format!(" and subclass=='{}'",name);
    } else if index == 2 {
        sql = format!(" and superclass=='{}'",name);
    } else if index == 3 {
        sql = format!(" and largeclass=='{}'",name);
    }
    let db= get_db();
    let border:Vec<GetMapsFiltersBorder> = db.query(
        format!("select array::group(border) AS border from map where (border!=NONE and border!=''{}) group all;",
        sql))
        .await?
        .take(0)?;
    let mut border_list:Vec<GetMapsFiltersChild> = vec![];
    if !border.is_empty() {
        let mut border_iter = border[0].border.iter();
        while let Some(bor) = border_iter.next() {
            border_list.push(GetMapsFiltersChild{
                label: bor.to_string().clone(),
                value: bor.to_string().clone()
            });
        }
    }
    let size:Vec<GetMapsFiltersSize> = db.query(format!(
        "select array::group(size) AS size from map where (size!=NONE and size!=''{}) group all;",
        sql))
        .await?
        .take(0)?;
    let mut size_list:Vec<GetMapsFiltersChild> = vec![];
    if !size.is_empty() {
        let mut size_iter = size[0].size.iter();
        while let Some(siz) = size_iter.next() {
            size_list.push(GetMapsFiltersChild{
                label: siz.to_string().clone(),
                value: siz.to_string().clone()
            });
        }
    }
    let scale:Vec<GetMapsFiltersScale> = db.query(format!(
        "select array::group(scale) AS scale from map where (scale!=NONE and scale!=''{}) group all;",
        sql))
        .await?
        .take(0)?;
    let mut scale_list:Vec<GetMapsFiltersChild> = vec![];
    if !scale.is_empty() {
        let mut scale_iter = scale[0].scale.iter();
        while let Some(sca) = scale_iter.next() {
            scale_list.push(GetMapsFiltersChild{
                label: sca.to_string().clone(),
                value: sca.to_string().clone()
            });
        }
    }
    let neighbouring_country:Vec<GetMapsFiltersNeighbouringCountry> = db.query(format!(
        "select array::group(neighbouringCountry) AS neighbouringCountry from map where (neighbouringCountry!=NONE and neighbouringCountry!=''{}) group all;",
        sql))
        .await?
        .take(0)?;
    let mut neighbouring_country_list:Vec<GetMapsFiltersChild2> = vec![];
    if !neighbouring_country.is_empty() {
        let mut neighbouring_country_iter = neighbouring_country[0].neighbouring_country.iter();
        while let Some(sca) = neighbouring_country_iter.next() {
            if sca.to_string() == "0" {
                neighbouring_country_list.push(GetMapsFiltersChild2{
                    label: "无".to_string(),
                    value: 0
                });
            } else if sca.to_string() == "1" {
                neighbouring_country_list.push(GetMapsFiltersChild2{
                    label: "有".to_string(),
                    value: 1
                });
            }
        }
    }
    let province_color:Vec<GetMapsFiltersProvinceColor> = db.query(format!(
        "select array::group(provinceColor) AS provinceColor from map where (provinceColor!=NONE and provinceColor!=''{}) group all;",
        sql))
        .await?
        .take(0)?;
    let mut province_color_list:Vec<GetMapsFiltersChild2> = vec![];
    if !province_color.is_empty() {
        let mut province_color_iter = province_color[0].province_color.iter();
        while let Some(sca) = province_color_iter.next() {
            if sca.to_string() == "0" {
                province_color_list.push(GetMapsFiltersChild2{
                    label: "否".to_string(),
                    value: 0
                });
            } else if sca.to_string() == "1" {
                province_color_list.push(GetMapsFiltersChild2{
                    label: "是".to_string(),
                    value: 1
                });
            }
        }
    }
    let illustration:Vec<GetMapsFiltersIllustration> = db.query(format!(
        "select array::group(illustration) AS illustration from map where (illustration!=NONE and illustration!=''{}) group all;",
        sql))
        .await?
        .take(0)?;
    let mut illustration_list:Vec<GetMapsFiltersChild2> = vec![];
    if !illustration.is_empty() {
        let mut illustration_iter = illustration[0].illustration.iter();
        while let Some(sca) = illustration_iter.next() {
            if sca.to_string() == "0" {
                illustration_list.push(GetMapsFiltersChild2{
                    label: "否".to_string(),
                    value: 0
                });
            } else if sca.to_string() == "1" {
                illustration_list.push(GetMapsFiltersChild2{
                    label: "是".to_string(),
                    value: 1
                });
            }
        }
    }
    let result:GetMapsFilters = GetMapsFilters{
        border: border_list,
        size: size_list,
        scale: scale_list,
        neighbouring_country: neighbouring_country_list,
        province_color: province_color_list,
        illustration: illustration_list
    };
    Ok(result)
}

pub struct MapRepository;

impl MapRepository {
    pub(crate) async fn create_indexes() -> Result<(), Box<dyn Error>> {
        let db= get_db();
        create_surreal_db_index(&db, "map", vec![vec!["mapId"]], IndexType::Unique).await?;
        create_surreal_db_index(&db, "map", vec![vec!["subclass"],vec!["superclass"],vec!["largeclass"]], IndexType::Normal).await?;
        Ok(())
    }

    pub(crate) async fn find_by_id(id: String) -> Result<Option<Maps>, Box<dyn Error>> {
        let db= get_db();
        let mut resp = db
            .query("SELECT * FROM map WHERE mapId = $id")
            .bind(("id", id))
            .await?;
        let opt_map: Option<Maps> = resp.take(0)?;
        Ok(opt_map)
    }

    pub(crate) async fn save(map: &Maps) -> Result<(), Box<dyn Error>> {
        let existing_record = Self::find_by_id(map.map_id.clone()).await?;
        let db= get_db();
        if existing_record.is_none() {
            let _: Option<Maps> = db.create("map").content(map.clone()).await?;
        } else {
            let id = existing_record.unwrap().id.unwrap();
            let _r: Option<Maps> = db.update(id).content(map.clone()).await?;
        }
        Ok(())
    }

    pub(crate) async fn save_all(maps: &Vec<Maps>) -> Result<(), Box<dyn Error>> {
        for map in maps {
            Self::save(map).await?;
        }
        Ok(())
    }
}
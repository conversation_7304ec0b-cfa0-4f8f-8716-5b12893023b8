use crate::database::{create_surreal_db_index, IndexType};
use crate::database::surreal::get_db;
use crate::models::plagiarism::SentenceMatchDb;
use crate::commands::plagiarism::BatchStatistics;
use anyhow::Result;

pub struct SentenceMatchRepository;

impl SentenceMatchRepository {
    /// 创建数据库索引
    pub async fn create_indexes() -> Result<()> {
        let db = get_db();
        create_surreal_db_index(&db, "sentence_match", vec![vec!["matchId"]], IndexType::Unique).await?;
        create_surreal_db_index(&db, "sentence_match", vec![vec!["batchId"], vec!["similarity"], vec!["matchType"]], IndexType::Normal).await?;
        Ok(())
    }

    /// 批量保存句子匹配结果
    pub async fn save_batch(matches: &[SentenceMatchDb]) -> Result<()> {
        let db = get_db();

        // 批量插入数据
        for match_item in matches {
            let _: Option<SentenceMatchDb> = db.create("sentence_match").content(match_item.clone()).await?;
        }

        Ok(())
    }

    /// 保存单个句子匹配结果
    #[allow(dead_code)]
    pub async fn save(match_item: &SentenceMatchDb) -> Result<()> {
        let db = get_db();
        let _: Option<SentenceMatchDb> = db.create("sentence_match").content(match_item.clone()).await?;
        Ok(())
    }

    /// 根据批次ID分页查询匹配结果
    pub async fn find_by_batch_id_paginated(
        batch_id: u64,
        page_no: i32,
        page_size: i32,
        min_similarity: Option<f64>,
        max_similarity: Option<f64>,
        match_type: Option<String>,
        source_book_id: Option<String>,
        target_book_id: Option<String>,
    ) -> Result<(Vec<SentenceMatchDb>, i32)> {
        let db = get_db();

        // 构建查询条件
        let mut where_conditions = vec![format!("batchId = {}", batch_id)];

        if let Some(min_sim) = min_similarity {
            where_conditions.push(format!("similarity >= {}", min_sim));
        }

        if let Some(max_sim) = max_similarity {
            where_conditions.push(format!("similarity <= {}", max_sim));
        }

        if let Some(match_type_val) = match_type {
            where_conditions.push(format!("matchType = '{}'", match_type_val));
        }

        if let Some(source_book_id_val) = source_book_id {
            where_conditions.push(format!("sourceBookId = '{}'", source_book_id_val));
        }

        if let Some(target_book_id_val) = target_book_id {
            where_conditions.push(format!("targetBookId = '{}'", target_book_id_val));
        }

        let where_clause = format!("WHERE {}", where_conditions.join(" AND "));

        // 查询总数
        let count_query = format!("COUNT(SELECT * FROM sentence_match {})", where_clause);
        let mut count_resp = db.query(&count_query).await?;
        let total: Option<i64> = count_resp.take(0)?;
        let total_count = total.unwrap_or(0) as i32;

        // 分页查询数据
        let offset = page_no * page_size;
        let data_query = format!(
            "SELECT * FROM sentence_match {} ORDER BY similarity DESC, createTime DESC LIMIT {} START {}",
            where_clause, page_size, offset
        );
        let mut data_resp = db.query(&data_query).await?;
        let matches: Vec<SentenceMatchDb> = data_resp.take(0)?;

        Ok((matches, total_count))
    }

    /// 根据批次ID获取所有匹配结果（用于统计）
    pub async fn find_all_by_batch_id(batch_id: u64) -> Result<Vec<SentenceMatchDb>> {
        let db = get_db();
        let mut resp = db
            .query("SELECT * FROM sentence_match WHERE batchId = $batch_id ORDER BY similarity DESC")
            .bind(("batch_id", batch_id))
            .await?;
        let matches: Vec<SentenceMatchDb> = resp.take(0)?;
        Ok(matches)
    }

    /// 根据批次ID删除所有匹配结果
    pub async fn delete_by_batch_id(batch_id: u64) -> Result<i32> {
        let db = get_db();
        let query = format!("DELETE FROM sentence_match WHERE batchId = {}", batch_id);
        let mut resp = db.query(&query).await?;
        let deleted_results: Vec<SentenceMatchDb> = resp.take(0)?;
        Ok(deleted_results.len() as i32)
    }

    /// 获取批次统计信息
    pub async fn get_batch_statistics(batch_id: u64) -> Result<Option<BatchStatistics>> {
        let matches = Self::find_all_by_batch_id(batch_id).await?;
        
        if matches.is_empty() {
            return Ok(None);
        }
        
        let total_matches = matches.len() as i32;
        let exact_matches = matches.iter().filter(|m| m.match_type == "exact").count() as i32;
        let similar_matches = matches.iter().filter(|m| m.match_type == "similar").count() as i32;
        let partial_matches = matches.iter().filter(|m| m.match_type == "partial").count() as i32;
        
        let total_similarity: f64 = matches.iter().map(|m| m.similarity).sum();
        let average_similarity = if total_matches > 0 {
            total_similarity / total_matches as f64
        } else {
            0.0
        };
        
        let high_similarity_matches = matches.iter().filter(|m| m.similarity > 0.8).count() as i32;
        let medium_similarity_matches = matches.iter().filter(|m| m.similarity >= 0.5 && m.similarity <= 0.8).count() as i32;
        let low_similarity_matches = matches.iter().filter(|m| m.similarity < 0.5).count() as i32;
        
        Ok(Some(BatchStatistics {
            batch_id: batch_id.to_string(),
            total_books: 0, // 这个需要从批次信息中获取
            total_sentences: 0, // 这个需要根据实际情况计算
            total_matches,
            exact_matches,
            similar_matches,
            partial_matches,
            average_similarity,
            high_similarity_matches,
            medium_similarity_matches,
            low_similarity_matches,
        }))
    }
}



use lazy_static::lazy_static;
use serde_derive::Deserialize;
use std::sync::Mutex;
use surrealdb::engine::local::{Db, RocksDb};
use surrealdb::opt::Config;
use surrealdb::RecordId;
use surrealdb::Surreal;
use crate::utils::path::get_surreal_db_dir;

#[derive(Debug, Deserialize)]
pub struct Record {
    #[allow(dead_code)]
    pub id: RecordId,
}

lazy_static! {
    static ref DB: Mutex<Option<Surreal<Db>>> = Mutex::new(None);
}

pub async fn init_db() -> Result<String, String> {
    let s_db = get_surreal_db_dir().map_err(|e| e.to_string())?;
    log::info!("Init surrealDB in app path: {}", s_db.display());
    let database_result = Surreal::new::<RocksDb>((s_db, Config::default())).await;
    if database_result.is_ok() {
        let db = database_result.unwrap();
        db.use_ns("qct").use_db("dup-check").await.unwrap();

        let mut db_option = DB.lock().unwrap();
        *db_option = Some(db);
        log::info!("SurrealDB init successfully");
        return Ok("SurrealDB init successfully".to_string());
    }
    log::error!("SurrealDB init fail");
    Err("SurrealDB init fail".to_string())
}

#[allow(dead_code)]
pub fn is_db_init() -> bool {
    let db_option = DB.lock().unwrap();
    db_option.is_some()
}

pub fn get_db() -> Surreal<Db> {
    let db_option = DB.lock().unwrap();
    let db = db_option.clone().unwrap();
    db
}

#[allow(dead_code)]
pub fn close_db(){
    let mut db_option = DB.lock().unwrap();
    *db_option = None;
}
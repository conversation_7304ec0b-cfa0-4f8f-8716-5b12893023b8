use std::error::Error;
use crate::database::{create_surreal_db_index, IndexType};
use crate::database::surreal::get_db;
use crate::models::letter::Letter;

pub(crate) struct LetterRepository;


impl LetterRepository {
    pub(crate) async fn create_indexes() -> Result<(), Box<dyn Error>> {
        let db= get_db();
        create_surreal_db_index(&db, "letter", vec![vec!["letterId"]], IndexType::Unique).await?;
        Ok(())
    }

    pub(crate) async fn find_by_id(id: u64) -> Result<Option<Letter>, Box<dyn Error>> {
        let db= get_db();
        let mut resp = db
            .query("SELECT * FROM letter WHERE letterId = $id")
            .bind(("id", id))
            .await?;
        let opt_letter: Option<Letter> = resp.take(0)?;
        Ok(opt_letter)
    }

    pub(crate) async fn find_all() -> Result<Vec<Letter>, Box<dyn Error>> {
        let db= get_db();
        let mut resp = db
            .query("SELECT * FROM letter")
            .await?;
        let letters: Vec<Letter> = resp.take(0)?;
        Ok(letters)
    }

    pub(crate) async fn save(letter: &Letter) -> Result<(), Box<dyn Error>> {
        let existing_record = Self::find_by_id(letter.letter_id).await?;
        let db= get_db();
        if existing_record.is_none() {
            let _: Option<Letter> = db.create("letter").content(letter.clone()).await?;
        } else {
            let id = existing_record.unwrap().id.unwrap();
            let _r: Option<Letter> = db.update(id).content(letter.clone()).await?;
        }
        Ok(())
    }

    pub(crate) async fn save_all(letters: &Vec<Letter>) -> Result<(), Box<dyn Error>> {
        for letter in letters {
            Self::save(letter).await?;
        }
        Ok(())
    }
}
use std::error::Error;
use crate::database::{create_surreal_db_index, IndexType};
use crate::database::surreal::get_db;
use crate::models::card::Card;
use crate::models::catalogue::{Catalogue, NewspaperNode, NewspaperNodeChildren};
pub(crate) struct CatalogueRepository;
pub async fn get_newspaper_catalogue_by_book_id(
    book_id: String
) -> Result<Vec<NewspaperNode>, surrealdb::Error>{
    println!("book_id: {}", book_id);
    let db = get_db();
    let mut title = db.query(format!("SELECT * FROM catalogue WHERE bookId = {} ORDER BY catalogueId", book_id))
        .await?;
    let titles:Vec<Catalogue> = title
        .take(0)?;
    let mut list:Vec<NewspaperNode> = vec![];
    let mut titles_iter = titles.iter();
    while let Some(title_item) = titles_iter.next() {
        let mut children = db.query(
            format!(
                "SELECT * FROM card WHERE (bookId = {} and catalogueId = {}) ORDER BY cardId",
                book_id,title_item.clone().catalogue_id
            ))
            .await?;
        let childrens:Vec<Card> = children
            .take(0)?;
        let mut itt:Vec<NewspaperNodeChildren> = vec![];
        let mut card_iter = childrens.iter();
        let mut page: u64 = 1;
        while let Some(card_item) = card_iter.next() {
            let card:NewspaperNodeChildren = NewspaperNodeChildren {
                card_id: card_item.clone().card_id,
                key: card_item.clone().card_id,
                page:page,
                title: card_item.clone().title,
            };
            itt.push(card);
            page += 1;
        }
        let node:NewspaperNode = NewspaperNode {
            catalogue_id:title_item.clone().catalogue_id,
            title:title_item.clone().title,
            children: itt
        };
        list.push(node);
    }
    Ok(list)
}
impl CatalogueRepository {
    pub(crate) async fn create_indexes() -> Result<(), Box<dyn Error>> {
        let db = get_db();
        create_surreal_db_index(&db, "catalogue", vec![vec!["catalogueId"]], IndexType::Unique).await?;
        create_surreal_db_index(&db, "catalogue", vec![vec!["bookId"]], IndexType::Normal).await?;
        Ok(())
    }

    pub(crate) async fn find_by_id(id: u64) -> Result<Option<Catalogue>, Box<dyn Error>> {
        let db = get_db();
        let mut resp = db
            .query("SELECT * FROM catalogue WHERE catalogueId = $id")
            .bind(("id", id))
            .await?;
        let opt_catalogue: Option<Catalogue> = resp.take(0)?;
        Ok(opt_catalogue)
    }

    pub(crate) async fn find_all_by_ids(ids: &Vec<u64>) -> Result<Vec<Catalogue>, Box<dyn Error>> {
        let db = get_db();
        let temp: Vec<String> = ids.iter().map(|id| id.to_string()).collect();
        let sql = format!("SELECT * FROM catalogue WHERE catalogueId IN [{}]", temp.join(","));
        let mut resp = db.query(sql).await?;
        let list: Vec<Catalogue> = resp.take(0)?;
        Ok(list)
    }

    pub async fn find_all_by_book_id(
        book_id: String
    ) -> Result<Vec<Catalogue>, surrealdb::Error>{
        let db = get_db();
        let mut book = db.query(format!("SELECT * FROM catalogue WHERE bookId = {} ORDER BY catalogueId", book_id))
            .await?;
        let result = book
            .take(0)?;
        Ok(result)
    }

    pub(crate) async fn save(catalogue: &Catalogue) -> Result<(), Box<dyn Error>> {
        let existing_record: Option<Catalogue> =Self::find_by_id(catalogue.catalogue_id).await?;
        let db = get_db();
        if existing_record.is_none() {
            let _: Option<Catalogue> = db.create("catalogue").content(catalogue.clone()).await?;
        } else {
            let id = existing_record.unwrap().id.unwrap();
            let _r: Option<Catalogue> = db.update(id).content(catalogue.clone()).await?;
        }
        Ok(())
    }

    pub(crate) async fn save_all(catalogues: &Vec<Catalogue>) -> Result<(), Box<dyn Error>> {
        for catalogue in catalogues {
            Self::save(catalogue).await?;
        }
        Ok(())
    }
}
use std::collections::HashSet;
use std::error::Error;
use indexmap::{IndexMap, IndexSet};
use crate::database::{create_surreal_db_index, IndexType};
use crate::database::surreal::get_db;
use crate::models::assemble::Assemble;
pub(crate) struct AssembleRepository;

impl AssembleRepository {
    pub(crate) async fn create_indexes() -> Result<(), Box<dyn Error>> {
        let db = get_db();
        create_surreal_db_index(&db, "assemble", vec![vec!["assembleId"]], IndexType::Unique).await?;
        Ok(())
    }

    pub(crate) async fn find_all_by_book_id_in(
        book_ids: &Vec<u64>
    ) -> Result<Vec<Assemble>, Box<dyn Error>> {
        let db = get_db();
        let mut assemble_map = IndexMap::new();
        let mut book_id_set = IndexSet::new();
        for bid in book_ids {
            book_id_set.insert(*bid);
        }
        loop {
            let mut temp_book_ids = vec![];
            for _i in 0..4 {
                if let Some(bid) = book_id_set.iter().next() {
                    temp_book_ids.push(*bid);
                } else {
                    break;
                }
            }
            if temp_book_ids.is_empty() {
                break;
            }
            for temp_bid in temp_book_ids.iter() {
                book_id_set.shift_remove(temp_bid);
            }
            let temp: Vec<String> = temp_book_ids.iter().map(|b| format!("books contains {}", *b)).collect();
            let sql = format!("SELECT * FROM assemble WHERE {}", temp.join(" OR "));
            let mut resp = db.query(sql).await?;
            let temp_list: Vec<Assemble> = resp.take(0)?;
            for item in temp_list {
                for temp_bid in item.books.iter() {
                    book_id_set.shift_remove(temp_bid);
                }
                assemble_map.insert(item.assemble_id, item);
            }
        }
        let list: Vec<Assemble> = assemble_map.into_iter().map(|(_k, v)| v).collect();
        Ok(list)
    }

    pub(crate) async fn find_by_id(id: u64) -> Result<Option<Assemble>, Box<dyn Error>> {
        let db = get_db();
        let mut resp = db
            .query("SELECT * FROM assemble WHERE assembleId = $id")
            .bind(("id", id))
            .await?;
        let opt_assemble: Option<Assemble> = resp.take(0)?;
        Ok(opt_assemble)
    }

    pub(crate) async fn find_all_by_type(r#type: String) -> Result<Vec<Assemble>, Box<dyn Error>> {
        let db = get_db();
        let mut resp = db
            .query("SELECT * FROM assemble WHERE type = $type ORDER BY assembleId ASC")
            .bind(("type", r#type))
            .await?;
        let assembles: Vec<Assemble> = resp.take(0)?;
        Ok(assembles)
    }

    pub(crate) async fn save(assemble: &Assemble) -> Result<(), Box<dyn Error>> {
        let existing_record = Self::find_by_id(assemble.assemble_id).await?;
        let db = get_db();
        if existing_record.is_none() {
            let _: Option<Assemble> = db.create("assemble").content(assemble.clone()).await?;
        } else {
            let id = existing_record.unwrap().id.unwrap();
            let _r: Option<Assemble> = db.update(id).content(assemble.clone()).await?;
        }
        Ok(())
    }

    pub(crate) async fn save_merged_book_ids(assemble: &Assemble) -> Result<(), Box<dyn Error>> {
        let opt_old_entity = Self::find_by_id(assemble.assemble_id).await?;
        if opt_old_entity.is_none() {
            let db = get_db();
            let _: Option<Assemble> = db.create("assemble").content(assemble.clone()).await?;
        } else {
            let mut book_id_set = HashSet::new();
            let old_entity = opt_old_entity.unwrap();
            book_id_set.extend(old_entity.books);
            let mut new_entity = assemble.clone();
            book_id_set.extend(new_entity.books);
            let mut book_ids: Vec<u64> = book_id_set.into_iter().collect();
            book_ids.sort();
            new_entity.books = book_ids;
            Self::save(&new_entity).await?;
        }
        Ok(())
    }

    pub(crate) async fn save_all_merged_book_ids(
        assembles: &Vec<Assemble>
    ) -> Result<(), Box<dyn Error>> {
        for assemble in assembles {
            Self::save_merged_book_ids(assemble).await?;
        }
        Ok(())
    }
}
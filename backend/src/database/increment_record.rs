use std::error::Error;
use crate::database::{create_surreal_db_index, IndexType};
use crate::database::surreal::get_db;
use crate::models::increment_record::IncrementRecord;

pub struct IncrementRecordRepository;

impl IncrementRecordRepository {
    pub(crate) async fn create_indexes() -> Result<(), Box<dyn Error>> {
        let db= get_db();
        create_surreal_db_index(&db, "IncrementRecord", vec![vec!["rId"]], IndexType::Unique).await?;
        create_surreal_db_index(&db, "IncrementRecord", vec![vec!["status"]], IndexType::Normal).await?;
        Ok(())
    }

    pub(crate) async fn find_by_id(r_id: String) -> Result<Option<IncrementRecord>, Box<dyn Error>> {
        let db= get_db();
        let mut resp = db
            .query("SELECT * FROM IncrementRecord WHERE rId = $rId")
            .bind(("rId", r_id))
            .await?;
        let opt_one: Option<IncrementRecord> = resp.take(0)?;
        Ok(opt_one)
    }

    pub(crate) async fn save(record: &IncrementRecord) -> Result<(), Box<dyn Error>> {
        let existing_record = Self::find_by_id(record.r_id.clone()).await?;
        let db= get_db();
        if existing_record.is_none() {
            let _: Option<IncrementRecord> = db.create("IncrementRecord").content(record.clone()).await?;
        } else {
            let id = existing_record.unwrap().id.unwrap();
            let _r: Option<IncrementRecord> = db.update(id).content(record.clone()).await?;
        }
        Ok(())
    }

    pub(crate) async fn count_by_status_in(statuses: &Vec<i8>) -> Result<usize, Box<dyn Error>> {
        let db= get_db();
        let temp = statuses.iter()
            .map(|s| s.to_string())
            .collect::<Vec<String>>()
            .join(",");
        let sql = format!("COUNT(SELECT * FROM IncrementRecord WHERE status IN [{}])", temp);
        let mut resp = db
            .query(sql)
            .await?;
        let opt_count: Option<usize> = resp.take(0)?;
        Ok(opt_count.unwrap_or(0))
    }

    pub(crate) async fn find_all_by_status_in(statuses: &Vec<i8>) -> Result<Vec<IncrementRecord>, Box<dyn Error>> {
        let db= get_db();
        let temp = statuses.iter()
            .map(|s| s.to_string())
            .collect::<Vec<String>>()
            .join(",");
        let sql = format!("SELECT * FROM IncrementRecord WHERE status IN [{}]", temp);
        let mut resp = db
            .query(sql)
            .await?;
        let records: Vec<IncrementRecord> = resp.take(0)?;
        Ok(records)
    }

    pub(crate) async fn save_all(records: &Vec<IncrementRecord>) -> Result<(), Box<dyn Error>> {
        for r in records {
            Self::save(r).await?;
        }
        Ok(())
    }

    pub async fn find_all_order_by_update_time_desc() -> Result<Vec<IncrementRecord>, Box<dyn Error>> {
        let db = get_db();
        let sql = "SELECT * FROM IncrementRecord ORDER BY updateTime DESC";
        let mut resp = db.query(sql).await?;
        let list: Vec<IncrementRecord> = resp.take(0)?;
        Ok(list)
    }

    pub async fn delete_by_id(r_id: String) -> Result<(), Box<dyn Error>> {
        let db = get_db();
        let sql = "DELETE FROM IncrementRecord WHERE rId = $rId";
        let mut resp = db.query(sql).bind(("rId", r_id)).await?;
        let _opt_one: Option<IncrementRecord> = resp.take(0)?;
        Ok(())
    }
}
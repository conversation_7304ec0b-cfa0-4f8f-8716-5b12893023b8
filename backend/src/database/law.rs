use std::error::Error;
use crate::database::{create_surreal_db_index, IndexType};
use crate::database::surreal::get_db;
use crate::models::law::{GetLaws, GetTotalResultLaw, Law, QueryLaws};

pub async fn get_law(
    params: QueryLaws
) -> Result<GetLaws, Box<dyn Error>> {
    let QueryLaws {
        current_page,
        expiry,  // 施行日期
        office, // 制定机关
        publish, // 发布日期
        type_code, // 分类标签
        status, //时效性
        search_value
    } = params.clone();
    println!("params: {:?}", params);
    //拼接可选参数
    let mut query = String::from("where");
    if expiry.gt != "" {
        query.push_str(&format!
        (" (expiry >= '{} 00:00:00' and expiry <= '{} 00:00:00')",
         expiry.gt,expiry.lt));
    }
    if office != "" {
        if expiry.gt != "" {
            query.push_str(&format!(" and office ?~ '{}'", office));
        } else {
            query.push_str(&format!(" office ?~ '{}'", office));
        }
    }
    if publish.gt != "" {
        if (expiry.gt != "")
            || (office != "")
        {
            query.push_str(&format!
            (" and (publish >= '{} 00:00:00' and publish <= '{} 00:00:00')",
             publish.gt,publish.lt));
        } else {
            query.push_str(&format!
            (" (publish >= '{} 00:00:00' and publish <= '{} 00:00:00')",
             publish.gt,publish.lt));
        }
    }
    if type_code != "all" {
        if (expiry.gt != "")
            || (office != "")
            || (publish.gt != "")
        {
            query.push_str(&format!(" and typeCode == '{}'", type_code));
        } else {
            query.push_str(&format!(" typeCode == '{}'", type_code));
        }
    }
    if status != "" {
        if (expiry.gt != "")
            || (office != "")
            || (publish.gt != "")
            || (type_code != "all")
        {
            query.push_str(&format!(" and status == '{}'", status));
        } else {
            query.push_str(&format!(" status == '{}'", status));
        }
    }
    if search_value != "" {
        if (expiry.gt != "")
            || (office != "")
            || (publish.gt != "")
            || (type_code != "all")
            || (status != "")
        {
            query.push_str(&format!(" and (title ?~ '{}' or content ?~ '{}')",
                search_value,search_value));
        } else {
            query.push_str(&format!(" (title ?~ '{}' or content ?~ '{}')",
                search_value,search_value));
        }
    }
    println!("query: {}", query);
    let db= get_db();
    let base_query = if query.len() == 5 {
        db.query("select * from law order by publish desc limit $limit start $start")
    } else {
        db.query(format!(
            "select * from law {} order by publish desc limit $limit start $start",
            query
        ))
    };
    let total_query = if query.len() == 5 {
        "select count() as title from law group all"
    } else {
        &format!("select count() as title from law {} group all", query)
    };
    println!("current_page:{:?}", current_page);

    let list: Vec<Law> = base_query
        .bind(("limit", 10))
        .bind(("start", (current_page - 1) * 10))
        .await?
        .take(0)?;
    let num: Vec<GetTotalResultLaw> = db.query(total_query).await?.take(0)?;
    let total_:Result<i32, Box<dyn Error>> = match num.get(0) {
        Some(v) => Ok(v.title),
        None => Ok(0),
    };
    let total = total_?;
    println!("totals:{:?}", total);
    let result: GetLaws = GetLaws {
        list,
        total
    };
    Ok(result)
}
pub async fn get_law_total() -> Result<i32, Box<dyn Error>> {
    let db= get_db();
    let total_query = "select count() as title from law group all";
    let num: Vec<GetTotalResultLaw> = db.query(total_query).await?.take(0)?;
    let total_:Result<i32, Box<dyn Error>> = match num.get(0) {
        Some(v) => Ok(v.title),
        None => Ok(0),
    };
    let total = total_?;
    Ok(total)
}
pub struct LawRepository;

impl LawRepository {
    pub(crate) async fn create_indexes() -> Result<(), Box<dyn Error>> {
        let db= get_db();
        create_surreal_db_index(&db, "law", vec![vec!["lawId"]], IndexType::Unique).await?;
        Ok(())
    }

    pub(crate) async fn find_by_id(id: String) -> Result<Option<Law>, Box<dyn Error>> {
        let db= get_db();
        let mut resp = db
            .query("SELECT * FROM law WHERE lawId = $id")
            .bind(("id", id))
            .await?;
        let opt_law: Option<Law> = resp.take(0)?;
        Ok(opt_law)
    }

    pub(crate) async fn save(law: &Law) -> Result<(), Box<dyn Error>> {
        let existing_record = Self::find_by_id(law.law_id.clone()).await?;
        let db= get_db();
        if existing_record.is_none() {
            let _: Option<Law> = db.create("law").content(law.clone()).await?;
        } else {
            let id = existing_record.unwrap().id.unwrap();
            let _r: Option<Law> = db.update(id).content(law.clone()).await?;
        }
        Ok(())
    }

    pub(crate) async fn save_all(law_list: &Vec<Law>) -> Result<(), Box<dyn Error>> {
        for law in law_list {
            Self::save(law).await?;
        }
        Ok(())
    }
}

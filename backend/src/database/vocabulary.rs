use std::error::Error;
use crate::database::{create_surreal_db_index, IndexType};
use crate::database::surreal::get_db;
use crate::models::vocabulary::Vocabulary;

pub(crate) struct VocabularyRepository;


impl VocabularyRepository {
    pub(crate) async fn create_indexes() -> Result<(), Box<dyn Error>> {
        let db= get_db();
        create_surreal_db_index(&db, "vocabulary", vec![vec!["vId"]], IndexType::Unique).await?;
        Ok(())
    }

    pub(crate) async fn find_by_id(id: u64) -> Result<Option<Vocabulary>, Box<dyn Error>> {
        let db= get_db();
        let mut resp = db
            .query("SELECT * FROM vocabulary WHERE vId = $id")
            .bind(("id", id))
            .await?;
        let opt_v: Option<Vocabulary> = resp.take(0)?;
        Ok(opt_v)
    }

    pub(crate) async fn find_all() -> Result<Vec<Vocabulary>, Box<dyn Error>> {
        let db= get_db();
        let mut resp = db
            .query("SELECT * FROM vocabulary")
            .await?;
        let vos: Vec<Vocabulary> = resp.take(0)?;
        Ok(vos)
    }

    pub(crate) async fn save(v: &Vocabulary) -> Result<(), Box<dyn Error>> {
        let existing_record = Self::find_by_id(v.v_id).await?;
        let db= get_db();
        if existing_record.is_none() {
            let _: Option<Vocabulary> = db.create("vocabulary").content(v.clone()).await?;
        } else {
            let id = existing_record.unwrap().id.unwrap();
            let _r: Option<Vocabulary> = db.update(id).content(v.clone()).await?;
        }
        Ok(())
    }

    pub(crate) async fn save_all(vocabularies: &Vec<Vocabulary>) -> Result<(), Box<dyn Error>> {
        for v in vocabularies {
            Self::save(v).await?;
        }
        Ok(())
    }
}
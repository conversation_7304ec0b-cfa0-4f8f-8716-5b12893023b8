use std::error::Error;
use crate::database::{create_surreal_db_index, IndexType};
use crate::database::surreal::get_db;
use crate::models::pinyin::Pinyin;

pub(crate) struct PinyinRepository;


impl PinyinRepository {
    pub(crate) async fn create_indexes() -> Result<(), Box<dyn Error>> {
        let db= get_db();
        create_surreal_db_index(&db, "pinyin", vec![vec!["pinyinId"]], IndexType::Unique).await?;
        Ok(())
    }

    pub(crate) async fn find_by_id(id: u64) -> Result<Option<Pinyin>, Box<dyn Error>> {
        let db= get_db();
        let mut resp = db
            .query("SELECT * FROM pinyin WHERE pinyinId = $id")
            .bind(("id", id))
            .await?;
        let opt_pinyin: Option<Pinyin> = resp.take(0)?;
        Ok(opt_pinyin)
    }

    pub(crate) async fn find_all_by_book_id_and_is_single_kanji(
        book_id: u64, is_single_kanji: bool
    ) -> Result<Vec<Pinyin>, Box<dyn Error>> {
        let db= get_db();
        let mut resp = db
            .query("SELECT * FROM pinyin WHERE bookId = $bookId AND isSingleKanji = $isSingleKanji")
            .bind(("bookId", book_id))
            .bind(("isSingleKanji", is_single_kanji))
            .await?;
        let pinyin_list: Vec<Pinyin> = resp.take(0)?;
        Ok(pinyin_list)
    }

    pub(crate) async fn save(pinyin: &Pinyin) -> Result<(), Box<dyn Error>> {
        let existing_record = Self::find_by_id(pinyin.pinyin_id).await?;
        let db= get_db();
        if existing_record.is_none() {
            let _: Option<Pinyin> = db.create("pinyin").content(pinyin.clone()).await?;
        } else {
            let id = existing_record.unwrap().id.unwrap();
            let _r: Option<Pinyin> = db.update(id).content(pinyin.clone()).await?;
        }
        Ok(())
    }

    pub(crate) async fn save_all(pinyin_list: &Vec<Pinyin>) -> Result<(), Box<dyn Error>> {
        for pinyin in pinyin_list {
            Self::save(pinyin).await?;
        }
        Ok(())
    }

    pub(crate) async fn find_all_by_book_id_and_is_single_kanji_and_without_tone(
        book_id: u64, is_single_kanji: bool, without_tone: String
    ) -> Result<Vec<Pinyin>, Box<dyn Error>> {
        let db= get_db();
        let sql = "SELECT * FROM pinyin WHERE bookId = $bookId AND isSingleKanji = $isSingleKanji AND withoutTone = $withoutTone";
        let mut resp = db.query(sql)
            .bind(("bookId", book_id))
            .bind(("isSingleKanji", is_single_kanji))
            .bind(("withoutTone", without_tone))
            .await?;
        let results: Vec<Pinyin> = resp.take(0)?;
        Ok(results)
    }
}
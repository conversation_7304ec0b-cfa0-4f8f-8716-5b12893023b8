use std::error::Error;
use crate::database::{create_surreal_db_index, IndexType};
use crate::database::surreal::get_db;
use crate::models::card::{ Card, Context, QueryNewspaperContext};

pub(crate) struct CardRepository;
pub async fn get_card_content_by_book_id_card_id(
    params: QueryNewspaperContext
) -> Result<Vec<Context>, surrealdb::Error>{
    let db = get_db();
    let mut context = db.query(
        format!(
            "SELECT content FROM card WHERE bookId = {} and cardId = {:?}",
            params.book_id, params.card_id,
        )
    )
        .await?;
    let result: Vec<Context> = context
        .take(0)?;
    Ok(result)
}
impl CardRepository {
    pub(crate) async fn create_indexes() -> Result<(), Box<dyn Error>> {
        let db = get_db();
        create_surreal_db_index(&db, "card", vec![vec!["cardId"]], IndexType::Unique).await?;
        create_surreal_db_index(&db, "card", vec![vec!["bookId"], vec!["catalogueId"]], IndexType::Normal).await?;
        Ok(())
    }

    pub(crate) async fn find_by_id(id: u64) -> Result<Option<Card>, Box<dyn Error>> {
        let db = get_db();
        let mut resp = db
            .query("SELECT * FROM card WHERE cardId = $id")
            .bind(("id", id))
            .await?;
        let opt_book: Option<Card> = resp.take(0)?;
        Ok(opt_book)
    }

    pub(crate) async fn find_all_by_ids(ids: &Vec<u64>) -> Result<Vec<Card>, Box<dyn Error>> {
        let db = get_db();
        let temp: Vec<String> = ids.iter().map(|id| id.to_string()).collect();
        let sql = format!("SELECT * FROM card WHERE cardId IN [{}]", temp.join(","));
        let mut resp = db.query(sql).await?;
        let list: Vec<Card> = resp.take(0)?;
        Ok(list)
    }

    pub(crate) async fn find_all_by_book_id_order_by_page_serial(
        book_id: u64
    ) -> Result<Vec<Card>, Box<dyn Error>> {
        let db = get_db();
        let sql = "SELECT * FROM card WHERE bookId = $bookId ORDER BY page, serial";
        let mut resp = db.query(sql)
            .bind(("bookId", book_id))
            .await?;
        let list: Vec<Card> = resp.take(0)?;
        Ok(list)
    }

    pub(crate) async fn save(card: &Card) -> Result<(), Box<dyn Error>> {
        let existing_record = Self::find_by_id(card.card_id).await?;
        let db = get_db();
        if existing_record.is_none() {
            let _: Option<Card> = db.create("card").content(card.clone()).await?;
        } else {
            let id = existing_record.unwrap().id.unwrap();
            let _r: Option<Card> = db.update(id).content(card.clone()).await?;
        }
        Ok(())
    }

    pub(crate) async fn save_all(cards: &Vec<Card>) -> Result<(), Box<dyn Error>> {
        for card in cards {
            Self::save(card).await?;
        }
        Ok(())
    }
}
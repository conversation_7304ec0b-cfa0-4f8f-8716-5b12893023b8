[package]
name = "book-guard-backend"
version = "1.0.0"
description = "Book Guard Backend API Server"
authors = ["you"]
license = ""
repository = ""
edition = "2021"

[dependencies]
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
walkdir = "2.5"
zip = "4"
once_cell = "1.21"
chrono = { version = "0.4", features = ["serde"] }
regex = "1.11"
anyhow = "1.0"
serde_derive = "1.0"
lazy_static = { version = "1.5", features = [] }
tokio = { version = "1.45", features = ["full"] }
tokio-tungstenite = "0.27"
log = "0.4"
bincode = {version="2",features = ["serde"] }
surrealdb = { version = "2.3", features = ["kv-rocksdb"] }
tantivy = "0.24"
tantivy-jieba = "0.14"
rand = "0.9"
reqwest = { version = "0.12", features = ["json", "multipart"] }
machine-uid = "0.5"
indexmap = "2.9"
actix-http = "3.11"
actix-web = "4"
actix-files = "0.6"
actix-ws = "0.3"
actix-cors = "0.7"
serde_urlencoded = "0.7"
uuid = { version = "1", features = ["v4"] }
futures-util = "0.3"
human-panic = "2.0"
fuzzy-matcher = "0.3"
similar = "2.7"
strsim = "0.11"
snowflaker = "0.3.6"
async-stream = "0.3"
env_logger = "0.11"
dotenv = "0.15"
clap = { version = "4.0", features = ["derive"] }

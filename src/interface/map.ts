export type Category = {
    name: string
    parent?: string
    children?: Category[]


export type MapObjModel = {
    id: any;
    mapId: string
    name: string
    content?: string | null
    scale?: string | null
    size: string
    superclass?: string | null
    subclass?: string | null
    smallclass?: string | null
    border?: string | null
    use?: string | null
    jpgPath?: string | null
    browseNum?: bigint | number | null
    downloadNum?: bigint | number | null
    telBrowseNum?: bigint | number | null
    telDownloadNum: bigint | number
    catelogs?: string | null
    largeclass?: string | null
    sequenceNum?: string | null
    midclass?: string | null
    isImg?: string | null
    mapNumber?: string | null
    mapYear?: string | null
    orderId?: string | null
    countyOder?: string | null
    neighbouringCountry?: number | null
    provinceColor?: number | null
    illustration?: number | null
    path?: string | null

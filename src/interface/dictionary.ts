


interface Letter {
    letterId: number;
    name: string;
    nameOutBase: string;
    bookId: number;
    page: number | null;
    strokeCount: number | null;
    level: number | null;
    structure: string | null;
    startStroke: string | null;
    radicalId: number | null;
    radical: string | null;
    habitualRadicalIds: number[];
    habitualRadicals: string[];
    pinyinIds: number[] | null;
    pinyinWithTones: string[];
    pinyinWithoutTones: string[] | null;
    illustration: string;


interface Vocabulary {
    vId: number;
    name: string;
    nameOutBase: string;
    bookId: number;
    page: number | null;
    pinyinId: number | null;
    pinyinList: string[];
    pinyinWithTone: string;
    pinyinWithoutTone: string | null;
    letterIds: number[];
    letters: string[];
    illustration: string;


interface Radical {
    radicalId: number;
    bookId: number;
    name: string;
    strokeCount: number;


interface PinyinWithoutToneVo {
    firstChar: string;
    withoutTones: string[];


interface LetterInfoVo {
    letterId: number;
    name: string;
    nameOutBase: string;
    strokeCount: number | null;


interface PinyinSectionVo {
    pinyinId: number;
    pyWithTone: string;
    letters: LetterInfoVo[];


interface StrokeRadicalsVo {
    stroke: number; // u16 在 TypeScript 中可以对应为 number
    list: Radical[];


interface LetterPinyinInfoVo {
    letterId: number; // u64 在 TypeScript 中可以对应为 number
    name: string;
    nameOutBase: string;
    pinyinList: string[];


interface LetterRadicalIdSectionVo {
    radicalId: number; // u64 在 TypeScript 中可以对应为 number
    remainingStrokeCount: number; // u16 在 TypeScript 中可以对应为 number
    letters: LetterPinyinInfoVo[];


interface SRRListItemVo {
    type: string; // 'type' 是保留字，如果遇到问题可以使用其他名称或用引号包裹
    id: number; // u64 在 TypeScript 中可以对应为 number
    name: string;
    nameOutBase: string;
    pyWithTone: string[];
    illustration: string;


interface SRRListVo {
    tip: string;
    items: SRRListItemVo[];


interface SearchResourceResultVo {
    showRecommend: boolean;
    showSamePinyin: boolean;
    showSameRadical: boolean;
    list: SRRListVo[];


// 定义搜索类型的枚举值
type SearchTypeEnum = 1 | 2 | 3;

interface SearchResourcesParams {
    searchText: string;
    searchType: SearchTypeEnum | null;
    bookId: string;


interface Snapshot {
    page: number | null;
    path: string;


export type {
    PinyinWithoutToneVo, PinyinSectionVo, Letter, StrokeRadicalsVo,
    LetterRadicalIdSectionVo, SearchResourceResultVo, SearchTypeEnum,
    SearchResourcesParams, SRRListVo, Snapshot, Vocabulary, SRRListItemVo
};
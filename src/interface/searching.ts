

interface PredictCardOrBookParams {
    searchText: string;
    pageNo: number;
    pageSize: number | null;
    subjectCode: string | null;
    type: string | null;
    bookId: number | null;
    author: string | null;
    series: string | null;
    version: string | null;
    edition: string | null;
    brand: string | null;
    period: number | null;
    grade: string | null;
    /**
     * 出版年份
     * 如果此参数存在，则earliestPublishDate和recentPublishDate不再起作用
     */
    publishYear: number | null;
    /**
     * 格式为: "2025-01-14"
     */
    earliestPublishDate: string | null;
    /**
     * 格式为: "2025-01-14"
     */
    recentPublishDate: string | null;
    assembleId: string | null;


interface PredictResult<T> {
    totals: number;
    pageNo: number;
    pageCount: number;
    pageSize: number;
    list: T[];
    highlights: string[];
    retrieval_duration: number;
    total_duration: number;


interface CardSearchResult {
    id: string;
    score: number;
    page: number;
    type: string;
    title: string | null;
    content: string;
    subjectCode: string;
    subjectName: string;
    bookId: string;
    bookName: string;
    series: string | null;
    catalogueId: string;
    catalogueCode: string;
    catalogueSerial: number;
    catalogueParentId: string | null;
    cataloguePathName: string;
    catalogueName: string;


interface BookSearchResult {
    id: string;
    score: number;
    batch: number | null;
    author: string | null;
    bookName: string;
    isbn: string;
    publisher: string | null;
    series: string | null;
    coverImageUrl: string | null;
    subjectCode: string;
    subjectName: string;
    type: string;
    updateTime: string | null;
    version: string | null;
    edition: string | null;
    publishDate: string | null;
    brand: string | null;
    period: number | null;
    grade: string | null;


export type {PredictCardOrBookParams, PredictResult, CardSearchResult, BookSearchResult};

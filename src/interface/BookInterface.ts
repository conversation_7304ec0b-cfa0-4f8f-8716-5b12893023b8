interface Snapshot {
    page: number;
    path: string;
    url: string;


interface Content {
    page: number;
    text: string;
    loadStatus: boolean;


interface Textbook {
    bookId: number;
    batch: number;
    bookName: string;
    publisher: string;
    author: string;
    isbn: string;
    subjectName: string;
    subjectCode: string;
    type: string;
    version?: string;
    edition?: string;
    series: string;
    updateTime: string;
    institutionCode: string;
    snapshots: Snapshot[];
    contents: Content[];


interface Catalogue {
    bookId: number;
    cardSize: number;
    catalogueId: number;
    code: string;
    id: string;
    institutionCode: string;
    page: number;
    endPage: number;
    parentId: string | null;
    pathName: string;
    serial: number;
    subjectCode: string;
    title: string;


interface PredictCardParams {
    pageNo?: number;
    pageSize?: number;
    subjectCode: string;
    textbookId?: number;
    searchText: string;


interface PredictResult<T> {
    totals: number;
    pageNo: number;
    pageCount: number;
    pageSize: number;
    list: T[];
    highlights: string[];
    duration: number;



interface CardSearchResult {
    id: string;
    score: number;
    page: number;
    content: string;
    subjectCode: string;
    subjectName: string;
    textbookId: number;
    bookName: string;
    series?: string | null;
    catalogueId: string;
    catalogueCode: string;
    catalogueSerial: number;
    catalogueParentId?: string | null;
    cataloguePathName: string;
    catalogueName: string;


interface BookVo {
    id: string;
    batch: number | null; // Option<i32> 转换为 number | null
    author: string | null; // Option<String> 转换为 string | null
    bookName: string; // 使用 serde 中指定的名称
    isbn: string;
    publisher: string | null; // Option<String> 转换为 string | null
    series: string | null; // Option<String> 转换为 string | null
    coverImageUrl: string | null; // Option<String> 转换为 string | null，并使用 serde 中指定的名称
    subjectCode: string; // 使用 serde 中指定的名称
    subjectName: string; // 使用 serde 中指定的名称
    type: string; // 'type' 是保留字，这里直接使用，如果遇到问题可以考虑其他命名方式如 'bookType'
    updateTime: string | null; // Option<String> 转换为 string | null，并使用 serde 中指定的名称
    version: string | null; // Option<String> 转换为 string | null
    edition: string | null; // Option<String> 转换为 string | null
    publishDate: string | null; // Option<String> 转换为 string | null，并使用 serde 中指定的名称
    brand: string | null; // Option<String> 转换为 string | null
    period: number | null; // Option<i32> 转换为 number | null
    grade: string | null; // Option<String> 转换为 string | null


interface BookPageByTypesParams {
    searchText: string;
    bookTypes: string[];
    pageNo: number | null;
    pageSize: number | null;



export type {Catalogue, Textbook, Snapshot, Content, PredictCardParams, PredictResult, CardSearchResult, BookVo, BookPageByTypesParams
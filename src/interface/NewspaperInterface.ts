interface Snapshot {
    page: number;
    path: string;
    url: string;


interface Content {
    page: number;
    text: string;
    cardId: number;

export interface DataNode {
    title: string;
    key: string;
    isLeaf?: boolean;
    children?: DataNode[];

interface Newspaper {
    bookId: number;
    batch: number;
    bookName: string;
    publisher: string;
    author: string;
    isbn: string;
    subjectName: string;
    subjectCode: string;
    type: string;
    version?: string;
    edition?: string;
    series: string;
    updateTime: string;
    institutionCode: string;
    snapshots: Snapshot[];
    contents: Content[];

interface CatalogueChildren {
    cardId: number;
    key: number;
    page: number;
    title: string;

interface Catalogue {
    catalogueId: number;
    children: CatalogueChildren[];
    title: string;


interface PredictCardParams {
    pageNo?: number;
    pageSize?: number;
    subjectCode: string;
    textbookId?: number;
    searchText: string;


interface PredictResult<T> {
    totals: number;
    pageNo: number;
    pageCount: number;
    pageSize: number;
    list: T[];
    highlights: string[];
    duration: number;



interface CardSearchResult {
    id: string;
    score: number;
    page: number;
    content: string;
    subjectCode: string;
    subjectName: string;
    textbookId: number;
    bookName: string;
    series?: string | null;
    catalogueId: string;
    catalogueCode: string;
    catalogueSerial: number;
    catalogueParentId?: string | null;
    cataloguePathName: string;
    catalogueName: string;



export type {Catalogue, Newspaper, Snapshot, Content, PredictCardParams, PredictResult, CardSearchResult
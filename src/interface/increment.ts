

// 状态枚举定义
type IncStatusEnum = 0 | 1 | 2 | 3;

interface IncrementRecord {
    rId: string;
    filePath: string;
    fileName: string;
    createTime: string;
    updateTime: string;
    handleTime: string | null;
    status: IncStatusEnum;
    msg: string | null;


interface IncrementResultVo {
    total: number;
    handled: number;
    list: IncrementRecord[];


export type { IncStatusEnum, IncrementRecord, IncrementResultVo };
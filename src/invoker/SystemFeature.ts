
import {
    UserDataDirCustomConfigVo,
    SystemFeatureDetails, UsageModeVo
} from "../interface/SystemFeatureInterface.ts";
import {ResponseVO} from "../interface";

import axios from "axios";

export const getSystemFeatDetails = async () => {
    let resp: ResponseVO<SystemFeatureDetails>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<SystemFeatureDetails>>("get_system_feat_details", {});
    } else {
        resp = (await axios.get<ResponseVO<SystemFeatureDetails>>('/api/system-feature/details')).data;
    
    if (resp.data !== null) { return resp.data; 
    throw Error('Error while get_system_feat_details');


export const setUserDataDirCustomConfig = async (config: UserDataDirCustomConfigVo) => {
    let resp: ResponseVO<void>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<void>>("set_user_data_dir_custom_config", { config });
    } else {
        resp = (await axios.post<ResponseVO<void>>('/api/system-feature/dir-custom-config', { ...config })).data;
    
    return resp;


export const invokeGetUsageMode = async () => {
    const resp = await invoke<ResponseVO<UsageModeVo>>("get_usage_mode", {});
    if (resp.data !== null) { return resp.data; 
    throw Error('Error while get_usage_mode');


export const invokeSetUsageMode = async (params: UsageModeVo) => {
    return await invoke<ResponseVO<void>>("set_usage_mode", { params });

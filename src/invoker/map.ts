
import {ResponseVO} from "../interface";

import axios from "axios";

export const invokeGetMaps = async (params: any) => {
    let resp: ResponseVO<any>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<any>>("get_maps", { params });
    } else {
        resp = (await axios.post<ResponseVO<any>>('/api/maps', { ...params })).data;
    
    return resp;


export const invokeGetMapsBreadCrumb = async () => {
    let resp: ResponseVO<any>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<any>>("get_maps_bread_crumb");
    } else {
        resp = (await axios.get<ResponseVO<any>>('/api/maps/getMapsBreadCrumb')).data;
    
    return resp;


export const invokeGetMapsFilters = async (query: {name: string, index: number}) => {
    let resp: ResponseVO<any>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<any>>("get_maps_filters", { query });
    } else {
        resp = (await axios.post<ResponseVO<any>>('/api/maps/getMapsFilters', { ...query })).data;
    
    return resp;


export const invokeGetMapsTotal = async () => {
    let resp: ResponseVO<any>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<any>>("get_maps_total");
    } else {
        resp = (await axios.get<ResponseVO<any>>('/api/maps/getMapsTotal')).data;
    
    return resp;


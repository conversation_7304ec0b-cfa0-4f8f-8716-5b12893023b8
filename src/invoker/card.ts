import {ResponseVO} from "../interface";
import axios from "axios";

export const invokeGetCardInfosByBookId = async (bookId: number) => {
    const resp = (await axios.get<ResponseVO<any[]>>('/api/card/infos/by-book-id', { params: {bookId} })).data;
    if (resp.data !== null) { return resp.data; 
    throw new Error("Error while invoke get_card_infos_by_book_id")


export const invokeGetCardContentByBookIdCardId = async (
    params: {bookId: number, cardId: number
) => {
    const resp = (await axios.post<ResponseVO<any>>('/api/card/content/by-book-id-card-id', { ...params })).data;
    return resp;

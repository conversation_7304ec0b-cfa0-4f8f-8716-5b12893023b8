import {ResponseVO} from "../interface";
import axios from "axios";

export const invokeGetCataloguesByBookId = async (bookId: string) => {
    const resp = (await axios.get<ResponseVO<any>>('/api/catalogue/by-book-id', {params: {bookId}})).data;
    return resp;


export const invokeGetNewspaperCatalogueByBookId = async (bookId: string) => {
    const resp = (await axios.get<ResponseVO<any>>('/api/catalogue/newspaper/by-book-id', {params: {bookId}})).data;
    return resp;


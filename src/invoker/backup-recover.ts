import {BackupRecoverStatusInfo} from "../interface/backup-recover.ts";

// Backup/Recover functionality is not available in web version
export const invokeGetBakRcvStatusInfo = async (): Promise<BackupRecoverStatusInfo> => {
    throw new Error("Backup/Recover functionality is not available in web version");


export const invokeBackupUserData = async (backupFilePath: string) => {
    throw new Error("Backup functionality is not available in web version");


export const invokeRecoverUserData = async (backupFilePath: string, optUserDataCustomDir?: string) => {
    throw new Error("Recover functionality is not available in web version");


export const invokeCancelBackupUserData = async () => {
    throw new Error("Backup functionality is not available in web version");


export const invokeCancelRecoverUserData = async () => {
    throw new Error("Recover functionality is not available in web version");

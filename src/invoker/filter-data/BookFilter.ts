import {BookFilterParams, BookInfo, Subject} from "../../interface/filter-data/BookFilter.ts";
import {ResponseVO} from "../../interface";
import axios from "axios";


export const invokeFilterBookTypes = async (params: BookFilterParams) => {
    let resp: ResponseVO<string[]>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<string[]>>("filter_book_types", { params });
    } else {
        resp = (await axios.post<ResponseVO<string[]>>('/api/book-filter/book-types', { ...params })).data;
    
    if (resp.data !== null) { return resp.data; 
    throw new Error("Error while filter_book_types");


export const invokeFilterSubjects = async (params: BookFilterParams) => {
    let resp: ResponseVO<Subject[]>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<Subject[]>>("filter_subjects", { params });
    } else {
        resp = (await axios.post<ResponseVO<Subject[]>>('/api/book-filter/subjects', { ...params })).data;
    
    if (resp.data !== null) { return resp.data; 
    throw new Error("Error while filter_subjects");


export const invokeFilterVersions = async (params: BookFilterParams) => {
    let resp: ResponseVO<string[]>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<string[]>>("filter_versions", { params });
    } else {
        resp = (await axios.post<ResponseVO<string[]>>('/api/book-filter/versions', { ...params })).data;
    
    if (resp.data !== null) { return resp.data; 
    throw new Error("Error while filter_versions");


export const invokeFilterGrades = async (params: BookFilterParams) => {
    let resp: ResponseVO<string[]>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<string[]>>("filter_grades", { params });
    } else {
        resp = (await axios.post<ResponseVO<string[]>>('/api/book-filter/grades', { ...params })).data;
    
    if (resp.data !== null) { return resp.data; 
    throw new Error("Error while filter_grades");


export const invokeFilterSeries = async (params: BookFilterParams) => {
    let resp: ResponseVO<string[]>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<string[]>>("filter_series", { params });
    } else {
        resp = (await axios.post<ResponseVO<string[]>>('/api/book-filter/series', { ...params })).data;
    
    if (resp.data !== null) { return resp.data; 
    throw new Error("Error while filter_series");


export const invokeFilterAuthors = async (params: BookFilterParams) => {
    let resp: ResponseVO<string[]>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<string[]>>("filter_authors", { params });
    } else {
        resp = (await axios.post<ResponseVO<string[]>>('/api/book-filter/authors', { ...params })).data;
    
    if (resp.data !== null) { return resp.data; 
    throw new Error("Error while filter_authors");


export const invokeFilterBooks = async (params: BookFilterParams) => {
    let resp: ResponseVO<BookInfo[]>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<BookInfo[]>>("filter_books", { params });
    } else {
        resp = (await axios.post<ResponseVO<BookInfo[]>>('/api/book-filter/books', { ...params })).data;
    
    if (resp.data !== null) { return resp.data; 
    throw new Error("Error while filter_books");

import {PageResult, ResponseVO} from "../interface";
import {BookPageByTypesParams, BookVo} from "../interface/BookInterface.ts";
import axios from "axios";


export const invokeGetBookPageByTypes = async (params: BookPageByTypesParams) => {
    const resp = (await axios.post<ResponseVO<PageResult<BookVo>>>('/api/book/page/by-types', { ...params })).data;
    if (resp.data !== null) { return resp.data; 
    throw Error('Error while searching books');


export const invokeGetBookByBookId = async (bookId: string) => {
    const resp = (await axios.get<ResponseVO<any>>('/api/book/by-id', {params: { bookId }})).data;
    return resp;


export const invokeGetBookNewsInfoByBrandPubDate = async (
    params: {brand: string, publishDate: string, types: number
) => {
    const resp = (await axios.post<ResponseVO<any>>('/api/book/news-info/by-brand-pub-date', { ...params })).data;
    return resp;

import {ResponseVO} from "../interface";
import {
    Letter,
    LetterRadicalIdSectionVo,
    PinyinSectionVo,
    PinyinWithoutToneVo, SearchResourceResultVo,
    SearchResourcesParams, Snapshot,
    StrokeRadicalsVo, Vocabulary
} from "../interface/dictionary.ts";
import {BookVo} from "../interface/BookInterface.ts";
import axios from "axios";


export const invokeGetDictionary = async (bookId: string) => {
    const resp = (await axios.get<ResponseVO<BookVo>>('/api/dictionary', {params: {bookId}})).data;
    if (resp.data !== null) { return resp.data; 
    throw Error('Error while searching books');


export const invokeGetPinyinWithoutTones = async (bookId: string) => {
    const resp = (await axios.get<ResponseVO<PinyinWithoutToneVo[]>>('/api/dictionary/pinyin/without-tones', {params: {bookId}})).data;
    if (resp.data !== null) { return resp.data; 
    throw Error('Error while searching books');


export const invokeGetAllRadicals = async (bookId: string) => {
    const resp = (await axios.get<ResponseVO<StrokeRadicalsVo[]>>('/api/dictionary/radicals/all', {params: {bookId}})).data;
    if (resp.data !== null) { return resp.data; 
    throw Error('Error while get_all_radicals');



export const getLettersWithPinyinByWithoutTone = async (bookId: string, pyWithoutTone: string) => {
    const resp = (await axios.get<ResponseVO<PinyinSectionVo[]>>('/api/dictionary/letters/with-pinyin/by-without-tone', {params: {bookId, pyWithoutTone}})).data;
    if (resp.data !== null) { return resp.data; 
    throw Error('Error while searching books');


export const getLetterSectionByRadicalId = async (bookId: string, radicalId: number) => {
    const resp = (await axios.get<ResponseVO<LetterRadicalIdSectionVo[]>>('/api/dictionary/letter-sections/by-radical-id', {params: {bookId, radicalId}})).data;
    if (resp.data !== null) { return resp.data; 
    throw Error('Error while searching letters');


export const getLettersByStrokeCount = async (bookId: string, strokeCount: number) => {
    const resp = (await axios.get<ResponseVO<Letter[]>>('/api/dictionary/letters/by-stroke-count', {params: {bookId, strokeCount}})).data;
    if (resp.data !== null) { return resp.data; 
    throw Error('Error while get_letters_by_stroke_count');


export const invokeGetLetterById = async (letterId: number) => {
    const resp = (await axios.get<ResponseVO<Letter>>('/api/dictionary/letter/by-id', {params: {letterId}})).data;
    if (resp.data !== null) { return resp.data; 
    throw Error('Error while searching letter');


export const invokeGetVocabularyById = async (vocabularyId: number) => {
    const resp = (await axios.get<ResponseVO<Vocabulary>>('/api/dictionary/vocabulary/by-id', {params: {vocabularyId}})).data;
    if (resp.data !== null) { return resp.data; 
    throw Error('Error while get_vocabulary_by_id');


export const invokeSearchResources = async (params: SearchResourcesParams) => {
    const resp = (await axios.post<ResponseVO<SearchResourceResultVo>>('/api/file-upload/search-resources', {...params})).data;
    if (resp.data !== null) { return resp.data; 
    throw Error('Error while invokeSearchResources');


export const invokeGetSnapshots = async (bookId: string) => {
    const resp = (await axios.get<ResponseVO<Snapshot[]>>('/api/file-upload/snapshots', {params: {bookId}})).data;
    if (resp.data !== null) { return resp.data; 
    throw Error('Error while get_snapshots');


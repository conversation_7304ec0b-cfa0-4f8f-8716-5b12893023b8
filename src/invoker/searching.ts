import {BookSearchResult, CardSearchResult, PredictCardOrBookParams, PredictResult} from "../interface/searching.ts";
import {ResponseVO} from "../interface";
import axios from "axios";

export const invokeGlobalSearchCards = async (params: PredictCardOrBookParams) => {
    const resp = (await axios.post<ResponseVO<PredictResult<CardSearchResult>>>('/api/global-search/cards', { ...params })).data;
    if (resp.data !== null) { return resp.data; 
    throw Error('Error while searching cards');


export const invokeGlobalSearchBooks = async (params: PredictCardOrBookParams) => {
    const resp = (await axios.post<ResponseVO<PredictResult<BookSearchResult>>>('/api/global-search/books', { ...params })).data;
    if (resp.data !== null) { return resp.data; 
    throw Error('Error while searching books');


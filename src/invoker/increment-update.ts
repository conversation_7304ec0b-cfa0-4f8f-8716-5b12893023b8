import {ResponseVO} from "../interface";
import {IncrementResultVo} from "../interface/increment.ts";
import axios from "axios";

export const importIncrementsQueue = async (filePaths: string[]) => {
    return (await axios.post<ResponseVO<null>>('/api/increment/queue', { filePaths })).data;


export const invokeGetIncrementResult = async () => {
    const resp = (await axios.get<ResponseVO<IncrementResultVo>>('/api/increment/result')).data;
    if (resp.data !== null) {
        return resp.data;
    
    throw new Error("Error while invoking get_increment_result");


export const invokeDeleteIncrementRecordById = async (rId: string) => {
    const resp = (await axios.delete<ResponseVO<void>>(`/api/increment/record/${rId}`)).data;
    if (resp.code === 200) {
        return;
    
    throw new Error(resp.msg);

import {ResponseVO} from "../interface";
import axios from "axios";

export async function invokeActivateStatus() {
    const resp = (await axios.get<ResponseVO<boolean>>("/api/activate/status")).data;
    return resp.data as boolean;


export async function invokeGetUnActivateStatus() {
    const resp = (await axios.get<ResponseVO<string>>("/api/activate/info")).data;
    return resp.data as string;


export async function invokeGetActivateKey() {
    const resp = (await axios.get<ResponseVO<string>>("/api/activate/key")).data;
    return resp.data as string;


export async function invokeActivateParseCode(code: string) {
    const resp = (await axios.get<ResponseVO<boolean>>("/api/activate/parseCode", {params: {code}})).data;
    return resp.data as boolean;

import { ResponseVO } from "../interface";
import axios from "axios";
import {
    PlagiarismCompareParams,
    PlagiarismResultParams,
    PlagiarismResult,
    BatchListParams,
    BatchListResult,
    BatchStatistics,
    PlagiarismBatch
} from "../interface/plagiarism.ts";

// 开始查重对比
export const invokePlagiarismCompare = async (params: PlagiarismCompareParams): Promise<PlagiarismBatch> => {
    const resp = (await axios.post<ResponseVO<PlagiarismBatch>>('/api/plagiarism/compare', { ...params })).data;
    if (resp.data !== null) {
        return resp.data;
    
    throw Error('Error while starting plagiarism comparison');
};

// 获取查重结果
export const invokePlagiarismResults = async (params: PlagiarismResultParams): Promise<PlagiarismResult> => {
    const resp = (await axios.get<ResponseVO<PlagiarismResult>>('/api/plagiarism/results', { params })).data;
    if (resp.data !== null) {
        return resp.data;
    
    throw Error('Error while fetching plagiarism results');
};

// 获取批次列表
export const invokeBatchList = async (params: BatchListParams): Promise<BatchListResult> => {
    const resp = (await axios.get<ResponseVO<BatchListResult>>('/api/plagiarism/batches', { params })).data;
    if (resp.data !== null) {
        return resp.data;
    
    throw Error('Error while fetching batch list');
};

// 获取批次详情
export const invokeBatchDetail = async (batchId: string): Promise<PlagiarismBatch> => {
    const resp = (await axios.get<ResponseVO<PlagiarismBatch>>(`/api/plagiarism/batch/${batchId}/detail`)).data;
    if (resp.data !== null) {
        return resp.data;
    
    throw Error('Error while fetching batch detail');
};

// 获取批次统计信息
export const invokeBatchStatistics = async (batchId: string): Promise<BatchStatistics> => {
    const resp = (await axios.get<ResponseVO<BatchStatistics>>(`/api/plagiarism/batch/${batchId}/statistics`)).data;
    if (resp.data !== null) {
        return resp.data;
    
    throw Error('Error while fetching batch statistics');
};

// 删除批次
export const invokeDeleteBatch = async (batchId: string): Promise<boolean> => {
    const resp = (await axios.delete<ResponseVO<boolean>>(`/api/plagiarism/batch/${batchId}`)).data;
    if (resp.data !== null) {
        return resp.data;
    
    throw Error('Error while deleting batch');
};

// 取消正在进行的查重任务
export const invokeCancelComparison = async (batchId: string): Promise<boolean> => {
    const resp = (await axios.post<ResponseVO<boolean>>(`/api/plagiarism/batch/${batchId}/cancel`)).data;
    if (resp.data !== null) {
        return resp.data;
    
    throw Error('Error while canceling comparison');
};

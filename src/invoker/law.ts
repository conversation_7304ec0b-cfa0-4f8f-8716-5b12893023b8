
import {ResponseVO} from "../interface";

import axios from "axios";

export const invokeGetLawTotal = async () => {
    let res: ResponseVO<number>;
    if (isTauriEnv) {
        res = await invoke<ResponseVO<number>>("get_law_total");
    } else {
        res = (await axios.get<ResponseVO<number>>('/api/laws/getLawsTotal')).data;
    
    if (res.data !== null) {
        return res.data;
    
    throw new Error("Failed to invoke get_law_total");


export const invokeGetLaw = async (params: any) => {
    let resp: ResponseVO<any>;
    if (isTauriEnv) {
        resp = await invoke<ResponseVO<any>>("get_law", { params });
    } else {
        resp = (await axios.post<ResponseVO<any>>('/api/laws', { ...params })).data;
    
    return resp;

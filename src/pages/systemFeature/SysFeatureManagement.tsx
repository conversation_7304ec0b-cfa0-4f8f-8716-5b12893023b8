import {open, save} from "@tauri-apps/plugin-dialog";
import {<PERSON><PERSON>, Col, Divider, Input, InputNumber, message, Modal, Progress, Radio, Row, Space, Switch, Tabs, TabsProps, Tag} from "antd";
import {useEffect, useState} from "react";
import {SystemFeatureDetails, UsageModeVo} from "../../interface/SystemFeatureInterface.ts";
import {
    getSystemFeatDetails, invokeGetUsageMode, invokeSetUsageMode,
    setUserDataDirCustomConfig,
} from "../../invoker/SystemFeature.ts";
import {LoadingOutlined, WarningOutlined, ExceptionOutlined} from "@ant-design/icons";
import {BackupRecoverStatusInfo} from "../../interface/backup-recover.ts";
import {
    invokeBackupUserData,
    invokeCancelBackupUserData,
    invokeGetBakRcvStatusInfo,
    invokeRecoverUserData
} from "../../invoker/backup-recover.ts";
import {listen} from "@tauri-apps/api/event";
import {isTauriEnv} from "../../utils/system.ts";


function TimeSpendView(props: {timeSpendInSec: number}) {
    const hours = Math.floor((props.timeSpendInSec / 3600));
    const minutes = Math.floor((props.timeSpendInSec / 60)) % 60;
    const seconds = props.timeSpendInSec % 60;
    return <>
        {hours < 10 ? `0${hours}` : hours
        :{minutes < 10 ? `0${minutes}` : minutes
        :{seconds < 10 ? `0${seconds}` : seconds
    </>



function SysFeatureManagement() {
    const [
        featDetails,
        setFeatDetails
    ] = useState<SystemFeatureDetails>({
        userDataDirCustomConfig: {
            enable: false,
            path: "",
            warning: null
        },
    });
    const [
        featDetailsEdit,
        setFeatDetailsEdit
    ] = useState<SystemFeatureDetails>({
        userDataDirCustomConfig: {
            enable: false,
            path: "",
            warning: null
        },
    });
    const [usageMode, setUsageMode] = useState<UsageModeVo>({
        mode: 0, port: 0, url: "", warning: null
    });
    const [usageModeEdit, setUsageModeEdit] = useState<UsageModeVo>({
        mode: 0, port: 0, url: "", warning: null
    });
    const [bakRcvStatusInfo, setBakRcvStatusInfo] = useState<BackupRecoverStatusInfo>({
        fileCount: 0, handledFileCount: 0, timeSpendInSec: 0, msg: null, status: 0
    });
    const [showRestartModal, setShowRestartModal] = useState<boolean>(false);
    const [activeTab, setActiveTab] = useState<string>('dataDir');

    useEffect(() => {
        async function useEffectAsync() {
            (async () => {
                try {
                    const tempFeatDetails = await getSystemFeatDetails();
                    setFeatDetails({...tempFeatDetails});
                    setFeatDetailsEdit({...tempFeatDetails})
                } catch (e: any) {
                    console.error(e.msg);
                
            })().then();
            (async () => {
                try {
                    if (isTauriEnv) {
                        const tempUsageMode = await invokeGetUsageMode();
                        setUsageMode({...tempUsageMode});
                        setUsageModeEdit({...tempUsageMode});
                    
                } catch (e: any) {
                    console.error(e.msg);
                
            })().then();
            (async () => {
                try {
                    if (isTauriEnv) {
                        const tempBakRcvSInfo = await invokeGetBakRcvStatusInfo();
                        setBakRcvStatusInfo({...tempBakRcvSInfo});
                    
                } catch (e: any) {
                    console.error(e.msg);
                
            })().then();

            listen_to_bak_rcv_info_changed();
        
        useEffectAsync().then();
    }, []);

    const onChangeADDCCEnable = async (enable: boolean) => {
        setFeatDetailsEdit({
            ...featDetailsEdit,
            userDataDirCustomConfig: {...featDetailsEdit.userDataDirCustomConfig, enable, warning: '更改尚未保存！'
        });
    
    const onClickADDCCPath = async () => {
        const selected = await open({
            multiple: false,
            directory: true,
            defaultPath: featDetailsEdit.userDataDirCustomConfig.path,
        });
        if (selected === null || selected === "") {
            return;
        

        if (selected === featDetailsEdit.userDataDirCustomConfig.path) {
            return;
        

        setFeatDetailsEdit({
            ...featDetailsEdit,
            userDataDirCustomConfig: {
                ...featDetailsEdit.userDataDirCustomConfig,
                path: selected,
                warning: '更改尚未保存！'
            
        });
    
    const onClickADDCC = async () => {
        try {
            await setUserDataDirCustomConfig(featDetailsEdit.userDataDirCustomConfig);
            message.success('保存成功！');
            setFeatDetailsEdit({
                ...featDetailsEdit,
                userDataDirCustomConfig: {
                    ...featDetailsEdit.userDataDirCustomConfig,
                    warning: null,
                },
            })
            setFeatDetails({
                ...featDetailsEdit,
                userDataDirCustomConfig: {
                  ...featDetailsEdit.userDataDirCustomConfig,
                  warning: null,
                },
            })
        } catch (e: any) {
            message.error(`${e.msg}`);
        
    

    const onChangeUsageMode = async (mode: number) => {
        setUsageModeEdit({...usageModeEdit, mode, warning: '更改尚未保存！'});
    
    const onChangeUsageModeUrl = async (url: string) => {
        setUsageModeEdit({...usageModeEdit, url, warning: '更改尚未保存！'});
    
    const onChangeUsageModePort = async (port: number | null) => {
        if (port === null || port === undefined) {
            message.error("请输入有效的网络端口号");
            return;
        
        setUsageModeEdit({...usageModeEdit, port, warning: '更改尚未保存！'});
    
    const onClickUsageMode = async () => {
        try {
            if (usageModeEdit.mode === 1 && usageModeEdit.url.trim() === "") {
                message.error("请设置正确的url!");
                return;
            
            await invokeSetUsageMode(usageModeEdit);
            message.success('保存成功！');
            setUsageModeEdit({...usageModeEdit, warning: null});
            setUsageMode({...usageModeEdit, warning: null});
        } catch (e) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-expect-error
            message.error(`${e.msg}`);
        
        // 如果是客户端模式，就从远程服务器上获取数据集
        if (usageModeEdit.mode === 1) {
            const tempFeatDetails = await getSystemFeatDetails();
            setFeatDetails({...tempFeatDetails});
            setFeatDetailsEdit({...tempFeatDetails});
        
    

    const onClickBackupBtn = async () => {
        const tempBakRcvSInfo = await invokeGetBakRcvStatusInfo();
        setBakRcvStatusInfo({...tempBakRcvSInfo});
        if (tempBakRcvSInfo.status === 1 || tempBakRcvSInfo.status === 4) {
            message.warning('当前正在备份或还原中，请耐心等候');
            return;
        

        const fileName = `BookGuard_backup_${new Date().getTime()}.zip`;
        const selected = await save({
            title: '请选择备份文件保存位置',
            defaultPath: fileName,
            filters: [{
                name: '备份文件',
                extensions: ['zip']
            }]
        });
        if (selected === null || selected === "") {
            return;
        

        try {
            await invokeBackupUserData(selected);
        } catch (e) {
            message.error(`${e}`);
        
    

    const onClickCancelBackupBtn = async () => {
        try {
            await invokeCancelBackupUserData();
            const tempBakRcvSInfo = await invokeGetBakRcvStatusInfo();
            setBakRcvStatusInfo({...tempBakRcvSInfo});

            message.success('取消备份成功');
        } catch (e) {
            message.error(`${e}`);
        
    

    const onClickRecoverBtn = async () => {
        const tempBakRcvSInfo = await invokeGetBakRcvStatusInfo();
        setBakRcvStatusInfo({...tempBakRcvSInfo});
        if (tempBakRcvSInfo.status === 1 || tempBakRcvSInfo.status === 4) {
            message.warning('当前正在备份或还原中，请耐心等候');
            return;
        

        const selected = await open({
            multiple: false,
            filters: [{
                name: '备份文件',
                extensions: ['zip']
            }]
        });
        if (selected === null || selected === "") {
            return;
        

        try {
            await invokeRecoverUserData(selected);
        } catch (e) {
            message.error(`${e}`);
        
    

    const listen_to_bak_rcv_info_changed = async () => {
        if (isTauriEnv) {
            await listen<string>("bak-rcv-info-changed", async () => {
                const tempInfo = await invokeGetBakRcvStatusInfo();
                setBakRcvStatusInfo({...tempInfo});
                // 恢复完成，弹窗提示用户重启
                if (tempInfo.status === 5) {
                    setShowRestartModal(true);
                
            });
        
    

    function BakRcvTag({status}: {status: number}) {
        switch (status) {
            case 1:
                return <Tag icon={<LoadingOutlined />} color="geekblue">备份中</Tag>;
            case 2:
                return <Tag color="green">备份成功</Tag>;
            case 3:
                return <Tag icon={<ExceptionOutlined />} color="red">备份出错</Tag>;
            case 4:
                return <Tag icon={<LoadingOutlined />} color="geekblue">恢复中</Tag>;
            case 5:
                return <Tag color="green">恢复成功</Tag>;
            case 6:
                return <Tag icon={<ExceptionOutlined />} color="red">恢复出错</Tag>;
            default:
                return <Tag>未开始</Tag>
        
    

    // 数据目录配置组件
    const DataDirConfig = () => (
        <div>
            <Space direction="vertical" size="middle" style={{width: '100%'}}>
                <Space.Compact style={{width: '100%'}}>
                    <Switch
                        disabled={!isTauriEnv || bakRcvStatusInfo.status === 1 || bakRcvStatusInfo.status === 4
                        value={featDetailsEdit.userDataDirCustomConfig.enable
                        onChange={onChangeADDCCEnable
                    />
                    <span>自定义用户数据文件夹（保存成功后立即生效。需提前评估数据规模，确保文件夹下有足够存储空间）</span>
                    {featDetailsEdit.userDataDirCustomConfig.warning && (
                        <span style={{marginLeft: '10px', color: 'orange'}}>
                            <WarningOutlined/>
                            {featDetailsEdit.userDataDirCustomConfig.warning
                        </span>
                    )
                </Space.Compact>
            </Space>
            {usageMode.mode === 1 && (
                <div style={{color: "red", marginTop: '10px'}}>
                    当前是客户端模式，因此修改的是服务端的用户数据文件夹配置，请谨慎操作！
                </div>
            )
            <div style={{marginTop: '15px'}}>
                <Input
                    disabled={!isTauriEnv || !featDetailsEdit.userDataDirCustomConfig.enable || bakRcvStatusInfo.status === 1 || bakRcvStatusInfo.status === 4
                    placeholder="请输入自定义应用数据文件夹路径"
                    value={featDetailsEdit.userDataDirCustomConfig.path
                    style={{width: '70%', marginRight: '10px'}
                    onClick={() => onClickADDCCPath()
                />
                <Button
                    type="primary"
                    disabled={!isTauriEnv || bakRcvStatusInfo.status === 1 || bakRcvStatusInfo.status === 4
                    onClick={onClickADDCC
                >
                    保存
                </Button>
            </div>
        </div>
    );

    // 使用模式配置组件
    const UsageModeConfig = () => (
        <div>
            <div>
                <Radio.Group disabled={!isTauriEnv} value={usageModeEdit.mode} onChange={(e) => onChangeUsageMode(e.target.value)}>
                    <Radio.Button value={0}>单机</Radio.Button>
                    <Radio.Button value={1}>客户端</Radio.Button>
                    <Radio.Button value={2}>服务器</Radio.Button>
                </Radio.Group>
                <span style={{marginLeft: '10px'}}>保存成功后重启软件生效</span>
                {usageModeEdit.warning && (
                    <span style={{marginLeft: '10px', color: 'orange'}}>
                        <WarningOutlined/>
                        {usageModeEdit.warning
                    </span>
                )
            </div>
            <div style={{margin: '15px 0'}}>
                {usageModeEdit.mode === 0 && (<>注意：单机模式只能本机使用此软件。</>)
                {usageModeEdit.mode === 1 && (<>客户端模式下允许本机通过网络访问其他设备的数据。</>)
                {usageModeEdit.mode === 2 && (<>服务器模式下允许其他设备通过网络访问本机的数据。</>)
            </div>
            {usageModeEdit.mode === 1 && (
                <div style={{marginBottom: '15px'}}>
                    <Input
                        placeholder="请输入要访问设备的url"
                        value={usageModeEdit.url
                        required
                        style={{width: '70%'}
                        onChange={(e) => onChangeUsageModeUrl(e.target.value)
                    />
                </div>
            )
            {usageModeEdit.mode === 2 && (
                <div style={{marginBottom: '15px'}}>
                    <span>请输入网络端口号：</span>
                    <InputNumber required min={1} value={usageModeEdit.port} onChange={onChangeUsageModePort} style={{marginLeft: '10px'}} />
                </div>
            )
            <Button disabled={!isTauriEnv} type="primary" onClick={onClickUsageMode}>保存</Button>
        </div>
    );

    // 备份恢复组件
    const BackupRecoverConfig = () => (
        <div>
            <p>用户数据备份与恢复（只能备份或恢复本机数据）</p>
            <div style={{marginBottom: '15px'}}>
                {bakRcvStatusInfo.status !== 1 && (
                    <Button
                        disabled={!isTauriEnv || usageMode.mode === 1 || bakRcvStatusInfo.status === 4
                        onClick={onClickBackupBtn
                        style={{marginRight: '10px'}
                    >
                        备份
                    </Button>
                )
                {bakRcvStatusInfo.status === 1 && (
                    <Button
                        color="danger"
                        variant="dashed"
                        onClick={onClickCancelBackupBtn
                        style={{marginRight: '10px'}
                    >
                        取消备份
                    </Button>
                )
                <Button
                    disabled={!isTauriEnv || usageMode.mode === 1 || !featDetails.userDataDirCustomConfig.enable || bakRcvStatusInfo.status === 1 || bakRcvStatusInfo.status === 4
                    onClick={onClickRecoverBtn
                >
                    恢复
                </Button>
            </div>
            {usageMode.mode === 1 && (
                <div style={{marginBottom: '15px'}}>
                    <span style={{color: 'orange'}}>
                        <WarningOutlined style={{marginRight: '5px'}}/>
                        客户端模式下备份/恢复功能不可用
                    </span>
                </div>
            )
            {!featDetails.userDataDirCustomConfig.enable && (
                <div style={{marginBottom: '15px'}}>
                    <span style={{color: 'orange'}}>
                        <WarningOutlined style={{marginRight: '5px'}}/>
                        为了避免将C盘空间占满，您必须自定义用户数据文件夹，方可恢复数据！
                    </span>
                </div>
            )
            <div style={{marginBottom: '15px'}}>
                <p style={{color: 'red', margin: 0}}>备份或恢复数据时请勿增量更新、修改自定义用户数据文件夹或关闭应用</p>
            </div>
            {bakRcvStatusInfo.status !== 0 && (
                <div>
                    <p>完成文件数/总文件数：{bakRcvStatusInfo.handledFileCount} / {bakRcvStatusInfo.fileCount}</p>
                    <div style={{marginBottom: '10px'}}>
                        <span>状态：</span>
                        <BakRcvTag status={bakRcvStatusInfo.status} />
                        <span style={{marginLeft: '10px'}}>耗时：<TimeSpendView timeSpendInSec={bakRcvStatusInfo.timeSpendInSec}/></span>
                        {bakRcvStatusInfo.msg && <span style={{marginLeft: '10px'}}>{bakRcvStatusInfo.msg}</span>
                    </div>
                    <Progress
                        percent={Number((bakRcvStatusInfo.fileCount > 0 ? bakRcvStatusInfo.handledFileCount / bakRcvStatusInfo.fileCount * 100 : 0).toFixed(3))
                        status={((s: number) => {
                            if (s === 1 || s === 4) {
                                return "active";
                            } else if (s === 2 || s=== 5) {
                                return "success";
                            } else if (s === 3 || s === 6) {
                                return "exception";
                            } else {
                                return "normal";
                            
                        })(bakRcvStatusInfo.status)
                    />
                </div>
            )
            <Modal
                open={showRestartModal
                closable={false
                maskClosable={false
                keyboard={false
                footer={null
            >
                <p style={{color: "red", textAlign: 'center'}}>数据恢复成功，请立即手动重启应用。</p>
            </Modal>
        </div>
    );

    // 定义tabs配置
    const tabItems: TabsProps['items'] = [
        {
            key: 'dataDir',
            label: '数据目录',
            children: <DataDirConfig />
        },
        {
            key: 'usageMode',
            label: '使用模式',
            children: <UsageModeConfig />
        },
        {
            key: 'backupRecover',
            label: '备份恢复',
            children: <BackupRecoverConfig />
        
    ];

    return (
        <div style={{ padding: '20px' }}>
            <Tabs
                tabPosition="left"
                activeKey={activeTab
                onChange={setActiveTab
                items={tabItems
                style={{ height: '100%' }
            />
        </div>
    );


export default SysFeatureManagement;

import {MathJax} from "better-react-mathjax";
import {useEffect, useState} from "react";
import './Mathdown.css';

const Mathdown = ({content = ''}) => {
    const [mathContent, setMathContent] = useState('');

    useEffect(() => {
        setMathContent(content);
    }, [content]);

    return (
        <div className={'mathdown'}>
            <MathJax style={{whiteSpace: 'pre-wrap', wordWrap: 'break-word'}
                     dynamic={true
                     dangerouslySetInnerHTML={{__html: mathContent}}>
            </MathJax>
        </div>
    );
};

export default Mathdown;

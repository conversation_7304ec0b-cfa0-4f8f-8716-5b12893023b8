import {useParams} from "react-router-dom";
import {MutableRefObject, useEffect, useRef, useState} from "react";
import {Empty, GetProps, TabsProps, TreeDataNode, TreeProps} from 'antd';
import {Col, Divider, Row, Tabs, Tree, Input, Card, Button, message, Spin, Pagination} from 'antd';
import './BookReader.css'
import {DownOutlined, SwapRightOutlined} from '@ant-design/icons';
import {convertToTreeData} from "../../utils/Book.ts";
import {
    Catalogue, Content, Snapshot, Textbook, CardSearchResult
} from "../../interface/BookInterface.ts";
import Mathdown from "../mathdown/Mathdown.tsx";
import React from 'react';
// import jsPDF from 'jspdf';
import {getFeFileUrlPrefix} from "../../invoker/file-url.ts";
import {buildFileUrl, buildImgSrc} from "../../utils/fileUrl.ts";
import {highlight_text,processContent} from "../../utils/text.ts";
import {invokeGetBookByBookId} from "../../invoker/book.ts";
import {invokeGetCataloguesByBookId} from "../../invoker/catalogue.ts";
import {invokeGlobalSearchCards} from "../../invoker/searching.ts";
import {invokeGetCardInfosByBookId} from "../../invoker/card.ts";

type SearchProps = GetProps<typeof Input.Search>;
const {Search} = Input;

// Lazy loaded image component with IntersectionObserver
interface LazyImageProps {
    src: string;
    alt: string;
    className: string;
    onLoad?: () => void;
    onMouseEnter?: () => void;
    imgScale:number


const LazyImage: React.FC<LazyImageProps> = ({ src, alt, className, onLoad, onMouseEnter, imgScale }) => {
    const [isLoaded, setIsLoaded] = useState(false);
    const [inView, setInView] = useState(false);
    const [error, setError] = useState(false);
    const [aspectRatio, setAspectRatio] = useState<number | null>(null);
    const imgRef = useRef<HTMLDivElement>(null);

    // Determine if this is a thumbnail based on className
    const isThumbnail = className === "book_thumb-img";

    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        setInView(true);
                        observer.unobserve(entry.target);
                    
                });
            },
            {
                rootMargin: isThumbnail ? '400px' : '200px', // Load thumbnails earlier
                threshold: isThumbnail ? 0.01 : 0.1 // Lower threshold for thumbnails
            
        );

        if (imgRef.current) {
            observer.observe(imgRef.current);
        

        return () => {
            if (imgRef.current) {
                observer.unobserve(imgRef.current);
            
        };
    }, [isThumbnail]);

    const handleLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
        setIsLoaded(true);

        // Get the natural dimensions of the loaded image
        const img = e.target as HTMLImageElement;
        if (img.naturalWidth && img.naturalHeight) {
            const ratio = img.naturalWidth / img.naturalHeight;
            setAspectRatio(ratio);
        

        if (onLoad) onLoad();
    };

    const handleMouseEnter = (e: React.SyntheticEvent<HTMLImageElement>) => {
        if (onMouseEnter) onMouseEnter();
    

    const handleError = () => {
        setError(true);
        setIsLoaded(true);
    };

    return (
      <div
          ref={imgRef
          className={`image-container ${isLoaded ? 'loaded' : 'loading'} ${isThumbnail ? 'thumbnail-container' : ''}`
          style={isThumbnail ? {
              height: error ? '60px' : 'auto',
              overflow: 'hidden',
              backgroundColor: '#f9f9f9',
              borderRadius: '4px',
          } : {
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              position: 'relative',
              minHeight: !isLoaded && !error ? '200px' : 'auto',
              width: isLoaded ? 'fit-content' : '100%',
              height: isLoaded ? 'fit-content' : '100%',
              margin: '0 auto',
          }
      >
          {inView && !isLoaded && !error && (
              <div style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  zIndex: 1
              }}>
                  <Spin size={isThumbnail ? "small" : "default"} />
              </div>
          )
          {inView && !error && (
            <img
                className={className
                src={src
                alt={alt
                onLoad={handleLoad
                onMouseEnter={handleMouseEnter
                onError={handleError
                style={{
                    opacity: isLoaded ? 1 : 0,
                    transition: 'opacity 0.3s',
                    position: 'relative',
                    zIndex: 0,
                    ...(className === "content-img" && aspectRatio && {
                        // aspectRatio: aspectRatio < 1 ? '1/1.414' : 'auto',
                        // maxHeight: aspectRatio < 1 ? 'calc(100vh - 200px)' : 'auto',
                        maxWidth: aspectRatio >= 1 ? '100%' : 'auto',
                        minWidth:800*imgScale,
                        minHeight:1300*imgScale
                    })
                }
            />
          )
          {error && (
              <div className="image-error" style={{
                  textAlign: 'center',
                  padding: '10px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#f5f5f5',
                  borderRadius: '4px',
                  fontSize: isThumbnail ? '12px' : '14px'
              }}>
                  <p>图片加载失败</p>
              </div>
          )
      </div>
    );
};
const TextbookThumbImage: React.FC<{
    snapshot: Snapshot; index: number; onClick?: () => void; highlight: boolean
}> = ({snapshot, index, onClick, highlight}) => {
    const className = `thumbnail-card ${highlight ? 'touming' : ''}`;
    return (
      <div
          className={className
          onClick={onClick
      >
            <LazyImage
                className={"book_thumb-img"
                src={snapshot.url
                imgScale={1
                alt={'snapshot_' + index
            />
            {snapshot.page > 0 ? (
                <div
                    className={`thumbnail-page-indicator ${highlight ? 'highlight-thumbnail' : ''}`}>
                    第{snapshot.page}页
                </div>
            ) : null
        </div>
    );
};

const TextbookImage: React.FC<{
    snapshot: Snapshot; index: number; onMouseEnter?: () => void;imgScale:number
}> = ({snapshot, index, onMouseEnter,imgScale}) => {

    return (
        <div style={{ width: '800px;', display: 'flex', flexDirection: 'column', alignItems: 'center', marginBottom: '30px' }}>
            <LazyImage
                imgScale={imgScale
                className={"content-img"
                src={snapshot.url
                alt={'snapshot_' + index
                onMouseEnter={onMouseEnter
            />
            {snapshot.page > 0 ?
                <Divider plain style={{ width: '80%' }}>第{snapshot.page}页</Divider>
                : <Divider style={{ width: '80%' }}></Divider>
        </div>
    );
};


const TextbookContentText: React.FC<{
    content: Content;
    index: number;
    onMouseEnter?: () => void;
}> = ({content, index, onMouseEnter}) => {
    const [isVisible, setIsVisible] = useState(false);
    const textRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        setIsVisible(true);
                        onMouseEnter?.();
                        observer.unobserve(entry.target);
                    
                });
            },
            {
                rootMargin: '100px',
                threshold: 0.1
            
        );

        if (textRef.current) {
            observer.observe(textRef.current);
        

        return () => {
            if (textRef.current) {
                observer.unobserve(textRef.current);
            
        };
    }, [onMouseEnter]);

    return (
        <div style={{ width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center', marginBottom: '30px' }}>
            <div
                ref={textRef
                style={{
                    width: '100%',
                    maxWidth: '800px',    // PDF.js 默认宽度
                    minHeight: '1132px',
                    padding: '20px',
                    backgroundColor: '#fff',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                    borderRadius: '4px',
                    // aspectRatio: '1/1.414',  // 保持 A4 比例
                    margin: '0 auto',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: content.loadStatus ? 'flex-start' : 'center'
                }
            >
                {content.loadStatus ? (
                    <div style={{
                        whiteSpace: 'pre-wrap',
                        flex: 1,
                        fontSize: '16px',
                        lineHeight: '1.8',
                        padding: '20px',
                        maxWidth: '100%',
                        margin: '0 auto'
                    }}>
                        <Mathdown content={content.text}/>
                    </div>
                ) : (
                    <div
                        style={{
                            textAlign: 'center',
                            color: '#666'
                        }
                    >
                        <h3>
                            {isVisible ? '加载中...' : '滚动加载...'
                        </h3>
                    </div>
                )
            </div>
            <Divider plain style={{ width: '80%' }}>第{content.page}页</Divider>
        </div>
    )
};


function TextbookReader() {
    const [messageApi, contextHolder] = message.useMessage();
    const [urlPrefix, setUrlPrefix] = React.useState<string>("");
    const {bookId} = useParams<{ bookId: string }>();
    const {page} = useParams<{ page: string }>();
    const [highlights, setHighlights] = useState<string[]>(['']);
    const [naviTab, setNaviTab] = useState<string>("img");
    const [contentTab, setContentTab] = useState<string>("img");
    const [ppage, setPpage] = useState(1);
    const [searchInfo, setSearchInfo] = useState<{
        pageNo: number;
        pageSize: number;
        searchText: string;
        subjectCode: null;
        bookId: number;
    }>({
        pageNo: 0,
        pageSize: 10,
        searchText: "",
        subjectCode: null,
        bookId: 0
    });
    const [searchResult, setSearchResult] = useState<{
        highlights:string[],
        list:any[],
        totals:number
    }>({
        // duration: 0,
        highlights: [],
        list: [],
        // pageCount: 0,
        // pageNo: 0,
        // pageSize: 0,
        totals: 0
    })
    const [searchLoading, setSearchLoading] = useState<boolean>(false)
      const [loadingImages, setLoadingImages] = useState<boolean>(true);
      const [visibleImages, setVisibleImages] = useState<number[]>([]);

    const [book, setBook] = useState<Textbook>({
        bookId: 0,
        batch:0,
        author: "",  bookName: "", edition: "", institutionCode: "", isbn: "", publisher: "", series: "",
        snapshots: [], contents: [], subjectCode: "", subjectName: "", type: "", updateTime: "", version: ""
    });
    const [cataloguesNodes, setCataloguesNodes] = useState<TreeDataNode[]>([]);
    const [targetThumbIndex, setTargetThumbIndex] = useState<number>(-2);

    const [expandedKeys, setExpandedKeys] = useState<any[]>([]);

    const textRefs: MutableRefObject<any[]> = useRef([]);
    const imgRefs: MutableRefObject<any[]> = useRef([]);
    const thumbImgRefs: MutableRefObject<any[]> = useRef([]);
    const initPage = useRef(true);
      const imageContainerRef = useRef<HTMLDivElement>(null);
    let initData = false;

    // Progressive loading of snapshots
    const loadImagesProgressively = (snapshots: Snapshot[], urlPrefix: string) => {
        // This function loads only the visible images and a few nearby ones
        setLoadingImages(true);

        // Create a new array with processed snapshots
        const processedSnapshots = [...snapshots];

        // Process in batches, prioritizing visible images first
        const batchSize = 5;
        let processed = 0;

        const processNextBatch = () => {
            const batch = processedSnapshots.slice(processed, processed + batchSize);
            if (batch.length === 0) {
                setLoadingImages(false);
                return;
            

            batch.forEach(snapshot => {
                snapshot.url = buildFileUrl(urlPrefix, snapshot.path);
            });

            processed += batchSize;

            // Update the book with processed snapshots
            setBook(prevBook => ({
                ...prevBook,
                snapshots: [...processedSnapshots]
            }));

            // Continue with next batch
            if (processed < processedSnapshots.length) {
                setTimeout(processNextBatch, 100); // Slight delay to prevent UI freezing
            } else {
                setLoadingImages(false);
            
        };

        // Start processing
        processNextBatch();
    };
    async function getBookInfo() {
        const res = await invokeGetBookByBookId(bookId as string);
        res.data.length > 0 ?
            initData = true
            : initData = false;
        if (!initData) {
            messageApi.open({
                type: 'error',
                content: '书本无数据',
            });
        } else {
            messageApi.open({
                key:"loading",
                type: 'loading',
                content: '查询中',
                duration: 0,
            });
        
        if (initData) {
            const tempUrlPrefix = await getFeFileUrlPrefix();
            setUrlPrefix(tempUrlPrefix);

            const nBook: Textbook = res.data[0];
            nBook.contents = [];

            try {
                // Initialize contents array without loading images yet
                const cardInfos = await invokeGetCardInfosByBookId(Number(bookId));
                for (const info of cardInfos) {
                    nBook.contents.push({page: info.page, text: info.text, loadStatus: false});
                
            } catch (err) {
               console.error(err);
            

            setBook(nBook);

            // Load images progressively after setting the initial book state
            loadImagesProgressively(nBook.snapshots, tempUrlPrefix);

            const res2 = await invokeGetCataloguesByBookId(bookId as string);
            const catalogues: Catalogue[] = res2.data;
            const treeData = convertToTreeData(catalogues);
            setCataloguesNodes(treeData);
            // console.log("cataloguesNodes:",cataloguesNodes)

            if (page) {
                const jumpPage = Number(page);
                const jumpImgIndex = nBook.snapshots.findIndex(sp => sp.page === jumpPage);
                if (jumpImgIndex >= 0) {
                    setContentTab('img');
                    jumpForContentImg(jumpImgIndex);
                } else {
                    setContentTab('text');
                    jumpForContentText(jumpPage);
                
            } else {
                if (nBook.snapshots.length === 0) {
                    setContentTab('text')
                
            
            messageApi.destroy("loading")
            messageApi.open({
                type: 'success',
                content: '打开书本成功',
            });
        
    

    useEffect(() => {
        async function useEffectAsync() {
            const tempUrlPrefix = await getFeFileUrlPrefix();
            setUrlPrefix(tempUrlPrefix);
        
        useEffectAsync().then();
    }, []);

    useEffect(() => {
        if (initPage.current) {
            const asyncFunction = async () => {
                await getBookInfo()
            };
            asyncFunction().then(() => {
            });
        
        initPage.current = false;
    }, [initPage.current,ppage,searchResult])

    // Setup intersection observer for content images
    useEffect(() => {
        if (contentTab === 'img' && imageContainerRef.current) {
            const options = {
                root: imageContainerRef.current,
                rootMargin: '200px 0px', // Load images slightly before they come into view
                threshold: 0.1
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    const index = parseInt(entry.target.getAttribute('data-index') || '-1');
                    if (entry.isIntersecting && index >= 0) {
                        setVisibleImages(prev => {
                            if (!prev.includes(index)) {
                                return [...prev, index];
                            
                            return prev;
                        });
                    
                });
            }, options);

            // Observe all image containers
            imgRefs.current.forEach((el, index) => {
                if (el) {
                    el.setAttribute('data-index', index.toString());
                    observer.observe(el);
                
            });

            return () => {
                imgRefs.current.forEach(el => {
                    if (el) observer.unobserve(el);
                });
            };
        
    }, [contentTab, book.snapshots.length]);


    const selectCatalogues: TreeProps['onSelect'] = (selectedKeys, info) => {
        const node: any = info.node
        setExpandedKeys(selectedKeys)
        jump(node.page, 'page')
    };


    const clickThumbImage = (index: number) => {
        setTargetThumbIndex(index)
        jump(index, 'index')
    

    const jumpThumbImage = (index: number) => {
        setTargetThumbIndex(index)
        if (thumbImgRefs.current[index]) {
            thumbImgRefs.current[index].scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        
    

    const jump = (value: number, type: string) => {
        if (book.bookId === 0)
            return
        let jumpIndex = 0
        let jumpPage = 0

        if (type === 'page') {
            jumpPage = value
            for (let index = 0; index < book.snapshots.length; index++) {
                if (book.snapshots[index].page === jumpPage) {
                    jumpIndex = index
                
            
        

        if (type === 'index') {
            jumpIndex = value
            if (book.snapshots[jumpIndex] !== undefined) {
                jumpPage = book.snapshots[jumpIndex].page;
            
        

        jumpThumbImage(jumpIndex);

        if (contentTab === 'search') {
            setContentTab('img')
            jumpForContentImg(jumpIndex)
        
        if (contentTab === 'img') {
            jumpForContentImg(jumpIndex)
        
        if (contentTab === 'text') {
            jumpForContentText(jumpPage)
        
    

    const jumpForContentText = (page: number) => {
        setTimeout(() => {
            if (textRefs.current[page]) {
                textRefs.current[page].scrollIntoView()
            
        }, 150);
    

    const jumpForContentImg = (index: number) => {
        // 跳转，提供index
        setTimeout(() => {
            if (imgRefs.current[index]) {
                imgRefs.current[index].scrollIntoView()
            
        }, 150);
    


    async function loadText(index: number) {
        const updatedContents = [...book.contents];
        const targetContent = updatedContents[index];

        const targetSnapIndex = book.snapshots.findIndex(sp => sp.page === targetContent.page);
        jumpThumbImage(targetSnapIndex);

        const {loadStatus} = targetContent
        let {text} = targetContent;
        if (loadStatus) {
            return
        
        if (text === null || text.trim() === '') {
            text = "暂无数据";
        
        // console.log(text)
        text = buildImgSrc(urlPrefix, text);
        targetContent.text = text
        targetContent.loadStatus = true
        updatedContents[index] = targetContent;
        setBook((prevBook) => ({
            ...prevBook,
            contents: updatedContents,
        }));
    

    const onNaviTabChange = (key: string) => {
        setNaviTab(key)
    };

    useEffect(() => {
        if (targetThumbIndex > -2 && thumbImgRefs.current[targetThumbIndex])
            thumbImgRefs.current[targetThumbIndex].scrollIntoView()
    }, [naviTab]);

    const clickSearchResult = (searchContent: CardSearchResult) => {
        jump(searchContent.page, 'page')
    

    const onContentTabChange = (key: string) => {
        setContentTab(key)
    };
    useEffect(() => {
        if (contentTab === 'search') {
            return
        

        if (targetThumbIndex > -2) {
            jump(targetThumbIndex, 'index')
        

    }, [contentTab]);

    const searchClick = async (value: string, event?: any, source?: any) => {
        if (!value) {
            message.error('搜索内容为空')
            // return
        
        setPpage(1);
        const predictCardParams = {
            pageNo: 0,
            pageSize: 10,
            searchText: value,
            subjectCode: null,
            bookId: Number(bookId)
        
        setSearchInfo(predictCardParams)
        await searchBookContent(predictCardParams)
    

    const paginationOnChange = async (pages: number, pageSize: number) => {
        setPpage(pages);
        const predictCardParams = {
            ...searchInfo,
            pageNo: pages-1,
            pageSize: pageSize,
            // subjectCode: "",
            bookId: Number(bookId)
        
        setSearchInfo(predictCardParams)
        await searchBookContent(predictCardParams)
    

    const searchBookContent = async (predictCardParams:any) => {
        setSearchLoading(true)
        const res = await invokeGlobalSearchCards(predictCardParams);
        if (res.totals === 0) {
            message.info('无搜索结果')
        
        setHighlights(res.highlights)
        // 获取 URL 前缀
        const urlPrefix = await getFeFileUrlPrefix();

        // 遍历 list，为每个 content 中的 img 标签的 src 加上 urlPrefix
        const updatedList = res.list.map((item:any) => {
            const updatedContent = item.content.replace(
                /<img[^>]+src="([^">]+)"/g,
                (match: any, src: any) => {
                    // 在 src 前面拼接 urlPrefix
                    return `<img src="${urlPrefix}/${src}"`;
                
            );
            // 返回更新后的对象
            return {
                ...item,
                // content: updatedContent,
                content: updatedContent,
            };
        });

        // 更新搜索结果
        setSearchResult({
            ...res,
            list: updatedList,
        });
        setSearchLoading(false)
    

    const naviTabs: TabsProps['items'] = [
        {key: 'img', label: '缩略图',},
        {key: 'catalogue', label: '目录',
    ];

    const contentTabs: TabsProps['items'] = [
        {key: 'text', label: '文字版',},
        {key: 'img', label: '图片版',},
        {key: 'search', label: '内容搜索',
    ];

    // const exportPdf = async () => {
    //     setShowProgress(true); // 点击后显示进度条
    //     const doc = new jsPDF();
    //     const pageWidth = doc.internal.pageSize.getWidth();
    //     const pageHeight = doc.internal.pageSize.getHeight();
    //
    //     const totalSnapshots = book.snapshots.length;
    //
    //     for (let i = 0; i < totalSnapshots; i++) {
    //         const img = new Image();
    //         img.src = book.snapshots[i].url;
    //
    //         await new Promise<void>((resolve) => {
    //             img.onload = () => {
    //                 const originalWidth = img.width;
    //                 const originalHeight = img.height;
    //                 const aspectRatio = originalWidth / originalHeight;
    //
    //                 let newWidth = pageWidth;
    //                 let newHeight = pageWidth / aspectRatio;
    //
    //                 if (newHeight > pageHeight) {
    //                     newHeight = pageHeight;
    //                     newWidth = pageHeight * aspectRatio;
    //                 
    //
    //                 doc.addImage(img, 'JPEG', 0, 0, newWidth, newHeight);
    //
    //                 if (i < totalSnapshots - 1) {
    //                     doc.addPage();
    //                 
    //                 resolve();
    //             };
    //         });
    //
    //         // 更新进度条
    //         const newProgress = Math.round(((i + 1) / totalSnapshots) * 100);
    //         setProgress(newProgress);
    //     
    //     doc.save(`${book.bookName}.pdf`);
    //     setProgress(0); // 导出完成后重置进度
    //     setShowProgress(false); // 隐藏进度条
    // };

    const [imgScale, setImgScale] = useState(1.4);

    // 放大
    async function addScale(){
        setImgScale(imgScale+0.2)
    

    // 缩小
    function lessScale() {
        setImgScale(imgScale-0.2);
    
    // 新增的滚动控制逻辑
    useEffect(() => {
        if (contentTab === 'search') {
            const searchContainer = document.querySelector('.search-group-mid');
            if (searchContainer) {
                searchContainer.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            
        
    }, [searchResult]); // 当搜索结果变化时触发


    return (
        <>
            {contextHolder
            <Row gutter={16}>
                <Col span={3}>
                    <Tabs
                        items={naviTabs
                        onChange={onNaviTabChange
                        activeKey={naviTab
                        style={{ marginBottom: 0 }
                    />
                    {naviTab === 'img' ? (
                        <div className={'book_thumb-image-group'} key="thumb_img">
                            {loadingImages && book.snapshots.length > 0 && (
                                <div className="loading-indicator">
                                    <Spin tip="正在加载图片..." />
                                </div>
                            )
                            {book.snapshots.length > 0 && (
                            // {!loadingImages && book.snapshots.length > 0 && (
                                <div style={{
                                    paddingRight: '5px',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                }}>
                                    {book.snapshots.map((snapshot, index) => (
                                        <div
                                            key={'thumb_img_' + index
                                            ref={(el) => {
                                                thumbImgRefs.current[index] = el
                                            }
                                        >
                                            <TextbookThumbImage
                                                snapshot={snapshot
                                                index={index
                                                onClick={() => clickThumbImage(index)
                                                highlight={targetThumbIndex === index
                                            />
                                        </div>
                                    ))
                                </div>
                            )
                            {!loadingImages && book.snapshots.length === 0 && (
                                <Empty description="暂无缩略图" />
                            )
                        </div>
                    ) : null
                    {naviTab === 'catalogue' ? (
                        <Tree
                            height={500
                            switcherIcon={<DownOutlined/>
                            showLine
                            blockNode
                            onSelect={selectCatalogues
                            treeData={cataloguesNodes
                            expandedKeys={expandedKeys
                            autoExpandParent={true}/>

                    ) : null
                    </Col>
                    <Col span={21}>
                        <Row gutter={16} align="middle">
                            <Col span={12}>
                                <Tabs
                                    items={contentTabs
                                    onChange={onContentTabChange
                                    activeKey={contentTab
                                    style={{ marginBottom: 0 }
                                />
                            </Col>
                            <Col span={12}>
                                <p style={{
                                    color: '#e03e2d',
                                    textAlign: 'right',
                                }}>温馨提示：文字版仅供检索及参考，最终以图片版为准</p>
                            </Col>
                            {/*<Col span={3}>*/
                            {/*    {showProgress ? ( // 根据状态决定是否渲染进度条*/
                            {/*        <Button>*/
                            {/*            <Progress percent={progress} steps={5} size="small" strokeColor="#4caf50"/>*/
                            {/*        </Button>*/
                            {/*    ):<Button type={"primary"} onClick={exportPdf}>导出 PDF</Button>}*/
                            {/*</Col>*/
                        </Row>
                        {contentTab === 'text' ? (
                            <div className={'text-group'} key={"content_text"}>
                                {book.contents.map((content, index) => (
                                    content.page > 0 ?
                                        <div
                                            key={'content_text_' + content.page} style={{width: '100%'}
                                            ref={(el) => {
                                                textRefs.current[content.page] = el
                                            }}>
                                            <TextbookContentText
                                                content={content
                                                index={index
                                                onMouseEnter={() => loadText(index)
                                            />
                                        </div> : null
                                ))
                            </div>
                        ) : null
                        {contentTab === 'img' && <>
                            <div style={{position:"fixed",top:90,right:50,zIndex:9999}}>
                                <Button type={"primary"} disabled={imgScale>=3.0} style={{marginRight:10}} onClick={addScale}>放大图片</Button>
                                <Button  onClick={lessScale} disabled={imgScale<=1}>缩小图片</Button>
                            </div>

                            {book.snapshots && book.snapshots.length > 0 ? (
                                <div className={'image-group'} key="content_img" ref={imageContainerRef}>
                                    {book.snapshots.map((snapshot, index) => (
                                        <div
                                            key={'content_img_' + index
                                            ref={(el) => {
                                                imgRefs.current[index] = el
                                            }}>
                                            <TextbookImage
                                                imgScale={imgScale
                                                snapshot={snapshot
                                                index={index
                                                onMouseEnter={() => jumpThumbImage(index)
                                            />
                                        </div>
                                    ))
                                </div>
                            ) : <>
                                <Empty description="暂无图片版"/>
                            </>
                        </>
                        {contentTab === 'search' ? (
                            <div key="content_search">
                                <Search
                                    size="large"
                                    placeholder="输入搜索文本"
                                    onSearch={searchClick
                                />
                                <Spin size="large" spinning={searchLoading} style={{minHeight: '400px'}}>
                                    <div className={'search-group-mid'}>
                                        {searchResult.list.map((contentItem, index) => (
                                          <Card
                                              key={'content_search_' + index
                                              hoverable
                                              className="search-result-card"
                                              style={{marginTop: '12px'}
                                          >

                                                <Card.Grid
                                                    style={{
                                                        width: '100%',
                                                        padding: '16px',
                                                        paddingBottom: '8px'
                                                    }
                                                    >
                                                <Mathdown content={highlight_text(processContent(contentItem.content),highlights)}></Mathdown>
                                                    <div
                                                    onClick={() => clickSearchResult(contentItem)
                                                    style={{
                                                        width: '100%',
                                                        padding: '8px',
                                                        textAlign: 'right'
                                                    }
                                                >
                                                    点击跳转
                                                    <SwapRightOutlined/>
                                                    <Button type="link">
                                                        (P{contentItem.page})
                                                    </Button></div>
                                                </Card.Grid>
                                            </Card>
                                        ))
                                    </div>
                                </Spin>

                                {searchResult.totals > 0 ?
                                    <Pagination
                                        current={ppage
                                        align={'center'
                                        hideOnSinglePage
                                        total={searchResult.totals
                                        showSizeChanger={true
                                        onChange={paginationOnChange
                                    />
                                    : null
                            </div>
                        ) : null
                    </Col>
                </Row>
            </>
    );


export default TextbookReader
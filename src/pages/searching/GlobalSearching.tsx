import {DatePicker, message, Radio, Row, Col} from "antd";
import Search from "antd/es/input/Search";
import React, {useEffect, useState} from "react";
import {BookSearchResult, CardSearchResult, PredictCardOrBookParams, PredictResult} from "../../interface/searching.ts";
import {invokeGlobalSearchBooks, invokeGlobalSearchCards} from "../../invoker/searching.ts";
import CardSearchResultList from "./components/CardSearchResultList.tsx";
import BookSearchResultList from "./components/BookSearchResultList.tsx";
import {AssembleVo} from "../../interface/assemble.ts";
import {getAssemblesByType} from "../../invoker/assemble.ts";
import {
    invokeFilterAuthors, invokeFilterBooks,
    invokeFilterBookTypes,
    invokeFilterGrades,
    invokeFilterSeries,
    invokeFilterSubjects,
    invokeFilterVersions
} from "../../invoker/filter-data/BookFilter.ts";
import {BookFilterParams, BookInfo, Subject} from "../../interface/filter-data/BookFilter.ts";
import {openOrSwitchWindow} from "../../utils/Book.ts";
import "./GlobalSearching.css";
import {getFeFileUrlPrefix} from "../../invoker/file-url.ts";
import {buildFileUrl, buildImgSrc} from "../../utils/fileUrl.ts";
import PlagiarismFloatButton from "./components/PlagiarismFloatButton.tsx";
import { PlagiarismProvider } from "../../context/PlagiarismContext.tsx";

const {RangePicker} = DatePicker;

const globalBookTypes = [
    '报纸', '初高中教材', '专业教材', '港澳台教材', '小学教材', '讲话', '政府公告', '统计年鉴', '期刊杂志', '课程标准', '文献古籍', '评价体系', '名著', '教辅', '教师用书',
];
const bookTypeBlackSet = new Set(['字典', '词典', '地图册', '法律法规']);

function handleBookTypes(tempTypes: string[]) {
    const typeSet = new Set(tempTypes);
    const important: string[] = [];
    const others: string[] = [];
    for (const globalBookType of globalBookTypes) {
        if (typeSet.has(globalBookType)) {
            important.push(globalBookType);
        
    
    const globalTypeSet = new Set(globalBookTypes);
    for (const tempType of tempTypes) {
        if (!globalTypeSet.has(tempType)) {
            others.push(tempType);
        
    
    let newTypes = [...important, ...others];
    newTypes = newTypes.filter(t => !bookTypeBlackSet.has(t));
    return newTypes;


// 设置最大显示的数量
const maxVisible = 12;

function canShowSearchType(t: string | null): boolean {
    return true;


function canShowBookResult(t: string | null): boolean {
    return true;


function canOpenBookReader(t: string | null): boolean {
    return true;


function shouldShowAssembles(t: string | null): boolean {
    return t === '报纸' || t === '期刊杂志' || t === '课程标准'
        || t === '文献古籍' || t === '评价体系' || t === '名著'
        || t === '教辅' || t === '教师用书' || t === '统计年鉴';


function shouldShowSubjects(t: string | null): boolean {
    return t === '初高中教材' || t === '专业教材' || t === '港澳台教材'
        || t == '小学教材' || t === '课程标准' || t === '文献古籍'
        || t === '评价体系' || t === '教辅' || t === '教师用书';


function shouldShowVersions(t: string | null): boolean {
    return t === '初高中教材' || t === '专业教材' || t === '港澳台教材'
        || t === '小学教材' || t === '四库全书';


function shouldShowGrades(t: string | null, tempPeriod: number | null): boolean {
    return (t === '初高中教材' && tempPeriod !== 1) || t == '小学教材';


function shouldShowSeriesList(t: string | null, p: number | null): boolean {
    return (t === '初高中教材' && (p === 1 || p === null)) || t === '四库全书';


function shouldShowAuthors(t: string | null): boolean {
    return t === '专业教材' || t === '四库全书';


function shouldShowPublishYears(t: string | null): boolean {
    return t === '讲话' || t === '政府公告';
    // return t === '讲话' || t === '统计年鉴';


function shouldShowBookInfos(t: string | null): boolean {
    return t === '四库全书';


function GlobalSearch() {
    const [bookTypes, setBookTypes] = useState<string[]>([]);
    const [assembles, setAssembles] = useState<AssembleVo[]>([]);
    const [subjects, setSubjects] = useState<Subject[]>([]);
    const [versions, setVersions] = useState<string[]>([]);
    const [grades, setGrades] = useState<string[]>([]);
    const [seriesList, setSeriesList] = useState<string[]>([]);
    const [authors, setAuthors] = useState<string[]>([]);
    const [bookInfos, setBookInfos] = useState<BookInfo[]>([]);

    const [searchBtnLoading, setSearchBtnLoading] = useState<boolean>(false);
    const [pageNoCardRst, setPageNoCardRst] = useState<number>(0);
    const [pageSizeCardRst, setPageSizeCardRst] = useState<number>(10);
    const [pageNoBookRst, setPageNoBookRst] = useState<number>(0);
    const [pageSizeBookRst, setPageSizeBookRst] = useState<number>(10);
    const [searchText, setSearchText] = useState<string>("");
    const [bookType, setBookType] = useState<string | null>(null);
    const [searchType, setSearchType] = useState<number>(0);
    const [assembleId, setAssembleId] = useState<string | null>(null);
    const [earliestPublishDate, setEarliestPublishDate] = useState<string | null>(null);
    const [recentPublishDate, setRecentPublishDate] = useState<string | null>(null);
    const [period, setPeriod] = useState<number | null>(null);
    const [selectSeries, setSelectSeries] = useState<string | null>(null);
    const [subjectCode, setSubjectCode] = useState<string | null>(null);
    const [selectVersion, setSelectVersion] = useState<string | null>(null);
    const [selectGrade, setSelectGrade] = useState<string | null>(null);
    const [selectAuthor, setSelectAuthor] = useState<string | null>(null);
    const [publishYear, setPublishYear] = useState<number | null>(null);
    const [selectBookId, setSelectBookId] = useState<number | null>(null);
    const [
        cardSearchResult, setCardSearchResult
    ] = useState<PredictResult<CardSearchResult> | undefined>(undefined);
    const [
        bookSearchResult, setBookSearchResult
    ] = useState<PredictResult<BookSearchResult> | undefined>(undefined);
    //更多按钮的展开状态
    const [expanded, setExpanded] = useState({
        bookType: false,
        subjects: false,
        versions: false,
        grades: false,
        seriesList: false,
        authors: false,
        assembles: false,
        publishYears: false,
        bookInfos: false,
    });
    const publishYears = [2024, 2023, 2022, 2021, 2020, 2019, 2018, 2017, 2016, 2015, 2014, 2013, 2012];
    const toggleExpand = (type: 'bookType' | 'subjects' | 'versions' | 'grades' | 'seriesList' | 'authors' | 'assembles' | 'publishYears' | 'bookInfos') => {
        setExpanded(prev => ({...prev, [type]: !prev[type]}));
    };

    async function handleUpdateAssembles(tempBookType: string | null) {
        if (shouldShowAssembles(tempBookType)) {
            const tempAssembles = await getAssemblesByType(tempBookType as string);
            setAssembles([...tempAssembles]);
        } else {
            setAssembles([]);
        
    

    async function handleUpdateSubjects(tempBookType: string | null, params: BookFilterParams) {
        if (shouldShowSubjects(tempBookType)) {
            const tempSubjects = await invokeFilterSubjects(params);
            setSubjects([...tempSubjects]);
        } else {
            setSubjects([]);
        
    

    async function handleUpdateVersions(tempBookType: string | null, params: BookFilterParams) {
        if (shouldShowVersions(tempBookType)) {
            const tempVersions = await invokeFilterVersions(params);
            setVersions([...tempVersions]);
        } else {
            setVersions([]);
        
    

    async function handleUpdateGrades(tempBookType: string | null, tempPeriod: number | null, params: BookFilterParams) {
        if (shouldShowGrades(tempBookType, tempPeriod)) {
            const tempGrades = await invokeFilterGrades(params);
            setGrades([...tempGrades]);
        } else {
            setGrades([]);
        
    

    async function handleUpdateSeriesList(tempBookType: string | null, tempPeriod: number | null, params: BookFilterParams) {
        if (shouldShowSeriesList(tempBookType, tempPeriod)) {
            const tempSeriesList = await invokeFilterSeries(params);
            setSeriesList([...tempSeriesList]);
        } else {
            setSeriesList([]);
        
    

    async function handleUpdateAuthors(tempBookType: string | null, params: BookFilterParams) {
        if (shouldShowAuthors(tempBookType)) {
            const tempAuthors = await invokeFilterAuthors(params);
            setAuthors([...tempAuthors]);
        } else {
            setAuthors([]);
        
    

    async function handleUpdateBookInfos(tempBookType: string | null, params: BookFilterParams) {
        if (shouldShowBookInfos(tempBookType)) {
            const tempBookInfos = await invokeFilterBooks(params);
            setBookInfos([...tempBookInfos]);
        } else {
            setBookInfos([]);
        
    

    function onChangeSearchText(val: string) {
        setSearchText(val);
    

    useEffect(() => {
        async function useEffectAsync() {
            let tempBookTypes = await invokeFilterBookTypes({
                grade: null, period: null, subjectCode: null,
                type: null, version: null, series: null,
                author: null,
            });
            tempBookTypes = handleBookTypes(tempBookTypes);
            setBookTypes([...tempBookTypes]);
            await onClickSearchBtn();
        

        useEffectAsync().then();
    }, []);

    async function onChangeBookType(val: string | null) {
        setBookType(val);
        setPageNoCardRst(0);
        setPageNoBookRst(0);
        setAssembleId(null);
        setEarliestPublishDate(null);
        setRecentPublishDate(null);
        setSubjectCode(null);
        setPeriod(null);
        setSelectVersion(null);
        setSelectGrade(null);
        setSelectSeries(null);
        setSelectAuthor(null);
        setPublishYear(null);
        setSelectBookId(null);

        await handleUpdateAssembles(val);
        const params: BookFilterParams = {
            period: null, grade: null, subjectCode: null,
            type: val, version: null, series: null,
            author: null,
        };
        await handleUpdateSubjects(val, params);
        await handleUpdateVersions(val, params);
        await handleUpdateGrades(val, null, params);
        await handleUpdateSeriesList(val, null, params);
        await handleUpdateAuthors(val, params);
        await handleUpdateBookInfos(val, params);
        await searchFunc(
            0, pageSizeCardRst, 0,
            pageSizeBookRst, searchText, searchType, val, null,
            null, null, null,
            null, null, null,
            null, null, null,
            null,
        );
    

    async function onChangeSearchType(val: number) {
        setSearchType(val);
        setPageNoCardRst(0);
        setPageNoBookRst(0);
        await searchFunc(
            0, pageSizeCardRst, 0,
            pageSizeCardRst, searchText, val, bookType, null,
            assembleId, earliestPublishDate, recentPublishDate,
            period, selectVersion, selectGrade,
            selectSeries, selectAuthor, publishYear,
            selectBookId,
        );
    

    async function onChangeAssembleId(val: string | null) {
        console.log("onChangeAssembleId", val);
        setAssembleId(val);
        setPageNoCardRst(0);
        setPageNoBookRst(0);
        await searchFunc(
            0, pageSizeCardRst, 0,
            pageSizeBookRst, searchText, searchType, bookType, null,
            val, earliestPublishDate, recentPublishDate,
            period, selectVersion, selectGrade,
            selectSeries, selectAuthor, publishYear,
            selectBookId,
        );
    

    async function searchFunc(
        tempPageNoCardRst: number, tempPageSizeCardRst: number,
        tempPageNoBookRst: number, tempPageSizeBookRst: number,
        tempSearchText: string, tempSearchType: number,
        tempBookType: string | null, tempSubjectCode: string | null,
        tempAssembleId: string | null, tempEarliestPublishDate: string | null,
        tempRecentPublishDate: string | null, tempPeriod: number | null,
        tempVersion: string | null, tempGrade: string | null,
        tempSeries: string | null, tempAuthor: string | null,
        tempPublishYear: number | null, tempBookId: number | null,
    ) {
        setSearchBtnLoading(true);
        try {
            const params: PredictCardOrBookParams = {
                assembleId: tempAssembleId,
                author: tempAuthor,
                bookId: tempBookId,
                brand: null,
                publishYear: tempPublishYear,
                earliestPublishDate: tempEarliestPublishDate,
                edition: null,
                period: tempPeriod,
                grade: tempGrade,
                pageNo: 0,
                pageSize: null,
                recentPublishDate: tempRecentPublishDate,
                searchText: tempSearchText,
                series: tempSeries,
                subjectCode: tempSubjectCode,
                type: tempBookType,
                version: tempVersion,
            };
            console.log("params", params);
            if (tempSearchType !== 0) {
                setCardSearchResult(undefined);
                setBookSearchResult(undefined);
            
            const urlPrefix = await getFeFileUrlPrefix();
            if (tempSearchType === 0 || tempSearchType === 1) {
                params.pageNo = tempPageNoCardRst;
                params.pageSize = tempPageSizeCardRst;
                const result = await invokeGlobalSearchCards(params);
                // 处理文章内容中图片的url
                for (const it of result.list) {
                    it.content = buildImgSrc(urlPrefix, it.content);
                
                if (cardSearchResult === undefined) {
                    setCardSearchResult({...result});
                } else {
                    setCardSearchResult({
                        ...cardSearchResult,
                        ...result,
                    });
                
            
            if (tempSearchType === 0 || tempSearchType === 2) {
                params.pageNo = tempPageNoBookRst;
                params.pageSize = tempPageSizeBookRst;
                const result = await invokeGlobalSearchBooks(params);
                // 处理书本封面图片的url
                for (const it of result.list) {
                    it.coverImageUrl = buildFileUrl(urlPrefix, it.coverImageUrl);
                
                if (bookSearchResult === undefined) {
                    setBookSearchResult({...result});
                } else {
                    setBookSearchResult({
                        ...bookSearchResult,
                        ...result,
                    });
                
            

        } catch (e: any) {
            message.error(`搜索出错：${e.msg}`);
        } finally {
            setSearchBtnLoading(false);
        
    

    async function onClickSearchBtn() {
        setPageNoCardRst(0);
        setPageNoBookRst(0);
        await searchFunc(
            0, pageSizeCardRst, 0,
            pageSizeBookRst, searchText, searchType, bookType, null,
            assembleId, earliestPublishDate, recentPublishDate,
            period, selectVersion, selectGrade,
            selectSeries, selectAuthor, publishYear,
            selectBookId,
        );
    

    async function onClearSearchText() {
        setPageNoCardRst(0);
        setPageNoBookRst(0);
        await searchFunc(
            0, pageSizeCardRst, 0,
            pageSizeBookRst, '', searchType, bookType, null,
            assembleId, earliestPublishDate, recentPublishDate,
            period, selectVersion, selectGrade,
            selectSeries, selectAuthor, publishYear,
            selectBookId,
        );
    

    async function onChangePageNoCardRst(tempPageNo: number) {
        setPageNoCardRst(tempPageNo);
        await searchFunc(
            tempPageNo, pageSizeCardRst, pageNoBookRst, pageSizeBookRst,
            searchText, searchType, bookType, null,
            assembleId, earliestPublishDate, recentPublishDate,
            period, selectVersion, selectGrade,
            selectSeries, selectAuthor, publishYear,
            selectBookId,
        );
    

    async function onChangePageSizeCardRst(tempPageSize: number) {
        setPageNoCardRst(0);
        setPageSizeCardRst(tempPageSize);
        await searchFunc(
            0, tempPageSize, pageNoBookRst, pageSizeBookRst,
            searchText, searchType, bookType, null,
            assembleId, earliestPublishDate, recentPublishDate,
            period, selectVersion, selectGrade,
            selectSeries, selectAuthor, publishYear,
            selectBookId,
        );
    

    async function onChangePageNoBookRst(tempPageNo: number) {
        setPageNoBookRst(tempPageNo);
        await searchFunc(
            pageNoCardRst, pageSizeCardRst, tempPageNo, pageSizeBookRst,
            searchText, searchType, bookType, subjectCode,
            assembleId, earliestPublishDate, recentPublishDate,
            period, selectVersion, selectGrade,
            selectSeries, selectAuthor, publishYear,
            selectBookId,
        );
    

    async function onChangePageSizeBookRst(tempPageSize: number) {
        setPageNoBookRst(0);
        setPageSizeBookRst(tempPageSize);
        await searchFunc(
            pageNoCardRst, pageSizeCardRst, 0, tempPageSize,
            searchText, searchType, bookType, subjectCode,
            assembleId, earliestPublishDate, recentPublishDate,
            period, selectVersion, selectGrade,
            selectSeries, selectAuthor, publishYear,
            selectBookId,
        );
    

    async function onChangeDatePicker(tempDateRanges: [string, string]) {
        setPageNoCardRst(0);
        setPageNoBookRst(0);
        let tempEarliestPublishDate = null;
        if (tempDateRanges[0]) {
            tempEarliestPublishDate = tempDateRanges[0];
        
        let tempRecentPublishDate = null;
        if (tempDateRanges[1]) {
            tempRecentPublishDate = tempDateRanges[1];
        
        setEarliestPublishDate(tempEarliestPublishDate);
        setRecentPublishDate(tempRecentPublishDate);

        await searchFunc(
            0, pageSizeCardRst, 0, pageSizeBookRst,
            searchText, searchType, bookType, null,
            assembleId, tempEarliestPublishDate, tempRecentPublishDate,
            period, selectVersion, selectGrade,
            selectSeries, selectAuthor, publishYear,
            selectBookId,
        );
    

    async function onChangePeriod(val: number | null) {
        setPeriod(val);
        setPageNoCardRst(0);
        setPageNoBookRst(0);
        setSubjectCode(null);
        setSelectVersion(null);
        setSelectGrade(null);
        setSelectSeries(null);
        setSelectBookId(null);

        const params: BookFilterParams = {
            period: val, grade: null, subjectCode: null,
            type: bookType, version: null, series: null,
            author: selectAuthor,
        };
        await handleUpdateSubjects(bookType, params);
        await handleUpdateVersions(bookType, params);
        await handleUpdateGrades(bookType, val, params);
        await handleUpdateSeriesList(bookType, val, params);

        await searchFunc(
            0, pageSizeCardRst, 0,
            pageSizeBookRst, searchText, searchType, bookType, null,
            assembleId, earliestPublishDate, recentPublishDate,
            val, null, null,
            null, null, null,
            null,
        );
    

    async function onChangeSubjectCode(val: string | null) {
        setSubjectCode(val);
        setPageNoCardRst(0);
        setPageNoBookRst(0);
        setSelectVersion(null);
        setSelectGrade(null);
        setSelectSeries(null);
        setSelectAuthor(null);
        setSelectBookId(null);

        const params: BookFilterParams = {
            grade: null, period: period, subjectCode: val,
            type: bookType, version: null, series: null,
            author: null,
        };
        await handleUpdateVersions(bookType, params);
        await handleUpdateGrades(bookType, period, params);
        await handleUpdateSeriesList(bookType, period, params);
        await handleUpdateAuthors(bookType, params);

        await searchFunc(
            0, pageSizeCardRst, 0,
            pageSizeBookRst, searchText, searchType, bookType, val,
            assembleId, earliestPublishDate, recentPublishDate,
            period, null, null,
            null, null, null,
            null,
        );
    

    async function onChangeVersion(val: string | null) {
        setSelectVersion(val);
        setPageNoCardRst(0);
        setPageNoBookRst(0);
        setSelectGrade(null);
        setSelectSeries(null);
        setSelectAuthor(null);
        setSelectBookId(null);

        const params: BookFilterParams = {
            grade: null, period: period, subjectCode: subjectCode,
            type: bookType, version: val, series: null,
            author: null,
        };
        await handleUpdateGrades(bookType, period, params);
        await handleUpdateSeriesList(bookType, period, params);
        await handleUpdateAuthors(bookType, params);
        await handleUpdateBookInfos(bookType, params);

        await searchFunc(
            0, pageSizeCardRst, 0,
            pageSizeBookRst, searchText, searchType, bookType, subjectCode,
            assembleId, earliestPublishDate, recentPublishDate,
            period, val, null,
            null, null, null,
            null,
        );
    

    async function onChangeGrade(val: string | null) {
        setSelectGrade(val);
        setPageNoCardRst(0);
        setPageNoBookRst(0);

        await searchFunc(
            0, pageSizeCardRst, 0,
            pageSizeBookRst, searchText, searchType, bookType, subjectCode,
            assembleId, earliestPublishDate, recentPublishDate,
            period, selectVersion, val,
            selectSeries, selectAuthor, publishYear,
            selectBookId,
        );
    

    async function onChangeSeries(val: string | null) {
        setSelectSeries(val);
        setPageNoCardRst(0);
        setPageNoBookRst(0);
        setSelectAuthor(null);
        setSelectBookId(null);

        const params: BookFilterParams = {
            grade: selectGrade, period: period, subjectCode: subjectCode,
            type: bookType, version: selectVersion, series: val, author: null,
        };
        await handleUpdateBookInfos(bookType, params);

        await searchFunc(
            0, pageSizeCardRst, 0,
            pageSizeBookRst, searchText, searchType, bookType, subjectCode,
            assembleId, earliestPublishDate, recentPublishDate,
            period, selectVersion, selectGrade,
            val, null, publishYear,
            null,
        );
    

    async function onChangeAuthor(val: string | null) {
        setSelectAuthor(val);
        setPageNoCardRst(0);
        setPageNoBookRst(0);
        setSelectBookId(null);

        const params: BookFilterParams = {
            grade: selectGrade, period: period,
            subjectCode: subjectCode, type: bookType,
            version: selectVersion, series: selectSeries,
            author: val
        };
        await handleUpdateBookInfos(bookType, params);

        await searchFunc(
            0, pageSizeCardRst, 0,
            pageSizeBookRst, searchText, searchType, bookType, subjectCode,
            assembleId, earliestPublishDate, recentPublishDate,
            period, selectVersion, selectGrade,
            selectSeries, val, publishYear,
            null,
        );
    

    async function onChangePublishYear(val: number | null) {
        setPublishYear(val);
        setPageNoCardRst(0);
        setPageNoBookRst(0);

        await searchFunc(
            0, pageSizeCardRst, 0,
            pageSizeBookRst, searchText, searchType, bookType, subjectCode,
            assembleId, earliestPublishDate, recentPublishDate,
            period, selectVersion, selectGrade,
            selectSeries, selectAuthor, val,
            selectBookId,
        );
    

    async function onChangeBookId(val: number | null) {
        setSelectBookId(val);
        setPageNoCardRst(0);
        setPageNoBookRst(0);

        await searchFunc(
            0, pageSizeCardRst, 0,
            pageSizeBookRst, searchText, searchType, bookType, subjectCode,
            assembleId, earliestPublishDate, recentPublishDate,
            period, selectVersion, selectGrade,
            selectSeries, selectAuthor, publishYear,
            val,
        );
    

    function openBookReader(bookType: string, bookId: string, catalogueId?: string, pageNo?: number, id?: string) {
        if (canOpenBookReader(bookType)) {
            pageNo ? openOrSwitchWindow('textbook_' + bookId, bookId, bookType, pageNo, id)
                : openOrSwitchWindow('textbook_' + bookId, bookId, bookType)
        
    

    return (
        <PlagiarismProvider>
            <div>
                    <div style={{display: 'flex', justifyContent: 'center'}}>
                        <Search
                            value={searchText
                            placeholder="请输入搜索内容"
                            enterButton="搜索"
                            size="middle"
                            loading={searchBtnLoading
                            allowClear
                            onChange={(e) => onChangeSearchText(e.target.value)
                            onClear={onClearSearchText
                            onSearch={onClickSearchBtn
                            style={{maxWidth: '1000px'}
                        />
                    </div>
            <div style={{display: 'flex', justifyContent: 'center'}}>
                <div style={{width: '100%', maxWidth: '1000px'}}>
                    <div className={'flex-container'}>
                        <span className={'right-align-text'}>资料类型：</span>
                        <Radio.Group onChange={e => onChangeBookType(e.target.value)} value={bookType}>
                            <Row>
                                <Col>
                                    <Radio value={null}><span className={'ellipsis-text'}>不限</span></Radio>
                                </Col>
                                {bookTypes.slice(0, expanded.bookType ? bookTypes.length : maxVisible).map(bt => (
                                    <Col key={bt}>
                                        <Radio value={bt}>
                                            <span className={'ellipsis-text'} title={bt}>{bt}</span>
                                        </Radio>
                                    </Col>
                                ))
                                {/* 如果显示的选项小于总数，则显示“更多”按钮 */
                                {bookTypes.length > maxVisible && (
                                    <Col className={'ellipsis-text'}>
                                        <span onClick={() => toggleExpand('bookType')} className={'show-more'}>
                                            {expanded.bookType ? '收起' : '更多'
                                        </span>
                                    </Col>
                                )
                            </Row>
                        </Radio.Group>

                    </div>

                    {canShowSearchType(bookType) && <>
                        <div className={'flex-container'}>
                            <span className={'right-align-text'}>搜索类型：</span>
                            <Radio.Group onChange={e => onChangeSearchType(e.target.value)} value={searchType}>
                                <Row>
                                    <Col><Radio value={0}><span className={'ellipsis-text'
                                                                title={'不限'}>不限</span></Radio></Col>
                                    <Col><Radio value={1}><span className={'ellipsis-text'
                                                                title={'搜文章'}>搜文章</span></Radio></Col>
                                    <Col><Radio value={2}><span className={'ellipsis-text'
                                                                title={'搜书名'}>搜书名</span></Radio></Col>
                                </Row>
                            </Radio.Group>
                        </div>
                    </>

                    {bookType === '初高中教材' && <>
                        <div className={'flex-container'}>
                            <span className={'right-align-text'}>学段：</span>
                            <Radio.Group onChange={e => onChangePeriod(e.target.value)} value={period}>
                                <Row>
                                    <Col><Radio value={null}><span className={'ellipsis-text'}>不限</span></Radio></Col>
                                    <Col><Radio value={2}><span className={'ellipsis-text'}title={'初中'}>初中</span></Radio></Col>
                                    <Col><Radio value={3}><span className={'ellipsis-text'
                                                                title={'初中（五四制）'}>初中（五四制）</span></Radio></Col>
                                    <Col><Radio value={1}><span className={'ellipsis-text'} title={'高中'}>高中</span></Radio></Col>
                                </Row>
                            </Radio.Group>
                        </div>
                    </>
                    {shouldShowSubjects(bookType) && <>
                        <div className={'flex-container'}>
                            <span className={'right-align-text'}>学科：</span>
                            <Radio.Group onChange={e => onChangeSubjectCode(e.target.value)} value={subjectCode}>
                                <Row>
                                    <Col><Radio value={null}><span className={'ellipsis-text'}>不限</span></Radio></Col>
                                    {subjects.slice(0, expanded.subjects ? subjects.length : maxVisible).map(s =>
                                        <Col key={s.code}>
                                            <Radio value={s.code}>
                                                <span className={'ellipsis-text'} title={s.name}>{s.name}</span>
                                            </Radio>
                                        </Col>
                                    )
                                    {subjects.length > maxVisible && (
                                        <Col className={'ellipsis-text'}>
                                            <span onClick={() => toggleExpand('subjects')} className={'show-more'}>
                                                {expanded.subjects ? '收起' : '更多'
                                            </span>
                                        </Col>
                                    )
                                </Row>
                            </Radio.Group>
                        </div>
                    </>
                    {shouldShowVersions(bookType) && <>
                        <div className={'flex-container'}>
                            <span className={'right-align-text'}>版本：</span>
                            <Radio.Group onChange={e => onChangeVersion(e.target.value)} value={selectVersion}>
                                <Row>
                                    <Col><Radio value={null}><span
                                        className={'ellipsis-text'}>不限</span></Radio></Col>
                                    {versions.slice(0, expanded.versions ? versions.length : maxVisible).map(v =>
                                        <Col key={v}>
                                            <Radio value={v}>
                                                <span className={'ellipsis-text'} title={v}>{v}</span>
                                            </Radio>
                                        </Col>
                                    )
                                    {versions.length > maxVisible && (
                                        <Col className={'ellipsis-text'}>
                                            <span onClick={() => toggleExpand('versions')} className={'show-more'}>
                                                {expanded.versions ? '收起' : '更多'
                                            </span>
                                        </Col>
                                    )
                                </Row>
                            </Radio.Group>
                        </div>
                    </>
                    {shouldShowGrades(bookType, period) && <>
                        <div className={'flex-container'}>
                            <span className={'right-align-text'}>年级：</span>
                            <Radio.Group onChange={e => onChangeGrade(e.target.value)} value={selectGrade}>
                                <Row>
                                    <Col><Radio value={null}><span className={'ellipsis-text'}>不限</span></Radio></Col>
                                    {grades.slice(0, expanded.grades ? grades.length : maxVisible).map(g =>
                                        <Col key={g}>
                                            <Radio value={g}>
                                                <span className={'ellipsis-text'} title={g}>{g}</span>
                                            </Radio>
                                        </Col>
                                    )
                                    {grades.length > maxVisible && (
                                        <Col className={'ellipsis-text'}>
                                            <span onClick={() => toggleExpand('grades')} className={'show-more'}>
                                                {expanded.grades ? '收起' : '更多'
                                            </span>
                                        </Col>
                                    )
                                </Row>
                            </Radio.Group>
                        </div>
                    </>
                    {shouldShowSeriesList(bookType, period) && <>
                        <div className={'flex-container'}>
                            <span className={'right-align-text'}>系列：</span>
                            <Radio.Group onChange={e => onChangeSeries(e.target.value)} value={selectSeries}>
                                <Row>
                                    <Col><Radio value={null}><span className={'ellipsis-text'}>不限</span></Radio></Col>
                                    {seriesList.slice(0, expanded.seriesList ? seriesList.length : maxVisible).map(s =>
                                        <Col key={s}>
                                            <Radio>
                                                <span className={'ellipsis-text'} title={s}>{s}</span>
                                            </Radio>
                                        </Col>
                                    )
                                    {seriesList.length > maxVisible && (
                                        <Col className={'ellipsis-text'}>
                                            <span onClick={() => toggleExpand('seriesList')} className={'show-more'}>
                                                {expanded.seriesList ? '收起' : '更多'
                                            </span>
                                        </Col>
                                    )
                                </Row>
                            </Radio.Group>
                        </div>
                    </>
                    {shouldShowAuthors(bookType) && <>
                        <div className={'flex-container'}>
                            <span className={'right-align-text'}>编著：</span>
                            <Radio.Group onChange={e => onChangeAuthor(e.target.value)} value={selectAuthor}>
                                <Row>
                                    <Col><Radio value={null}><span className={'ellipsis-text'}>不限</span></Radio></Col>
                                    {authors.slice(0, expanded.authors ? authors.length : maxVisible).map(a =>
                                        <Col key={a}>
                                            <Radio value={a}>
                                                <span className={'ellipsis-text'} title={a}>{a}</span>
                                            </Radio>
                                        </Col>
                                    )
                                    {authors.length > maxVisible && (
                                        <Col className={'ellipsis-text'}>
                                            <span onClick={() => toggleExpand('authors')} className={'show-more'}>
                                                {expanded.authors ? '收起' : '更多'
                                            </span>
                                        </Col>
                                    )
                                </Row>
                            </Radio.Group>
                        </div>
                    </>
                    {shouldShowBookInfos(bookType) && <>
                        <div className={'flex-container'}>
                            <span className={'right-align-text'}>书名：</span>
                            <Radio.Group onChange={e => onChangeBookId(e.target.value)} value={selectBookId}>
                                <Row>
                                    <Col><Radio value={null}><span className={'ellipsis-text'}>不限</span></Radio></Col>
                                    {bookInfos.slice(0, expanded.bookInfos ? bookInfos.length : maxVisible).map(bi =>
                                        <Col key={bi.bookId}>
                                            <Radio value={bi.bookId}>
                                                <span className={'ellipsis-text'
                                                      title={bi.bookName}>{bi.bookName}</span>
                                            </Radio>
                                        </Col>
                                    )
                                    {bookInfos.length > maxVisible && (
                                        <Col className={'ellipsis-text'}>
                                            <span onClick={() => toggleExpand('bookInfos')} className={'show-more'}>
                                                {expanded.bookInfos ? '收起' : '更多'
                                            </span>
                                        </Col>
                                    )
                                </Row>
                            </Radio.Group>
                        </div>
                    </>
                    {shouldShowAssembles(bookType) && <>
                        <div className={'flex-container'}>
                            <span className={'right-align-text'}>品牌：</span>
                            <Radio.Group onChange={e => onChangeAssembleId(e.target.value)} value={assembleId}>
                                <Row>
                                    <Col><Radio value={null}><span className={'ellipsis-text'}>不限</span></Radio></Col>
                                    {assembles.slice(0, expanded.assembles ? assembles.length : maxVisible).map(asm =>
                                        <Col key={asm.assembleId}>
                                            <Radio value={asm.assembleId}>
                                                <span className={'ellipsis-text'} title={asm.name}>{asm.name}</span>
                                            </Radio>
                                        </Col>
                                    )
                                    {assembles.length > maxVisible && (
                                        <Col className={'ellipsis-text'}>
                                            <span onClick={() => toggleExpand('assembles')} className={'show-more'}>
                                                {expanded.assembles ? '收起' : '更多'
                                            </span>
                                        </Col>
                                    )
                                </Row>
                            </Radio.Group>
                        </div>
                    </>
                    {shouldShowPublishYears(bookType) && <>
                        <div className={'flex-container'}>
                            <span className={'right-align-text'}>时间筛选：</span>
                            <Radio.Group onChange={e => onChangePublishYear(e.target.value)} value={publishYear}>
                                <Row>
                                    <Col><Radio value={null}><span className={'ellipsis-text'}>不限</span></Radio></Col>
                                    {publishYears.slice(0, expanded.publishYears ? publishYears.length : maxVisible).map(dates =>
                                        <Col key={dates}><Radio value={dates}>
                                            <span className={'ellipsis-text'}>{dates}</span>
                                        </Radio>
                                        </Col>
                                    )
                                    {publishYears.length > maxVisible && (
                                        <Col className={'ellipsis-text'}>
                                            <span onClick={() => toggleExpand('publishYears')} className={'show-more'}>
                                                {expanded.subjects ? '收起' : '更多'
                                            </span>
                                        </Col>
                                    )
                                </Row>
                            </Radio.Group>
                        </div>
                    </>
                    {bookType === '统计年鉴' && <>
                        <div className={'flex-container'}>
                            <span className={'right-align-text'}>时间筛选：</span>
                            <Radio.Group onChange={e => onChangePublishYear(e.target.value)} value={publishYear}>
                                <Row>
                                    <Col><Radio value={null}><span className={'ellipsis-text'}>不限</span></Radio></Col>
                                    <Col><Radio value={2024}><span className={'ellipsis-text'}>2024</span></Radio></Col>
                                    <Col><Radio value={2023}><span className={'ellipsis-text'}>2023</span></Radio></Col>
                                    <Col><Radio value={2022}><span className={'ellipsis-text'}>2022</span></Radio></Col>
                                    <Col><Radio value={2021}><span className={'ellipsis-text'}>2021</span></Radio></Col>
                                    <Col><Radio value={2020}><span className={'ellipsis-text'}>2020</span></Radio></Col>
                                </Row>
                            </Radio.Group>
                        </div>
                    </>
                    {bookType === '报纸' && <>
                        <div className={'flex-container'}>
                            <span className={'right-align-text'}>日期：</span>
                            <RangePicker onChange={(_d, ds) => onChangeDatePicker(ds)}/>
                        </div>
                    </>
                </div>
            </div>
        </div>
        <div style={{marginTop: 10}}>
            {searchBtnLoading && (
                <h1>加载中....</h1>
            )
            {!searchBtnLoading && <>
                {(searchType === 0 || searchType === 2) && canShowBookResult(bookType) && <>
                    <div style={{display: 'flex', justifyContent: 'center'}}>
                        <BookSearchResultList
                            result={bookSearchResult
                            onChangePageNo={onChangePageNoBookRst
                            onChangePageSize={onChangePageSizeBookRst
                            onClickResultItem={openBookReader
                            style={{width: '100%', maxWidth: '1100px'}
                        />
                    </div>
                </>
                {(searchType === 0 || searchType === 1) && <>
                    <div style={{display: 'flex', justifyContent: 'center', marginTop: 10}}>
                        <CardSearchResultList
                            result={cardSearchResult
                            onChangePageNo={onChangePageNoCardRst
                            onChangePageSize={onChangePageSizeCardRst
                            onClickResultItem={openBookReader
                            style={{width: '100%', maxWidth: '1100px'}
                        />
                    </div>
                </>
            </>
            </div>
            <PlagiarismFloatButton />
        </PlagiarismProvider>
    );


export default GlobalSearch;
import {Card, List, Pagination} from "antd";
import {PredictResult, CardSearchResult} from "../../../interface/searching.ts";
import React, {CSSProperties} from "react";
import {processContent} from "../../../utils/text.ts";
import Mathdown from "../../mathdown/Mathdown.tsx";
import {highlight_text} from "../../../utils/text.ts";

interface CardSearchResultProps {
    style?: CSSProperties | undefined;
    result?: PredictResult<CardSearchResult>;
    onChangePageNo?: (pageNo: number) => void;
    onChangePageSize?: (pageSize: number) => void;
    onClickResultItem?: (bookType: string, bookId: string, catalogueId: string, pageNo: number, id: string) => void;


function CardSearchResultList(props: CardSearchResultProps) {

    function onChangePagination(pageNo: number, pageSize: number) {
        pageNo -= 1;
        if (props.onChangePageNo && pageNo !== props.result?.pageNo) {
            props.onChangePageNo(pageNo);
        
        if (props.onChangePageSize && pageSize !== props.result?.pageSize) {
            props.onChangePageSize(pageSize);
        
    

    function onClickResultItem(bookType: string, bookId: string, catalogueId: string, pageNo: number, id: string) {
        if (props.onClickResultItem !== undefined) {
            props.onClickResultItem(bookType, bookId, catalogueId, pageNo, id);
        
    


    return <>
        {props.result !== undefined && <>
            <List
                size="small"
                style={props.style
                bordered
                dataSource={props.result.list
                header={<div>
                    文章搜索结果（共{props.result.totals}条
                    {props.result.totals === 0 && <>）</>
                    {props.result.totals > 0 && <>
                        ，查询索引耗时：{props.result.retrieval_duration}ms，
                        总耗时：{props.result.total_duration}ms）
                    </>
                </div>
                renderItem={(item) => {
                    // 处理内容：跳过图片，只保留一张
                    const simpleContent = (content: string) => {
                        return processContent(content);
                    };

                    return (
                        <div style={{ display: 'flex', justifyContent: 'center'}}>
                            <Card
                                style={{marginTop:5,width:'98%'}
                                hoverable
                                onClick={() => onClickResultItem(item.type, item.bookId, item.catalogueId, item.page, item.id)}>
                                <div style={{width: '100%'}}>
                                    <div style={{textAlign: 'right', fontSize: '11px'}}>
                                        《{item.bookName}》
                                        {item.type === '报纸' && item.catalogueName
                                        {item.type !== '报纸' && <>P{item.page}</>
                                    </div>
                                    <div>
                                        <p style={{fontWeight: 'bold', lineHeight: '2px', fontSize: '18px',textAlign:"center"}}>
                                            {item.type === '报纸' && item.title
                                            {item.type !== '报纸' && item.cataloguePathName
                                        </p>
                                    </div>
                                    <div>
                                        <Mathdown
                                            content={highlight_text(simpleContent(item.content), props?.result?.highlights)
                                        />
                                    </div>
                                </div>
                            </Card>
                        </div>

                    );
                }
                footer={<div>
                    {props.result.totals > 0 && <>
                        <Pagination
                            align={'center'
                            showQuickJumper
                            pageSize={props?.result.pageSize
                            current={props?.result.pageNo + 1
                            defaultCurrent={1
                            total={props?.result.totals
                            onChange={onChangePagination
                        />
                    </>
                </div>
            />
        </>
    </>


export default CardSearchResultList;
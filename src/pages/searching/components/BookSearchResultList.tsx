import {Card, List, Pagination, Button, message} from "antd";
import {PredictResult, BookSearchResult} from "../../../interface/searching.ts";
import React, {CSSProperties} from "react";
import { PlusOutlined, CheckOutlined } from '@ant-design/icons';
import { usePlagiarism } from '../../../context/PlagiarismContext.tsx';

const { Meta } = Card;

interface CardSearchResultProps {
    style?: CSSProperties | undefined;
    result?: PredictResult<BookSearchResult>;
    onChangePageNo?: (pageNo: number) => void;
    onChangePageSize?: (pageSize: number) => void;
    onClickResultItem?: (bookType: string, bookId: string) => void;


function BookSearchResultList(props: CardSearchResultProps) {
    const { addBook, isBookSelected } = usePlagiarism();

    function onChangePagination(pageNo: number, pageSize: number) {
        pageNo -= 1;
        if (props.onChangePageNo && pageNo !== props.result?.pageNo) {
            props.onChangePageNo(pageNo);
        
        if (props.onChangePageSize && pageSize !== props.result?.pageSize) {
            props.onChangePageSize(pageSize);
        
    

    function onClickResultItem(bookType: string, bookId: string) {
        if (props.onClickResultItem !== undefined) {
            props.onClickResultItem(bookType, bookId);
        
    

    function handleAddToPlagiarism(book: BookSearchResult, event: React.MouseEvent) {
        event.stopPropagation(); // 阻止事件冒泡，避免触发卡片点击
        addBook(book);
        message.success(`《${book.bookName}》已加入查重列表`);
    

    return <>
        {props.result !== undefined && <>
            <List
                size="small"
                style={props.style
                bordered
                grid={{ gutter: 0, column: 5 }
                dataSource={props.result.list
                header={<div>
                    书名搜索结果（共{ props.result.totals }条
                    {props.result.totals === 0 && <>）</>
                    {props.result.totals > 0 && <>
                        ，查询索引耗时：{ props.result.retrieval_duration }ms，
                        总耗时：{ props.result.total_duration }ms）
                    </>
                </div>
                renderItem={(item) => {
                    const isSelected = isBookSelected(item.id);
                    return <List.Item onClick={() => onClickResultItem(item.type, item.id)}>
                        <Card
                            styles={{body: {padding: '5px'}}
                            style={{ backgroundColor: item.coverImageUrl ? 'white': '#f8f5f0', position: 'relative' }
                            hoverable
                            cover={item.coverImageUrl?<img alt={item.bookName} src={item.coverImageUrl} style={{ width: '100%', aspectRatio: '1/1.4' }} />:""
                            actions={[
                                <Button
                                    key="plagiarism"
                                    type={isSelected ? "default" : "primary"
                                    size="small"
                                    icon={isSelected ? <CheckOutlined /> : <PlusOutlined />
                                    onClick={(e) => handleAddToPlagiarism(item, e)
                                    disabled={isSelected
                                    style={{
                                        width: '90%',
                                        backgroundColor: isSelected ? '#f6ffed' : undefined,
                                        borderColor: isSelected ? '#b7eb8f' : undefined,
                                        color: isSelected ? '#52c41a' : undefined
                                    }
                                >
                                    {isSelected ? '已加入' : '加入查重'
                                </Button>
                            ]
                        >
                            {
                                item.coverImageUrl ? (
                                    <div style={{ fontSize: '11px', textAlign: "center", fontWeight: 550 }}>
                                        《{item.bookName}》
                                    </div>
                                ) : (
                                    <div
                                        style={{
                                            display: 'flex',
                                            flexDirection: 'column', // 垂直排列
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            fontSize: '16px',
                                            width: '100%',
                                            height: '260px',
                                            fontFamily: '"KaiTi", "STKaiti", "楷体", serif',
                                        }
                                    >
                                        {
                                            item.bookName
                                                .replace(/[(\\[（](.*?)[)\]）]/g, '.$1') // 替换 ()、（）或 [] 内的内容为 .content
                                                .replace(/\s+/g, '') // 删除所有空格
                                                .match(/(\d+|[^\d])/g) // 分割为数字或非数字字符
                                                ?.map((charOrNumber, index) => (
                                                    <div key={index}>
                                                        {charOrNumber === ' ' ? <br /> : charOrNumber
                                                    </div>
                                                ))
                                        

                                        {/* 书脊效果 */
                                        <div style={{
                                            position: 'absolute',
                                            left: '0',
                                            top: '0',
                                            height: '100%',
                                            width: '8px',
                                            backgroundColor: 'rgba(0,0,0,0.1)',
                                            borderRight: '1px solid rgba(0,0,0,0.2)'
                                        }} />
                                    </div>

                                )
                            

                        </Card>
                    </List.Item>
                }
                footer={<div>
                    {props.result.totals > 0 && <>
                        <Pagination
                            align={'center'
                            showQuickJumper
                            pageSize={props?.result.pageSize
                            current={props?.result.pageNo + 1
                            defaultCurrent={1
                            total={props?.result.totals
                            onChange={onChangePagination
                        />
                    </>
                </div>
            />
        </>
    </>


export default BookSearchResultList;
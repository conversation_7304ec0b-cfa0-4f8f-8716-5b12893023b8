import {Card, Table, TableProps} from 'antd'
import { LawModel} from './../../interface/law'
import { statusOptions } from './index.tsx'
import React, { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import {getFeFileUrlPrefix} from "../../invoker/file-url.ts";
import {buildFileUrl} from "../../utils/fileUrl.ts";
import {WebviewWindow} from "@tauri-apps/api/webviewWindow";
import {invokeGetLaw} from "../../invoker/law.ts";
import {isTauriEnv} from "../../utils/system.ts";
type TablePagination<T extends object> = NonNullable<Exclude<TableProps<T>['pagination'], boolean>>;
type TablePaginationPosition<T extends object> = NonNullable<
    TablePagination<T>['position']
>[number];

type LawContentProps = {
    searchValue?: string
    typeCode?: string
    formValues: any


const LawContent: React.FC<LawContentProps> = ({
                                                   searchValue,
                                                   typeCode,
                                                   formValues
                                               }) => {
    const [urlPrefix, setUrlPrefix] = useState<string>("");
    const [pi, setPi] = useState(1)
    const [total, setTotal] = useState(0)
    const [laws, setlaws] = useState<LawModel[]>([])
    const [bottom] = useState<TablePaginationPosition<LawModel>>('bottomCenter');
    const [searchValuesState,setSearchValuesState] = useState("");
    const [initPage,setInitPage] = useState(true);
    const query:{
        expiry: {gt:string;lt:string};
        office: string;
        publish: {gt:string;lt:string};
        status: string;
    } = {
        expiry: formValues!=undefined ? formValues.expiry!=undefined ? {
            gt: dayjs(formValues?.expiry[0]).format('YYYY-MM-DD'),
            lt: dayjs(formValues?.expiry[1]).format('YYYY-MM-DD')
        } : {gt:"",lt:""} : {gt:"",lt:""},
        office: formValues!=undefined ? formValues.office!=undefined ? formValues.office : "" :"",
        publish: formValues!=undefined ? formValues.publish!=undefined ? {
            gt: dayjs(formValues?.publish[0]).format('YYYY-MM-DD'),
            lt: dayjs(formValues?.publish[1]).format('YYYY-MM-DD')
        }  :{gt:"",lt:""} :{gt:"",lt:""} ,
        status: formValues!=undefined ? formValues.status!=undefined ? formValues.status:"" : "",
    
    const [expirygt,setExpirygt] = useState("");
    const [expirylt,setExpirylt] = useState("");
    const [office,setOffice] = useState("");
    const [publishgt,setPublishgt] = useState("");
    const [publishlt,setPublishlt] = useState("");
    const [status,setStatus] = useState("");
    const [typeCodes,setTypeCodes] = useState("all");
    const columns: TableProps<LawModel>['columns'] = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            render: (_, record, index: number) => <span>{index + 1}</span>,
            width: 60
        },
        {
            title: '标题',
            dataIndex: 'title',
            key: 'title',
            ellipsis: true,
            render: (_, record) => {
                return record.path ? (
                    <a
                        onClick={() =>
                            openFileOnNewWindow(record.lawId, record.path as string,record.title)
                        
                    >
                        {record.title
                    </a>
                ) : (
                    <span>{record.title}</span>
                )
            
        },
        {
            title: '制定机关',
            dataIndex: 'office',
            key: 'office',
            width: 280,
            ellipsis: true
        },
        {
            title: '法律性质',
            dataIndex: 'type',
            key: 'type',
            width: 160
        },
        {
            title: '时效性',
            dataIndex: 'status',
            key: 'status',
            render: (_, record) => (
                <span>
          {statusOptions.find((i) => i.value == record.status)?.label
        </span>
            ),
            width: 100
        },
        {
            title: '发布日期',
            dataIndex: 'publish',
            key: 'publish',
            width: 160,
            render: (_, record) =>
                record.publish ? (
                    <span>{`[${dayjs(record.publish).format('YYYY-MM-DD')}]`}</span>
                ) : (
                    <></>
                )
        
    ]
    async function queryLaws(page:number) {
        const result: {
            code:number;
            data:{
                list:LawModel[];
                total: number;
            };
            msg: string;
        } = await invokeGetLaw({
            currentPage:page,
            expiry: query.expiry,  // 施行日期
            office: query.office, // 制定机关
            publish: query.publish, // 发布日期
            typeCode:typeCode!=undefined ? typeCode : "all", // 分类标签
            status:query.status, //时效性
            searchValue:searchValue!=undefined ? searchValue : ""
        });
        setTotal(result.data.total)
        setlaws(result.data.list)
    

    // 在新的浏览器窗口打开文件
    const openFileOnNewWindow = async (lawId: string, path: string,title:string) => {
        const url = buildFileUrl(urlPrefix, `/cms_illustration_image/law/pdf/${path}`);
        if (isTauriEnv) {
            const webview = new WebviewWindow(`law_pdf_${lawId}`, {
                url,
            });
            webview.once("tauri://created", async function () {
                // webview window successfully created
                await webview.setTitle(title);
            });

            webview.once("tauri://error", function (e) {
                // an error occurred during webview window creation
            });
        } else {
            window.open(url, "_blank");
        
    

    useEffect(() => {
        async function useEffectAsync() {
            const tempUrlPrefix = await getFeFileUrlPrefix();
            setUrlPrefix(tempUrlPrefix);
        
        useEffectAsync().then();
    }, []);

    useEffect(() => {
        const asyncFunction = async () => {
            if (initPage) {
                setTypeCodes(typeCode!=undefined ? typeCode : "all")
                setExpirygt(query.expiry.gt);
                setExpirylt(query.expiry.lt);
                setOffice(query.office);
                setPublishgt(query.publish.gt);
                setPublishlt(query.publish.lt);
                setStatus(query.status);
                setSearchValuesState(searchValue!=undefined ? searchValue : "");
                await queryLaws(1);
                setInitPage(false);
            

            if (expirygt!=query.expiry.gt || expirylt!=query.expiry.lt) {
                setExpirygt(query.expiry.gt);
                setExpirylt(query.expiry.lt);
                await queryLaws(1);
            
            if (office!=query.office) {
                setOffice(query.office)
                await queryLaws(1);
                setPi(1);
            
            if (publishgt!=query.publish.gt || publishlt!=query.publish.lt) {
                setPublishgt(query.publish.gt)
                setPublishlt(query.publish.lt)
                await queryLaws(1);
                setPi(1);
            
            if (status!=query.status) {
                setStatus(query.status)
                await queryLaws(1);
                setPi(1);
            
            if (typeCodes!= (typeCode!=undefined ? typeCode : "all")) {
                setTypeCodes(typeCode!=undefined ? typeCode : "all")
                setPi(1);
                await queryLaws(1);
            
            if (searchValuesState!= (searchValue!=undefined ? searchValue : "")) {
                setSearchValuesState(searchValue!=undefined ? searchValue : "")
                setPi(1);
                await queryLaws(1);
            
        
        asyncFunction().then(() => {});
    }, [initPage,searchValue, pi,
        expirygt,expirylt,office,publishgt,publishlt,status,typeCodes,
        searchValuesState,laws,formValues, typeCode
    ])

    return (
        <div style={{marginBottom:-20}}>
            <Card className="w-full" bordered={false
                style={{marginTop:-20}
            >
                <Table
                    rowKey={(record) => record.lawId
                    size="large"
                    dataSource={laws
                    columns={columns
                    style={{marginTop:-10,marginBottom:-8}
                    pagination={{
                        current:pi,
                        total,
                        pageSize: 10,
                        position: [bottom],
                        async onChange(page) {
                            setPi(page)
                            await queryLaws(page);
                        
                    }
                />
            </Card>
        </div>
    )


export default LawContent

import {<PERSON><PERSON>, Card, Col, DatePicker, Empty, Form,
    Input, Radio, Row, Select, Typography} from 'antd'
import { LawModel } from './../../interface/law'
import React, {useEffect, useState} from 'react'
import LawContent from './Content'
import {useNavigate} from "react-router-dom";
import {invokeGetLawTotal} from "../../invoker/law.ts";

const { RangePicker } = DatePicker

const items = [
    {
        label: ' 全部 ',
        key: 'all'
    },
    {
        label: ' 宪 法 ',
        key: 'xffl'
    },
    {
        label: '法律法规',
        key: 'flfg'
    },
    {
        label: '行政法规',
        key: 'xzfg'
    },
    {
        label: '监察法规',
        key: 'jcfg'
    },
    {
        label: '地方性法规',
        key: 'dfxfg'
    
]

export const statusOptions = [
    {
        label: '有效',
        value: '1'
    },
    {
        label: '尚未生效',
        value: '3'
    },
    {
        label: '已修改',
        value: '5'
    },
    // {
    //   label: '',
    //   value: '7'
    // },
    {
        label: '已废止',
        value: '9'
    
]

const LawPage: React.FC = () => {
    const [searchValue, setSearchValue] = useState<string>();
    const [typeCode, setTypeCode] = useState<string>('all');
    const [formValues, setFormValues] = useState<LawModel>();
    const navigate = useNavigate();
    const [totals, setTotals] = useState(0);
    async function getLawsTotal() {
        const tempTotals = await invokeGetLawTotal();
        setTotals(tempTotals)
    
    useEffect(() => {
        const init = async () => {
            await getLawsTotal();
        
        init().then()
    }, [totals])
    const [form] = Form.useForm()
    return (
        <div className="size-full max-w-full overflow-y-auto">
            <Card
                className="w-full"
                title={
                    <div className="px-8 py-2">
                        {/* <img src="./guohui.png"></img> */
                        <span
                            className="text-3xl tracking-widest"
                            style={{ color: '#990000ff' }
                        >
                          法律法规数据库
                        </span>
                    </div>
                
            >
                <div className="px-72 text-center">
                    <Input.Search
                        placeholder="标题或内容搜索"
                        onSearch={(value) => {
                            if (value) {
                                setTypeCode('all')
                            
                            setSearchValue(value)
                        }
                    />
                </div>

                <div className="py-4 text-center"
                    style={{textAlign:"center",padding:8}
                >
                    <Radio.Group
                        buttonStyle="solid"
                        size="large"
                        value={typeCode
                        onChange={(e) => setTypeCode(e.target.value)
                    >
                        {items.map((item) => {
                            return (
                                <Radio.Button key={item.key} value={item.key}>
                                    {item.label
                                </Radio.Button>
                            )
                        })
                    </Radio.Group>
                </div>

                <div className="px-8 py-2">
                    <div className="flex w-full">
                        <div className="w-full">
                            <Form<LawModel>
                                layout="horizontal"
                                className="w-full"
                                form={form
                                onValuesChange={(changedValues, values) => {
                                    setFormValues(values)
                                }
                            >
                                <Row gutter={16}>
                                    <Col span={5}>
                                        <Form.Item name="office" label="制定机关">
                                            <Input placeholder="请输入制定机关" allowClear={true} />
                                        </Form.Item>
                                    </Col>
                                    <Col span={5}>
                                        <Form.Item name="status" label="时效性">
                                            <Select
                                                placeholder="请选择时效性"
                                                allowClear={true
                                                options={statusOptions
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span={5}>
                                        <Form.Item name="expiry" label="施行日期">
                                            <RangePicker />
                                        </Form.Item>
                                    </Col>
                                    <Col span={5}>
                                        <Form.Item name="publish" label="发布日期">
                                            <RangePicker />
                                        </Form.Item>
                                    </Col>
                                </Row>
                            </Form>
                        </div>
                    </div>
                </div>
                {totals === 0 ?
                    <Empty
                        image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg"
                        styles={{ image: { height: 60 } }
                        description={
                            <Typography.Text>
                                法律法规数据为空
                            </Typography.Text>
                        
                    >
                        <Button type="primary"
                                onClick={()=>navigate('/settings/increment-update')
                        >
                            增量更新法律法规
                        </Button>
                    </Empty>
                    :<LawContent
                        searchValue={searchValue
                        typeCode={typeCode
                        formValues={formValues
                    />
                

            </Card>
        </div>
    )


export default LawPage

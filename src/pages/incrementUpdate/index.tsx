import {<PERSON><PERSON>, Drawer, Float<PERSON><PERSON>on, message, Progress, Table, Tag} from "antd";
import {useEffect, useRef, useState} from "react";
import {open} from "@tauri-apps/plugin-dialog";
import {
    invokeDeleteIncrementRecordById,
    invokeGetIncrementResult
} from "../../invoker/increment-update.ts";
import {IncrementRecord, IncrementResultVo} from "../../interface/increment.ts";
import {listen} from "@tauri-apps/api/event";
import {IncFileUpdRecord} from "../../interface/IncFileUpd.ts";
import {SystemFeatureDetails, UsageModeVo, UserDataDirCustomConfigVo} from "../../interface/SystemFeatureInterface.ts";
import {getSystemFeatDetails, invokeGetUsageMode, setUserDataDirCustomConfig} from "../../invoker/SystemFeature.ts";
import {LoadingOutlined, WarningOutlined} from "@ant-design/icons";
import {isTauriEnv} from "../../utils/system.ts";
import {useIncFileUpload} from "../../context/IncrementUpload.tsx";

const EVENT_NAME_increment_result_changed = 'increment-result-changed';

function IncrementUpdate() {
    // 仅B/S模式下使用
    const wsClient = useRef<WebSocket | null>(null);
    const fileInputRef = useRef<HTMLInputElement | null>(null);
    const [
        incrementResult, setIncrementResult
    ] = useState<IncrementResultVo>({
        handled: 0, list: [], total: 0
    });
    const [
        fileUploadDrawerVisible,
        setFileUploadDrawerVisible
    ] = useState<boolean>(false);
    const { incFileUpdRecords, updateIncFileUpdRecords, uploadIncrementInTauriEnv, uploadIncrementInBrowser } = useIncFileUpload();
    const [usageMode, setUsageMode] = useState<UsageModeVo>({mode: 0, port: 0, url: "", warning: null});
    const [featDetails, setFeatDetails] = useState<SystemFeatureDetails>({
        userDataDirCustomConfig: {
            enable: true,
            path: "",
            warning: null
        
    });

    useEffect(() => {
        async function useEffectAsync() {
            const tempFeatureDetails = await getSystemFeatDetails();
            setFeatDetails({...tempFeatureDetails});
            if (isTauriEnv) {
                const tempUsageMode = await invokeGetUsageMode();
                setUsageMode({...tempUsageMode});
            
            await updateIncFileUpdRecords();
            const tempResult = await invokeGetIncrementResult();
            setIncrementResult({...tempResult});
        

        useEffectAsync().then();

        // 监听增量包导入记录变化
        listen_to_increment_result_changed();

        // 组件卸载时关闭连接
        return () => {
            if (wsClient.current !== null && wsClient.current.readyState === WebSocket.OPEN) {
                wsClient.current.close();
            
        };
    }, []);

    const listen_to_increment_result_changed = async () => {
        if (isTauriEnv) {
            await listen<string>(EVENT_NAME_increment_result_changed, async () => {
                const tempResult = await invokeGetIncrementResult();
                setIncrementResult({...tempResult});
            });
        } else {
            // 使用websocket监听后端
            establishWsConnectionForIncrementResultChanged();
        
    

    const handleUpdIncrement = async (inputFiles: FileList | null) => {
        let resp;
        if (isTauriEnv) {
            const paths = await open({
                multiple: true,
                filters: [{
                    name: '增量包',
                    extensions: ['zip']
                }]
            });
            if (paths === null || paths.length === 0) {
                return;
            
            resp = await uploadIncrementInTauriEnv(paths);
        } else {
            if (inputFiles === null || inputFiles.length === 0) {
                if (fileInputRef.current) {
                    fileInputRef.current.click();
                
                return;
            } else {
                resp = await uploadIncrementInBrowser(inputFiles);
            
        

        if (resp.code === 201) {
            message.error(resp.msg);
            return;
        

        const tempResult = await invokeGetIncrementResult();
        setIncrementResult({
            ...incrementResult,
            list: [...tempResult.list],
        });
        // 当是浏览器或客户端模式时，如果上传增量包就弹出文件记录抽屉
        if (!isTauriEnv || usageMode.mode === 1) {
            setFileUploadDrawerVisible(true);
        
    

    function onclickFileUpdFloatBtn() {
        setFileUploadDrawerVisible(true)
    

    function onCloseFileUpdDrawer() {
        setFileUploadDrawerVisible(false);
    

    async function onClickIncrementRecordDeleteBtn(rId: string) {
        try {
            await invokeDeleteIncrementRecordById(rId);
            message.success('删除成功');
        } catch (e: any) {
            message.error(`${e}`);
        
    

    async function onClickUserDataDirCustomBtn() {
        const selected = await open({
            multiple: false,
            directory: true,
            defaultPath: featDetails.userDataDirCustomConfig.path,
        });
        if (selected === null || selected === "") {
            return;
        

        const config: UserDataDirCustomConfigVo = {
            enable: true,
            path: selected,
            warning: null
        };
        try {
            await setUserDataDirCustomConfig(config);
            message.success('保存成功！');
            setFeatDetails({
                ...featDetails,
                userDataDirCustomConfig: {
                    ...config,
                },
            });
            const tempUsageMode = await invokeGetUsageMode();
            setUsageMode({...tempUsageMode});
            await updateIncFileUpdRecords();
            const tempResult = await invokeGetIncrementResult();
            setIncrementResult({...tempResult});
        } catch (e: any) {
            message.error(`${e.msg}`);
        
    

    function establishWsConnectionForIncrementResultChanged() {
        const wsUrl = `ws://${window.location.host}/ws-broadcast`;

        function onOpenCallback() {
            console.log('WebSocket connected');
        

        async function onMassageCallback(event: MessageEvent<any>) {
            try {
                const message: string = event.data;
                const payload = JSON.parse(message);
                if (payload.event === EVENT_NAME_increment_result_changed) {
                    const tempResult = await invokeGetIncrementResult();
                    setIncrementResult({...tempResult});
                
            } catch (err) {
                console.error('消息解析失败:', err);
            
        

        function onErrorCallback(error: Event) {
            // setError('连接错误，请检查网络或服务器状态');
            console.error('WebSocket error:', error);
        

        function onCloseCallback(event: CloseEvent) {
            console.log('WebSocket closed');
            if (!event.wasClean) {
                // 非正常关闭，尝试重连
                console.log('连接断开，尝试重新连接...');
                setTimeout(() => {
                    // 重新连接逻辑
                    if (wsClient.current !== null && wsClient.current.readyState !== WebSocket.OPEN) {
                        try {
                            wsClient.current.close();
                        } catch (e: any) {
                            //
                        } finally {
                            wsClient.current = null;
                        
                        wsClient.current = new WebSocket(wsUrl);
                        // 连接成功
                        wsClient.current.onopen = onOpenCallback;
                        // 接收消息
                        wsClient.current.onmessage = onMassageCallback;
                        // 错误处理
                        wsClient.current.onerror = onErrorCallback;
                        // 关闭处理
                        wsClient.current.onclose = onCloseCallback;
                    
                }, 5000);
            
        

        // 创建WebSocket连接
        wsClient.current = new WebSocket(wsUrl);
        // 连接成功
        wsClient.current.onopen = onOpenCallback;
        // 接收消息
        wsClient.current.onmessage = onMassageCallback;
        // 错误处理
        wsClient.current.onerror = onErrorCallback;
        // 关闭处理
        wsClient.current.onclose = onCloseCallback;
    


    return (<>
        <div>
            {/* 隐藏的input元素 */
            <input
                type="file"
                multiple
                onChange={(e) => handleUpdIncrement(e.target.files)
                accept="application/zip"
                ref={fileInputRef
                style={{ display: 'none' }} // 完全隐藏
            />
            <Button
                type="primary"
                disabled={
                    !featDetails.userDataDirCustomConfig.enable || featDetails.userDataDirCustomConfig.warning !== null
                
                onClick={() => handleUpdIncrement(null)
            >
                导入增量包
            </Button>
        </div>
        {(!featDetails.userDataDirCustomConfig.enable || featDetails.userDataDirCustomConfig.warning) && <div style={{marginTop: '5px'}}>
            <Button disabled={!isTauriEnv} size="small" type="primary" onClick={onClickUserDataDirCustomBtn}>立即设置</Button>
            <span style={{marginLeft: '10px', color: 'orange'}}>
                <WarningOutlined/>
                {!featDetails.userDataDirCustomConfig.enable && <>
                    必须设置用户数据文件夹位置，方可增量更新！请提前评估数据规模，确保文件夹下有足够存储空间。
                </>
                {featDetails.userDataDirCustomConfig.warning && <>
                    {featDetails.userDataDirCustomConfig.warning
                </>
            </span>
        </div>
        <div>总进度（完成数/总数）：{ incrementResult.handled } / { incrementResult.total }</div>
        <div>
            <Table<IncrementRecord>
                rowKey="rId"
                columns={[
                    {
                        title: '文件名',
                        dataIndex: 'fileName',
                    },
                    {
                        title: '状态',
                        dataIndex: 'status',
                        width: 100,
                        render: (s: number, rcd) => {
                            switch (s) {
                                case 0:
                                    return <Tag>排队中</Tag>
                                case 1:
                                    return <Tag icon={<LoadingOutlined />} color="geekblue">导入中</Tag>
                                case 2:
                                    return <Tag color="green">已完成</Tag>
                                default:
                                    return <Tag color="red" onClose={() => onClickIncrementRecordDeleteBtn(rcd.rId)} closable>出错</Tag>
                            
                        
                    },
                    {
                        title: '更新时间',
                        dataIndex: 'updateTime',
                        width: 300,
                    },
                    {
                        title: '备注',
                        dataIndex: 'msg',
                    
                ]
                dataSource={incrementResult.list
                size="small"
                pagination={{
                    position: ['bottomCenter'],
                    defaultPageSize: 25,
                }
            />
        </div>
        <div>
            <FloatButton
                type="primary"
                description="文件列表"
                badge={{ showZero: false, count: incFileUpdRecords.filter(r => r.status === 0 || r.status === 1).length }
                onClick={onclickFileUpdFloatBtn
                style={{ insetInlineEnd: 94, position: "absolute", right: 40, top: 90 }
            />
        </div>
        <div>
            <Drawer
                title="本地增量包文件上传至服务器列表"
                onClose={onCloseFileUpdDrawer
                open={fileUploadDrawerVisible
                width={500
            >
                <div style={{ height: '100%', overflowY: 'auto' }}>
                    <Table<IncFileUpdRecord>
                        rowKey="rId"
                        columns={[
                            {
                                title: '文件名',
                                dataIndex: 'fileName',
                            },
                            {
                                title: '进度',
                                dataIndex: 'updPercentage',
                                width: 60,
                                render: (p: number, rcd) => {
                                    return <Progress
                                        type="circle"
                                        percent={Number(p.toFixed(1))
                                        size={40
                                        status={rcd.status === 3 ? "exception" : undefined
                                    />
                                
                            },
                            {
                                title: '备注',
                                dataIndex: 'msg',
                            
                        ]
                        dataSource={incFileUpdRecords
                        size="small"
                        pagination={false
                    />
                </div>
            </Drawer>
        </div>
    </>)



export default IncrementUpdate;
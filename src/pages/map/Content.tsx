import {DownloadOutlined} from '@ant-design/icons'
import {<PERSON><PERSON>, Card, Col, Image, message, Pagination, Row} from 'antd'
import { Category, MapObjModel } from './../../interface/map.ts'
// import { saveAs } from 'file-saver'
import React, { useEffect, useState } from 'react'
import {download as downloadInPlugin} from "@tauri-apps/plugin-upload";
import {getFeFileUrlPrefix} from "../../invoker/file-url.ts";
import {buildFileUrl} from "../../utils/fileUrl.ts";
import * as PATH from '@tauri-apps/api/path';
import {copyFile, exists as existsFile, mkdir, remove as removeFile} from "@tauri-apps/plugin-fs";
import {openPath} from "@tauri-apps/plugin-opener";
import {save} from "@tauri-apps/plugin-dialog";
import {v4 as UUIDv4} from "uuid";
import {invokeGetMaps} from "../../invoker/map.ts";
import {isTauriEnv} from "../../utils/system.ts";

type MapContentProps = {
    searchValue?: string
    categorys: Category[]
    borders: string[]
    mapObj?: Pick<
        MapObjModel,
        'size' | 'scale' | 'provinceColor' | 'neighbouringCountry' | 'illustration'
    >


const MapContent: React.FC<MapContentProps> = ({
                                                   searchValue,
                                                   categorys,
                                                   borders,
                                                   mapObj
                                               }) => {
    const [pi, setPi] = useState(1)
    const [total, setTotal] = useState(0)
    const [mapObjs, setMapObjs] = useState<MapObjModel[]>([])
    const query:{
        subclass: string;
        superclass: string;
        largeclass: string;
        borderWhere: any[];
        size: string;
        scale: string;
        neighbouringCountry: number;
        provinceColor: number;
        illustration: number;
    } = {
        subclass: categorys.length>0 ? categorys[0].name : "",
        superclass: categorys.length>1 ? categorys[1].name : "",
        largeclass: categorys.length>2 ? categorys[2].name : "",
        borderWhere: borders,
        size: mapObj!=undefined ? mapObj.size!=undefined ? mapObj.size : "" : "",
        scale: mapObj!=undefined ? mapObj.scale!=undefined ? mapObj.scale : "" :"",
        neighbouringCountry: mapObj!=undefined ? mapObj.neighbouringCountry!=undefined ? mapObj.neighbouringCountry :3 : 3,
        provinceColor: mapObj!=undefined ? mapObj.provinceColor!=undefined ? mapObj.provinceColor:3 : 3,
        illustration: mapObj!=undefined ? mapObj.illustration!=undefined ? mapObj.illustration:3 : 3
    
    const [subclass,setSubclass] = useState("");
    const [superclass,setSuperclass] = useState("");
    const [largeclass,setLargeclass] = useState("");
    const [borderWhere,setBorderWhere] = useState<any[]>([]);
    const [size,setSize] = useState("");
    const [scale,setScale] = useState("");
    const [neighbouringCountry,setNeighbouringCountry] = useState(3);
    const [provinceColor,setProvinceColor] = useState(3);
    const [illustration,setIllustration] = useState(3);

    const [searchValuesState,setSearchValuesState] = useState("");
    const [initPage,setInitPage] = useState(true);
    async function queryMaps(page:number) {
        const result:{
            code:number,
            data:{
                list:MapObjModel[],
                total:number
            },
            msg:string
        } = await invokeGetMaps({
            currentPage:page,
            subclass:query.subclass,
            superclass:query.superclass,
            largeclass:query.largeclass,
            border: query.borderWhere,
            size:query.size,
            scale:query.scale,
            neighbouringCountry:query.neighbouringCountry,
            provinceColor:query.provinceColor,
            illustration:query.illustration,
            searchValue:searchValue!=undefined ? searchValue : ""
        });
        const urlPrefix = await getFeFileUrlPrefix();
        const list_:MapObjModel[] = [];
        result.data.list.forEach(e => {
            list_.push({
                border: e.border,
                browseNum: e.browseNum,
                catelogs: e.catelogs,
                content: e.content,
                countyOder: e.countyOder,
                downloadNum: e.downloadNum,
                id: e.id,
                illustration: e.illustration,
                isImg: e.isImg,
                jpgPath: buildFileUrl(urlPrefix,"cms_illustration_image/map"),
                largeclass: e.largeclass,
                mapId: e.mapId,
                mapNumber: e.mapNumber,
                mapYear: e.mapYear,
                midclass: e.midclass,
                name: e.name,
                neighbouringCountry: e.neighbouringCountry,
                orderId: e.orderId,
                path: e.path,
                provinceColor: e.provinceColor,
                scale: e.scale,
                sequenceNum: e.sequenceNum,
                size: e.size,
                smallclass: e.smallclass,
                subclass: e.subclass,
                superclass: e.superclass,
                telBrowseNum: e.telBrowseNum,
                telDownloadNum: e.telDownloadNum,
                use: e.use
            })
        })
        setTotal(result.data.total)
        setMapObjs(list_)
    
    useEffect(() => {
        const asyncFunction = async () => {
            if (initPage) {
                setSearchValuesState(searchValue!=undefined ? searchValue : "");
                setSubclass(query.subclass)
                setSuperclass(query.superclass)
                setLargeclass(query.largeclass)
                setBorderWhere(query.borderWhere)
                setSize(query.size)
                setScale(query.scale)
                setNeighbouringCountry(query.neighbouringCountry)
                setProvinceColor(query.provinceColor)
                setIllustration(query.illustration)
                await queryMaps(1);
                setInitPage(false);
            
            if (subclass!=query.subclass) {
                setSubclass(query.subclass)
                setPi(1)
                await queryMaps(1);
            
            if (superclass!=query.superclass) {
                setSuperclass(query.superclass)
                setPi(1)
                await queryMaps(1);
            
            if (largeclass!=query.largeclass) {
                setLargeclass(query.largeclass)
                setPi(1)
                await queryMaps(1);
            
            if (borderWhere!=query.borderWhere) {
                setBorderWhere(query.borderWhere)
                setPi(1)
                await queryMaps(1);
            
            if (size!=query.size) {
                setSize(query.size)
                setPi(1)
                await queryMaps(1);
            
            if (scale!=query.scale) {
                setScale(query.scale)
                setPi(1)
                await queryMaps(1);
            
            if (neighbouringCountry!=query.neighbouringCountry) {
                setNeighbouringCountry(query.neighbouringCountry)
                setPi(1)
                await queryMaps(1);
            
            if (provinceColor!=query.provinceColor) {
                setProvinceColor(query.provinceColor)
                setPi(1)
                await queryMaps(1);
            
            if (illustration!=query.illustration) {
                setIllustration(query.illustration)
                setPi(1)
                await queryMaps(1);
            
            if (searchValuesState!= (searchValue!=undefined ? searchValue : "")) {
                setSearchValuesState(searchValue!=undefined ? searchValue : "")
                setPi(1)
                await queryMaps(1);
            
        };
        asyncFunction().then(() => {});
    }, [initPage,searchValue,searchValuesState,mapObjs,
        subclass,superclass,largeclass,borderWhere,size,scale,neighbouringCountry,provinceColor,illustration,
        categorys, borders, mapObj, pi])


    const changePage = async (page:number) => {
        setPi(page)
        await queryMaps(page);
    

    const download = async (url: string, filename: string) => {
        if (!url) {
            return;
        

        if (!isTauriEnv) {
            // 浏览器环境下的文件下载处理
            try {
                const response = await fetch(url);

                if (!response.ok) {
                    if (response.status === 404) {
                        message.warning('抱歉，该文件未收集，暂无法下载');
                        return;
                    
                    throw new Error(`HTTP请求失败! 状态码: ${response.status}`);
                

                const blob = await response.blob();

                const downloadUrl = window.URL.createObjectURL(blob);
                const link = document.createElement('a');

                link.href = downloadUrl;
                link.download = filename || 'download';
                link.style.display = 'none';

                document.body.appendChild(link);
                link.click();


                document.body.removeChild(link);
                window.URL.revokeObjectURL(downloadUrl);
            } catch (error) {
                message.error(`下载失败：${error}`);
            
            return;
        

        const tauriAssetPrefixes= ["http://asset.localhost/", "asset://localhost/"];
        let isLocalFilePath = false;
        for (const tauriAssetPrefix of tauriAssetPrefixes) {
            if (url.startsWith(tauriAssetPrefix)) {
                url = url.replace(tauriAssetPrefix, "");
                url = decodeURI(url);
                isLocalFilePath = true;
                break;
            
        

        let tempPath = '';
        if (isLocalFilePath) {
            try {
                if (!await existsFile(url)) {
                    message.warning('抱歉，该文件未收集，暂无法下载');
                    return;
                
            } catch (e) {
                message.error(`${e}`);
                return;
            
        } else {
            try {
                const dlDir = await PATH.downloadDir();
                if (!await existsFile(dlDir)) {
                    await mkdir(dlDir);
                
                tempPath = await PATH.join(dlDir, `${UUIDv4().toString()}.temp`);
                await downloadInPlugin(url, tempPath);
            } catch (e) {
                if (typeof e === "string" && e.includes("status code 404")) {
                    message.warning('抱歉，该文件未收集，暂无法下载');
                } else {
                    message.error(`${e}`);
                
                return;
            
        

        const distFilePath = await save({
            title: '请选择文件保存位置',
            defaultPath: filename,
        });
        if (distFilePath === null) {
            if (tempPath !== '') {
                await removeFile(tempPath);
            
            return;
        

        tempPath = isLocalFilePath ? url : tempPath;
        try {
            await copyFile(tempPath, distFilePath);
        } catch (e) {
            message.error(`${e}`);
            return;
        

        try {
            await openPath(distFilePath);
        } catch (e) {
            message.error(`文件打开失败：${e}`);
        
    

    return (
        <>
            <Card
                className="w-full"
                bordered={false
            >
                <Row gutter={16}>
                    {mapObjs.map((item) => {
                        return (
                            <Col span={6} key={item.mapId}>
                                <Card bordered={true} className="h-80 text-center">
                                    <div className="size-full cursor-pointer"
                                         style={{textAlign: "center"}
                                    >
                                        <Image
                                            style={{display: "inline-block"}
                                            src={`${item.jpgPath}/thumbs/${item.mapId}.jpg`
                                            preview={{
                                                src: `${item.jpgPath}/images/${item.mapId}.jpg`,
                                                toolbarRender: (originalNode) => {
                                                    return (
                                                        <div style={{display: "flex", margin: 8}}>
                                                            {originalNode
                                                            <Button
                                                                type="primary"
                                                                icon={<DownloadOutlined/>
                                                                size="large"
                                                                style={{marginLeft: 12, marginRight: 12}
                                                                onClick={() => download(
                                                                    `${item.jpgPath}/eps/${item.mapId}.zip`,`${item.name}.zip`
                                                                )
                                                            >
                                                                下载EPS压缩包
                                                            </Button>
                                                            <Button
                                                                type="primary"
                                                                icon={<DownloadOutlined/>
                                                                size="large"
                                                                onClick={() => download(
                                                                    `${item.jpgPath}/images/${item.mapId}.jpg`,`${item.name}.jpg`
                                                                )
                                                            >
                                                                下载JPG图片
                                                            </Button>
                                                        </div>
                                                    )
                                                
                                            }}/>
                                    </div>
                                    <div style={{margin: 2, textAlign: "center"}}>{item.name}</div>
                                    <div className="w-full"
                                         style={{display: "flex", textAlign: "center"}}>
                                        <span className="pr-2"
                                              style={{width: "20%", marginLeft: 60, marginRight: 12, alignSelf: "center"}
                                        >
                                            {item.provinceColor == 1 ? '分省设色' : undefined
                                        </span>
                                        <span
                                            style={{marginRight: 12, alignSelf: "center"}
                                        >
                                            {item.neighbouringCountry == 1 ? '有邻国' : undefined
                                        </span>
                                        <span
                                            style={{alignSelf: "center"}
                                        >
                                            {item.neighbouringCountry == 0 ? '无邻国' : undefined
                                        </span>
                                    </div>
                                    <div className="w-full text-center" style={{margin: 2, textAlign: "center"}}>
                                        <a className="px-2" style={{margin: 4}
                                           onClick={() => download(
                                               `${item.jpgPath}/images/${item.mapId}.jpg`,`${item.name}.jpg`
                                           )
                                        >
                                            <DownloadOutlined/>
                                            下载JPG
                                        </a>
                                        <a className="px-2" style={{margin: 4}
                                           onClick={() => download(
                                               `${item.jpgPath}/eps/${item.mapId}.zip`,`${item.name}.zip`
                                           )
                                        >
                                            <DownloadOutlined/>
                                            下载EPS
                                        </a>
                                    </div>
                                </Card>
                            </Col>
                        )
                    })
                </Row>
            </Card>
            <Pagination
                current={pi
                pageSize={12
                total={total
                align={"center"
                hideOnSinglePage={true
                showSizeChanger={false
                showQuickJumper={true
                onChange={(page) => changePage(page)
                style={{marginTop:16}
            />
        </>
    )


export default MapContent

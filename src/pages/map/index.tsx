import {
    Button,
    Card,
    Col, Empty,
    Form,
    Input,
    Menu,
    MenuProps, message,
    Row,
    Select,
    Space,
    Tag,
    Typography
} from 'antd'
import {
    Category,
    MapObjModel
} from './../../interface/map.ts'
import React, { useEffect, useState } from 'react'
import Breadcrumb from './Breadcrumb'
import MapContent from './Content'
import {useNavigate} from "react-router-dom";
import {invokeGetMapsFilters, invokeGetMapsTotal} from "../../invoker/map.ts";

const items: MenuProps['items'] = [
    {
        label: <span className="font-bold">中国地图</span>,
        key: '中国地图'
    },
    {
        label: <span className="font-bold">世界地图</span>,
        key: '世界地图'
    },
    {
        label: <span className="font-bold">专题地图</span>,
        key: '专题地图'
    },
    {
        label: <span className="font-bold">参考地图</span>,
        key: '参考地图'
    
]


const MapPage: React.FC = () => {
    const [searchValue, setSearchValue] = useState<string>()
    const [category, setCategory] = useState<Category>()
    const [categorys, setCategorys] = useState<Category[]>([])
    const [borders, setBorders] = useState<string[]>([])
    const navigate = useNavigate();
    const [initPage,setInitPage] = useState(true);
    const [totals, setTotals] = useState(0);
    const [optionsMap, setOptionsMap] = useState<{
        "border":[{
            "label": string,
            "value": string
        }],
        "size":[{
            "label": string,
            "value": string
        }],
        "scale":[{
            "label": string,
            "value": string
        }],
        "neighbouringCountry": [
            {
                "label": string,
                "value": number
            
        ],
        "provinceColor": [
            {
                "label": string,
                "value": number
            
        ],
        "illustration": [
            {
                "label": string,
                "value": number
            
        ]
    }>({
        "border":[{
            "label": "",
            "value": ""
        }],
        "size":[{
            "label": "",
            "value": ""
        }],
        "scale":[{
            "label": "",
            "value": ""
        }],
        "neighbouringCountry": [
            {
                "label": "",
                "value": 3
            
        ],
        "provinceColor": [
            {
                "label": "",
                "value": 3
            
        ],
        "illustration": [
            {
                "label": "",
                "value": 3
            
        ]
    })
    const [formValues, setFormValues] = useState<MapObjModel>()
    const [messageApi, contextHolder] = message.useMessage();
    const [form] = Form.useForm()
    async function getMapsFilters() {
        const res:{
            code:number,
            data: {
                "border":[{
                    "label": string,
                    "value": string
                }],
                "size":[{
                    "label": string,
                    "value": string
                }],
                "scale":[{
                    "label": string,
                    "value": string
                }],
                "neighbouringCountry": [
                    {
                        "label": string,
                        "value": number
                    
                ],
                "provinceColor": [
                    {
                        "label": string,
                        "value": number
                    
                ],
                "illustration": [
                    {
                        "label": string,
                        "value": number
                    
                ]
            },
            msg:string
        } = await invokeGetMapsFilters({
            name:"",
            index:0
        });
        setOptionsMap(res.data)
    
    async function getMapsTotal() {
        const res:{
            code:number,
            data: number,
            msg:string
        } = await invokeGetMapsTotal();
        setTotals(res.data)
    
    useEffect(() => {
        if (initPage) {
            const init = async () => {
                await getMapsTotal();
                await getMapsFilters();
            
            init().then()
            setInitPage(false);
        
    }, [totals,optionsMap,initPage])

    const onCategorysChange = async (cs: Category[]) => {
        if (totals === 0) {
            messageApi.info('请先导入地图册增量包');
        } else {
            let n = "";
            let ind = 0;
            cs.map((c, index) => {
                n = c.name;
                ind = index;
            })
            const res: {
                code: number,
                data: {
                    "border": [{
                        "label": string,
                        "value": string
                    }],
                    "size": [{
                        "label": string,
                        "value": string
                    }],
                    "scale": [{
                        "label": string,
                        "value": string
                    }],
                    "neighbouringCountry": [
                        {
                            "label": string,
                            "value": number
                        
                    ],
                    "provinceColor": [
                        {
                            "label": string,
                            "value": number
                        
                    ],
                    "illustration": [
                        {
                            "label": string,
                            "value": number
                        
                    ]
                },
                msg: string
            } = await invokeGetMapsFilters({
                name: n,
                index: ind
            });
            setOptionsMap(res.data)
            setBorders([])
            form.resetFields()
        
    };

    const onClick: MenuProps['onClick'] = async (e) => {
        if (totals === 0) {
            messageApi.info('请先导入地图册增量包');
        } else {
            const res: {
                code: number,
                data: {
                    "border": [{
                        "label": string,
                        "value": string
                    }],
                    "size": [{
                        "label": string,
                        "value": string
                    }],
                    "scale": [{
                        "label": string,
                        "value": string
                    }],
                    "neighbouringCountry": [
                        {
                            "label": string,
                            "value": number
                        
                    ],
                    "provinceColor": [
                        {
                            "label": string,
                            "value": number
                        
                    ],
                    "illustration": [
                        {
                            "label": string,
                            "value": number
                        
                    ]
                },
                msg: string
            } = await invokeGetMapsFilters({
                name: e.key,
                index: 1
            });
            setOptionsMap(res.data)
            setBorders([])
            form.resetFields()
            setCategory({name: e.key, parent: undefined})
            setCategorys([{name: e.key, parent: undefined}])
        
    

    const onBorderTagClick = (border: string) => {
        if (borders.includes(border)) {
            setBorders(borders.filter((b) => b !== border))
        } else {
            setBorders([...borders, border])
        
    

    return (
        <>
            {contextHolder
            <div className="size-full max-w-full overflow-y-auto">
                <Card
                    className="w-full"
                    title={<div className="px-8 py-2">
                        <span className="text-3xl tracking-widest">地图册</span>
                    </div>
                >
                    <div className="px-72 text-center">
                        <Input.Search
                            placeholder="名称或地图号搜索"
                            onSearch={(value) => {
                                setSearchValue(value)
                            }
                        />
                    </div>

                    <div className="px-72 py-4 text-center">
                        <Menu
                            onClick={onClick
                            mode="horizontal"
                            items={items
                            style={{fontSize: 16}}/>
                    </div>

                    <Card bordered={false}>
                        <Space className="flex"
                               style={{display: "flex", padding: 2}
                        >
                            <div className="py-2">分类：</div>
                            <div>
                                {category && (
                                    <Breadcrumb
                                        category={category
                                        onChange={(categorys: Category[]) => {
                                            onCategorysChange(categorys).then()
                                            setCategorys(categorys)
                                        }}/>
                                )
                            </div>
                        </Space>
                        {(optionsMap && optionsMap['border'].length > 0) ? (
                            <Space className="flex"
                                   style={{display: "flex", padding: 2}
                            >
                                <div className="py-2">表现形式：</div>
                                <div>
                                    {optionsMap['border'].map((b) => {
                                        return (
                                            <Tag.CheckableTag
                                                key={b.value
                                                checked={borders.includes(b.value)
                                                onClick={() => onBorderTagClick(b.value)
                                            >
                                                {b.label
                                            </Tag.CheckableTag>
                                        )
                                    })
                                </div>
                            </Space>)
                            :(<Space className="flex"
                                    style={{display: "flex", padding: 2}
                            >
                                <div className="py-2">表现形式：</div>
                                <div>
                                    暂无数据
                                </div>
                            </Space>)
                        

                        <div className="flex w-full"
                             style={{display: "block", padding: 2}
                        >
                            <div className="w-24 py-2">高级选项：</div>
                            <div className="w-full">
                                <Form<MapObjModel>
                                    layout="horizontal"
                                    className="w-full"
                                    form={form
                                    onValuesChange={(changedValues, values) => {
                                        setFormValues(values)
                                    }
                                >
                                    <Row gutter={16}>
                                        {optionsMap && optionsMap['size'].length > 0 && (
                                            <Col span={4}>
                                                <Form.Item name="size" label="规格">
                                                    <Select
                                                        variant="borderless"
                                                        placeholder="不限"
                                                        allowClear={true
                                                        options={optionsMap['size']
                                                        showSearch/>
                                                </Form.Item>
                                            </Col>
                                        )
                                        {optionsMap && optionsMap['scale'].length > 0 && (
                                            <Col span={5}>
                                                <Form.Item name="scale" label="比例尺">
                                                    <Select
                                                        variant="borderless"
                                                        placeholder="不限"
                                                        allowClear={true
                                                        options={optionsMap['scale']
                                                        showSearch/>
                                                </Form.Item>
                                            </Col>
                                        )
                                        {optionsMap &&
                                            optionsMap['neighbouringCountry'].length > 0 && (
                                                <Col span={4}>
                                                    <Form.Item name="neighbouringCountry" label="邻国">
                                                        <Select
                                                            variant="borderless"
                                                            placeholder="不限"
                                                            allowClear={true
                                                            options={optionsMap['neighbouringCountry']
                                                            showSearch/>
                                                    </Form.Item>
                                                </Col>
                                            )
                                        {optionsMap && optionsMap['provinceColor'].length > 0 && (
                                            <Col span={4}>
                                                <Form.Item name="provinceColor" label="分省设色">
                                                    <Select
                                                        variant="borderless"
                                                        placeholder="不限"
                                                        allowClear={true
                                                        options={optionsMap['provinceColor']
                                                        showSearch/>
                                                </Form.Item>
                                            </Col>
                                        )
                                        {optionsMap && optionsMap['illustration'].length > 0 && (
                                            <Col span={6}>
                                                <Form.Item name="illustration" label="南海诸岛以附图表示">
                                                    <Select
                                                        variant="borderless"
                                                        placeholder="不限"
                                                        allowClear={true
                                                        options={optionsMap['illustration']
                                                        showSearch/>
                                                </Form.Item>
                                            </Col>
                                        )
                                    </Row>
                                </Form>
                            </div>
                        </div>
                    </Card>
                    {totals===0 ?
                        <Empty
                            image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg"
                            styles={{ image: { height: 60 } }
                            description={
                                <Typography.Text>
                                    地图数据为空
                                </Typography.Text>
                            
                        >
                            <Button type="primary"
                                    onClick={()=>navigate('/settings/increment-update')
                            >
                                增量更新地图册
                            </Button>
                        </Empty>
                        :<MapContent
                            searchValue={searchValue
                            categorys={categorys
                            borders={borders
                            mapObj={formValues}/>
                    

                </Card>

            </div>
        </>
    )


export default MapPage

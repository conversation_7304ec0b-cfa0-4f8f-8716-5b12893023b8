import { Breadcrumb as B<PERSON>, <PERSON><PERSON>, Popover } from 'antd'
import { Category } from './../../interface/map.ts'
import React, { useEffect, useState } from 'react'
import {invokeGetMapsBreadCrumb} from "../../invoker/map.ts";

const getBreadItems = (curNode: Category, TreeNodes: Category[]) => {
    let items: Category[] = []
    for (const node of TreeNodes) {
        if (node.name == curNode.name && node.parent == curNode.parent) {
            items.push(node)
            return items
        
        if (node.children) {
            const childrenItems = getBreadItems(curNode, node.children)
            if (childrenItems.length > 0) {
                items = [node, ...childrenItems]
            
        
    
    return items


export type BreadcrumbProps = {
    category: Category
    onChange: (categorys: Category[]) => void


/**
 * 地图分类
 */
const Breadcrumb: React.FC<BreadcrumbProps> = ({ category, onChange }) => {
    const [open, setOpen] = useState<string>()
    const [curNode, setCurNode] = useState<Category>(category)
    const [data, setData] = useState<Category[]>([])
    const [nodes, setNodes] = useState<Category[]>([])

    async function getBreadcrumb() {
        const bc:{
            code:number,
            data:Category[],
            msg:string
        } = await invokeGetMapsBreadCrumb();
        setData(bc.data)
    

    useEffect(() => {
        const init = async () => {
            await getBreadcrumb();
        
        init().then()
    }, [])

    useEffect(() => {
        const newNodes = getBreadItems(curNode, data)
        setNodes(newNodes)
    }, [data, category, curNode])

    useEffect(() => {
        setNodes(getBreadItems(category, data))
    }, [category, data])

    const renderPopoverContent = (items: Category[]) => {
        if (items && items.length > 0) {
            return (
                <div
                     style={{overflow:"auto",maxHeight:"500px"}
                >
                    {items.map((item: Category) => {
                        return (
                            <div key={item.name}>
                                <Button
                                    type="text"
                                    block
                                    className="text-left"
                                    onClick={() => {
                                        setCurNode(item)
                                        setOpen(undefined)
                                        onChange(getBreadItems(item, data))
                                    }
                                >
                                    {item.name
                                </Button>
                            </div>
                        )
                    })
                </div>
            )
        } else {
            return undefined
        
    

    const items = nodes.map((item) => {
        return {
            title: (
                <Popover
                    content={renderPopoverContent(item.children || [])
                    placement="bottom"
                    trigger={['hover']
                    open={item.name == open
                    arrow={false
                    onOpenChange={(e) => {
                        if (e) {
                            setOpen(item.name)
                        } else {
                            setOpen(undefined)
                        
                    }
                >
                    <a className="flex">
                        <div>{item.name}</div>
                    </a>
                </Popover>
            )
        
    })

    return <div className="p-2">{curNode && <Bread items={items} />}</div>

export default Breadcrumb

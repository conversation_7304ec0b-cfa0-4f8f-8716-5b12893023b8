import Search from "antd/es/input/Search";
import {Col, message, Radio, Row} from "antd";
import React, {useEffect, useState } from "react";
import DictSearchResultList from "./components/DictSearchResultList.tsx";
import {BookPageByTypesParams, BookVo} from "../../interface/BookInterface.ts";
import {invokeGetBookPageByTypes} from "../../invoker/book.ts";
import {PageResult} from "../../interface";
import {WebviewWindow} from "@tauri-apps/api/webviewWindow";
import {openOrSwitchWindow} from "../../utils/Book.ts";
import {getFeFileUrlPrefix} from "../../invoker/file-url.ts";
import {buildFileUrl} from "../../utils/fileUrl.ts";
import {isTauriEnv} from "../../utils/system.ts";

const bookTypes = [
    '字典', '词典'
];

async function openDictionaryWindow(bookId: string, url: string, title: string) {
    if (isTauriEnv) {
        const label = 'dictionary_' + bookId;
        const newWindow = new WebviewWindow(label, {
            url: url,
            maximized: true,
        });

        await newWindow.once('tauri://created', async () => {
            await newWindow.setTitle(title);
        });

        await newWindow.show();
        await newWindow.setFocus();
    } else {
        window.open(url, '_blank');
    


function DictionarySearch() {
    const [searchBtnLoading, setSearchBtnLoading] = useState<boolean>(false);
    const [pageNoBookRst, setPageNoBookRst] = useState<number>(0);
    const [pageSizeBookRst, setPageSizeBookRst] = useState<number>(25);
    const [searchText, setSearchText] = useState<string>("");
    const [bookType, setBookType] = useState<string | null>(null);
    const [
        bookSearchResult, setBookSearchResult
    ] = useState<PageResult<BookVo> | undefined>(undefined);

    function onChangeSearchText(val: string) {
        setSearchText(val);
    

    useEffect(() => {
        async function useEffectAsync() {
            await onClickSearchBtn();
        

        useEffectAsync().then();
    }, []);

    async function onChangeBookType(val: string | null) {
        setBookType(val);
        setPageNoBookRst(0);

        await searchFunc(0, pageSizeBookRst, searchText, val);
    

    async function searchFunc(
        tempPageNoBookRst: number, tempPageSizeBookRst: number,
        tempSearchText: string, tempBookType: string | null,
    ) {
        setSearchBtnLoading(true);
        try {
            const bookTypes = [];
            if (!tempBookType) {
                bookTypes.push('字典', '词典');
            } else {
                bookTypes.push(tempBookType);
            
            const params: BookPageByTypesParams = {
                searchText: tempSearchText, bookTypes, pageNo: tempPageNoBookRst, pageSize: tempPageSizeBookRst
            };

            params.pageNo = tempPageNoBookRst;
            params.pageSize = tempPageSizeBookRst;
            const result = await invokeGetBookPageByTypes(params);
            const urlPrefix = await getFeFileUrlPrefix();
            for (const it of result.list) {
                it.coverImageUrl = buildFileUrl(urlPrefix, it.coverImageUrl);
            
            if (bookSearchResult === undefined) {
                setBookSearchResult({...result});
            } else {
                setBookSearchResult({
                    ...bookSearchResult,
                    ...result,
                });
            

        } catch (e) {
            message.error(`搜索出错：${e}`);
        } finally {
            setSearchBtnLoading(false);
        
    

    async function onClickSearchBtn() {
        setPageNoBookRst(0);
        await searchFunc(0, pageSizeBookRst, searchText, bookType);
    

    async function onChangePageNoBookRst(tempPageNo: number) {
        setPageNoBookRst(tempPageNo);
        await searchFunc(
            tempPageNo, pageSizeBookRst, searchText, bookType
        );
    

    async function onChangePageSizeBookRst(tempPageSize: number) {
        setPageNoBookRst(0);
        setPageSizeBookRst(tempPageSize);
        await searchFunc(
            0, tempPageSize, searchText, bookType
        );
    

    async function openBookReader(bookType: string, bookId: string, catalogueId?: string, pageNo?: number) {
        if (bookId === '54389') {
            const url = `/dictionary/letter-dictionary-book/${bookId}`;
            await openDictionaryWindow(bookId, url, '新华字典');
        } else if (bookId === '12783') {
            const url = `/dictionary/word-dictionary-book/${bookId}`;
            await openDictionaryWindow(bookId, url, '现代汉语词典');
        } else {
            pageNo ? await openOrSwitchWindow('textbook_' + bookId, bookId, bookType, pageNo)
                : await openOrSwitchWindow('textbook_' + bookId, bookId, bookType)
        
    


    return (<>
        <div>
            <div style={{display: 'flex', justifyContent: 'center'}}>
                <Search
                    value={searchText
                    placeholder="请输入搜索内容"
                    enterButton="搜索"
                    size="middle"
                    loading={searchBtnLoading
                    onChange={(e) => onChangeSearchText(e.target.value)
                    onSearch={onClickSearchBtn
                    style={{maxWidth: '1000px'}
                />
            </div>
            <div style={{display: 'flex', justifyContent: 'center'}}>
                <div style={{width: '100%', maxWidth: '1000px'}}>
                    <div className={'flex-container'}>
                        <span className={'right-align-text'}>类型：</span>
                        <Radio.Group onChange={e => onChangeBookType(e.target.value)} value={bookType}>
                            <Row>
                                <Col className={'ellipsis-text'}>
                                    <Radio value={null}>不限</Radio>
                                </Col>
                                {bookTypes.map(bt => (
                                    <Col className={'ellipsis-text'} key={bt}>
                                        <Radio value={bt}>{bt}</Radio>
                                    </Col>
                                ))
                            </Row>
                        </Radio.Group>
                    </div>
                </div>
            </div>
        </div>
        <div>
            {searchBtnLoading && (
                <h1>加载中....</h1>
            )
            {!searchBtnLoading && <>
                <div style={{display: 'flex', justifyContent: 'center'}}>
                    <DictSearchResultList
                        result={bookSearchResult
                        onChangePageNo={onChangePageNoBookRst
                        onChangePageSize={onChangePageSizeBookRst
                        onClickResultItem={openBookReader
                        style={{width: '100%', maxWidth: '1000px'}
                    />
                </div>
            </>
        </div>
    </>)


export default DictionarySearch;
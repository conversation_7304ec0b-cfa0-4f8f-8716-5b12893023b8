import "./LetterDictionaryBook.css";

import {useEffect, useState} from "react";
import {<PERSON><PERSON>, <PERSON>, Col, Collapse, CollapseProps, Divider, Empty, Image, message, Radio, Row} from "antd";
import Search from "antd/es/input/Search";
import {useParams} from "react-router-dom";
import {
    Letter,
    LetterRadicalIdSectionVo,
    PinyinSectionVo,
    PinyinWithoutToneVo,
    SearchResourcesParams,
    SearchTypeEnum, Snapshot, SRRListVo,
    StrokeRadicalsVo
} from "../../interface/dictionary.ts";
import {
    getLettersByStrokeCount,
    getLetterSectionByRadicalId,
    getLettersWithPinyinByWithoutTone, invokeGetAllRadicals,
    invokeGetDictionary, invokeGetLetterById,
    invokeGetPinyinWithoutTones, invokeGetSnapshots, invokeSearchResources
} from "../../invoker/dictionary.ts";
import {<PERSON>Vo} from "../../interface/BookInterface.ts";
import classNames from "classnames";
import {getFeFileUrlPrefix} from "../../invoker/file-url.ts";
import {buildFileUrl} from "../../utils/fileUrl.ts";


const globalStrokeList = [
    1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20
    ,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36
];

function LetterDictionaryBook() {
    const [urlPrefix, setUrlPrefix] = useState<string>("");
    const {bookId} = useParams<{ bookId: string }>();
    const [book, setBook] = useState<BookVo | null>(null);
    const [snapshots, setSnapshots] = useState<Snapshot[] | null>(null);
    const [searchText, setSearchText] = useState<string>('');
    // selectMode 管理左侧面板类型
    const [selectMode, setSelectMode] = useState<string>('spell');
    // 拼音列表
    const [spellList, setSpellList] = useState<PinyinWithoutToneVo[]>([]);
    // 当前无声调拼音下的所有字列表
    const [spellLetterList, setSpellLetterList] = useState<PinyinSectionVo[]>([]);
    // 当前部首下所有汉字列表
    const [radicalLetterList, setRadicalLetterList] = useState<LetterRadicalIdSectionVo[]>([]);
    // 当前汉字笔画数下所有汉字列表
    const [strokeLetterList, setStrokeLetterList] = useState<Letter[]>([]);
    // 部首列表
    const [radicalList, setRadicalList] = useState<StrokeRadicalsVo[]>([]);
    // 汉字笔画列表
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [strokeList, setStrokeList] = useState<number[]>([...globalStrokeList]);
    // 搜索结果列表
    const [searchResultList, setSearchResultList] = useState<SRRListVo[] | null>(null);
    // 当前搜索结果折叠模板展开的key列表
    const [searchResultCollapseActiveKey, setSearchResultCollapseActiveKey] = useState<string[]>([]);
    // 当前选中的拼音首字母
    const [nowFirstSpell, setNowFirstSpell] = useState<string | null>(null);
    // 当前选中的无声调拼音
    const [nowSpell, setNowSpell] = useState<string | null>(null);
    // 选中的当前部首笔画数
    const [nowRadicalStroke, setNowRadicalStroke] = useState<number | null>(null);
    // 选中的当前部首id
    const [nowRadicalId, setNowRadicalId] = useState<number | null>(null);
    // 选中的当前汉字笔画数
    const [nowLetterStroke, setNowLetterStroke] = useState<number | null>(null);
    // 当前选中的汉字id
    const [nowLetterId, setNowLetterId] = useState<number | null>(null);
    // 当前选中的汉字
    const [nowLetter, setNowLetter] = useState<Letter | null>(null);
    // 是否显示快照
    const [snapshotVisible, setSnapshotVisible] = useState<boolean>(false);
    // 当前要查看的快照路径
    const [nowSnapPath, setNowSnapPath] = useState<string>('');


    useEffect(() => {
        async function useEffectAsync() {
            try {
                if (bookId) {
                    const tempBook = await invokeGetDictionary(bookId);
                    const tempUrlPrefix = await getFeFileUrlPrefix();
                    setUrlPrefix(tempUrlPrefix);
                    tempBook.coverImageUrl = buildFileUrl(tempUrlPrefix, tempBook.coverImageUrl);
                    setBook({...tempBook});
                    const tempSpellList = await invokeGetPinyinWithoutTones(bookId);
                    setSpellList([...tempSpellList]);
                    const tempRadicalList = await invokeGetAllRadicals(bookId);
                    setRadicalList([...tempRadicalList]);
                
            } catch (e) {
                message.error(`${e}`);
            
        

        useEffectAsync().then();
    }, []);

    function onChangeSearchText(text: string) {
        setSearchText(text);
    

    function onClearSearchText() {
        setSelectMode("spell");
    

    function onChangeSelectMode(tempSelectMode: string) {
        setSelectMode(tempSelectMode);
    

    async function onSearch() {
        const tempText = searchText.trim();
        if (tempText === '') {
            message.warning('请输入搜索关键词');
        } else {
            setSelectMode('search');
            const searchTypes: SearchTypeEnum[] = [1, 2, 3];
            if (bookId) {
                const params: SearchResourcesParams = {
                    bookId: bookId, searchText: tempText, searchType: null
                };
                const results: SRRListVo[] = [];
                for (const st of searchTypes) {
                    params.searchType = st;
                    const rst = await invokeSearchResources(params);
                    results.push(...rst.list);
                
                setSearchResultList([...results]);
                setSearchResultCollapseActiveKey(['0', (results.length - 1).toString()])
            
        
    

    function onChangeNowFirstSpell(tempFirstSpell: string) {
        setNowFirstSpell(tempFirstSpell);
        setNowSpell(null);
        setTimeout(() => {
            const el = window.document.getElementById('spellGroup_' + tempFirstSpell);
            if (el) {
                el.scrollIntoView({ behavior: "smooth" });
            
        }, 100);
    

    async function onChangeNowSpell(tempFirstSpell: string, tempNowSpell: string) {
        setNowFirstSpell(tempFirstSpell);
        setNowSpell(tempNowSpell);
        if (bookId) {
            const tempSpellLetterList = await getLettersWithPinyinByWithoutTone(bookId, tempNowSpell);
            setSpellLetterList([...tempSpellLetterList]);
        
    

    async function onChangeNowRadicalStroke(tempRadicalStroke: number) {
        setNowRadicalStroke(tempRadicalStroke);
        setNowRadicalId(null);
        setTimeout(() => {
            const el = window.document.getElementById('radicalGroup_' + tempRadicalStroke);
            if (el) {
                el.scrollIntoView({ behavior: "smooth" });
            
        }, 100);
    

    async function onChangeNowRadicalId(tempRadicalStroke: number, tempRadicalId: number) {
        setNowRadicalStroke(tempRadicalStroke);
        setNowRadicalId(tempRadicalId);
        if (bookId) {
            const tempRadicalLetterList = await getLetterSectionByRadicalId(bookId, tempRadicalId);
            setRadicalLetterList([...tempRadicalLetterList]);
        
    

    async function onChangeNowLetterStroke(tempStrokeCount: number) {
        setNowLetterStroke(tempStrokeCount);
        setNowLetterId(null);
        if (bookId) {
            const tempLetters = await getLettersByStrokeCount(bookId, tempStrokeCount);
            setStrokeLetterList([...tempLetters]);
        
    

    async function onChangeLetter(tempLetterId: number) {
        setNowLetterId(tempLetterId);
        const tempLetter = await invokeGetLetterById(tempLetterId);
        setNowLetter({...tempLetter});
    

    async function showSnapshot(tempPage: number | null) {
        if (tempPage === null) {
            message.error('当前汉字没有纸质书的页码信息，无法查看快照！');
            return;
        
        let tempSnapshots: Snapshot[] = [];
        if (snapshots === null) {
            if (bookId) {
                tempSnapshots = await invokeGetSnapshots(bookId);
                for (const sp of tempSnapshots) {
                    sp.path =buildFileUrl(urlPrefix, sp.path);
                
                setSnapshots([...tempSnapshots]);
            
        } else {
            tempSnapshots = snapshots;
        

        const curSnap = tempSnapshots.find(ss => ss.page === tempPage);
        if (!curSnap) {
            message.error('无法根据当前汉字的页面找到对应快照！');
            return;
        
        setNowSnapPath(curSnap.path);
        setSnapshotVisible(true);
    

    function buildSearchCollapseItems(tempResults: SRRListVo[]) {
        const items: CollapseProps['items'] = [];
        for (let i = 0; i < tempResults.length; i++) {
            const r = tempResults[i];
            items.push({
                key: i,
                label: r.tip,
                children: <div className="letter-group">
                    {r.items.map((it, j) => <>
                        <div key={j}>
                            <div className="letter-stroke-item">
                                { it.pyWithTone 
                            </div>
                            <div
                                className={classNames({
                                    'letter-item': true,
                                    'select': it.id === nowLetterId,
                                })
                                onClick={() => onChangeLetter(it.id)
                            >
                                { it.nameOutBase 
                            </div>
                        </div>
                    </>)
                </div>
            });
        

        return items;
    

    function onChangeSearchResultCollapse(key: string[]) {
        setSearchResultCollapseActiveKey([...key]);
    

    return (<>
        <Row>
            <Col span={12} style={{border: '1px solid #ededed'}}>
                <div style={{display: 'flex', justifyContent: 'center', marginTop: '5px'}}>
                    <Search
                        placeholder="请输入搜索文本"
                        allowClear
                        onClear={onClearSearchText
                        onSearch={onSearch
                        onChange={e => onChangeSearchText(e.target.value)
                        style={{ width: 300 }
                    />
                </div>
                {selectMode !== 'search' && <>
                    <div style={{display: 'flex', justifyContent: 'center', marginTop: '15px'}}>
                        <Radio.Group value={selectMode} onChange={e => onChangeSelectMode(e.target.value)}>
                            <Radio.Button value="spell">拼音</Radio.Button>
                            <Radio.Button value="radical">部首</Radio.Button>
                            <Radio.Button value="stroke">笔画</Radio.Button>
                        </Radio.Group>
                    </div>
                </>
                <div style={{display: 'flex', justifyContent: 'center', margin: '15px 5px 0 5px'}}>
                    <Card style={{width:'100%', height: 'calc(100vh - 140px)', backgroundColor: 'rgb(247, 247, 247)'}}>
                        {selectMode === 'spell' && (<>
                            <div className="top-select-group" style={{height: '15%'}}>
                                {spellList.map((spell, i) => <div key={i}>
                                    <div
                                        className={classNames({
                                            'top-select-item': true,
                                            'select': spell.firstChar === nowFirstSpell,
                                        })
                                        onClick={() => onChangeNowFirstSpell(spell.firstChar)
                                    >
                                        { spell.firstChar 
                                    </div>
                                </div>)
                            </div>
                            <div style={{maxHeight: 'calc(100vh - 270px)', overflowY: 'auto'}}>
                                {spellList.map(spell =>
                                    <>{((nowSpell && nowFirstSpell && spell.withoutTones.includes(nowSpell)) || !nowSpell) &&
                                        <div key={`spellGroup_${spell.firstChar}`} id={`spellGroup_${spell.firstChar}`}>
                                            <Divider>{ spell.firstChar }</Divider>
                                            <div style={{display: 'flex', flexWrap: 'wrap'}}>
                                                {spell.withoutTones.map((wt, j) =>
                                                    <div
                                                        key={`${spell.firstChar}_wt_${j}`
                                                        className={classNames({
                                                            'spell-item': true,
                                                            'select': wt === nowSpell,
                                                        })
                                                        onClick={() => onChangeNowSpell(spell.firstChar, wt)
                                                    >
                                                        { wt 
                                                    </div>
                                                )
                                            </div>
                                        </div>
                                    </>)
                            </div>
                            {nowSpell && <div style={{height: 'calc(100vh - 420px)', overflowY: 'auto'}}>
                                {spellLetterList.map(sl => <div key={sl.pinyinId}>
                                    <Divider>{ sl.pyWithTone }</Divider>
                                    <div className="letter-group">
                                        {sl.letters.map(le => <>
                                            <div>
                                                <div className="letter-stroke-item">
                                                    {le.strokeCount !== null ? <>{ le.strokeCount }画</> : <>无笔画数</>
                                                </div>
                                                <div
                                                    className={classNames({
                                                        'letter-item': true,
                                                        'select': le.letterId === nowLetterId,
                                                    })
                                                    onClick={() => onChangeLetter(le.letterId)
                                                >
                                                    { le.nameOutBase 
                                                </div>
                                            </div>
                                        </>)
                                    </div>
                                </div>)
                            </div>
                        </>)
                        {selectMode === 'radical' && (<>
                            <div className="top-select-group" style={{height: '15%'}}>
                                {radicalList.map((sr, i) => <div key={i}>
                                    <div
                                        className={classNames({
                                            'top-select-item': true,
                                            'select': sr.stroke === nowRadicalStroke,
                                        })
                                        onClick={() => onChangeNowRadicalStroke(sr.stroke)
                                    >
                                        { sr.stroke 
                                    </div>
                                </div>)
                            </div>
                            <div style={{maxHeight: 'calc(100vh - 270px)', overflowY: 'auto'}}>
                                {radicalList.map(sr =>
                                    <>{((nowRadicalId && sr.list.find(r => r.radicalId === nowRadicalId)) || !nowRadicalId) &&
                                        <div key={`radicalGroup_${sr.stroke}`} id={`radicalGroup_${sr.stroke}`}>
                                            <Divider>{ sr.stroke }画</Divider>
                                            <div style={{display: 'flex', flexWrap: 'wrap'}}>
                                                {sr.list.map((r, j) =>
                                                    <div
                                                        key={`${sr.stroke}_wt_${j}`
                                                        className={classNames({
                                                            'spell-item': true,
                                                            'select': r.radicalId === nowRadicalId,
                                                        })
                                                        onClick={() => onChangeNowRadicalId(sr.stroke, r.radicalId)
                                                    >
                                                        { r.name 
                                                    </div>
                                                )
                                            </div>
                                        </div>
                                    </>)
                            </div>
                            {nowRadicalId && <div style={{height: 'calc(100vh - 420px)', overflowY: 'auto'}}>
                                {radicalLetterList.map((lrs, i) => <div key={i}>
                                    <Divider>剩{ lrs.remainingStrokeCount }画</Divider>
                                    <div className="letter-group">
                                        {lrs.letters.map(le => <>
                                            <div>
                                                <div className="letter-stroke-item">
                                                    { le.pinyinList 
                                                </div>
                                                <div
                                                    className={classNames({
                                                        'letter-item': true,
                                                        'select': le.letterId === nowLetterId,
                                                    })
                                                    onClick={() => onChangeLetter(le.letterId)
                                                >
                                                    { le.nameOutBase 
                                                </div>
                                            </div>
                                        </>)
                                    </div>
                                </div>)
                            </div>
                        </>)
                        {selectMode === 'stroke' && (<>
                            <div className="top-select-group" style={{height: '15%'}}>
                                {strokeList.map((s, i) => <div key={i}>
                                    <div
                                        className={classNames({
                                            'top-select-item': true,
                                            'select': s === nowLetterStroke,
                                        })
                                        onClick={() => onChangeNowLetterStroke(s)
                                    >
                                        { s 
                                    </div>
                                </div>)
                            </div>
                            {nowLetterStroke && <div style={{display: 'flex', flexWrap: 'wrap', height: 'calc(100vh - 420px)', overflowY: 'auto'}}>
                                {strokeLetterList.map((le, i) => <div key={i}>
                                    <div className="letter-stroke-item">
                                        { le.pinyinWithTones 
                                    </div>
                                    <div
                                        className={classNames({
                                            'letter-item': true,
                                            'select': le.letterId === nowLetterId,
                                        })
                                        onClick={() => onChangeLetter(le.letterId)
                                    >
                                        { le.nameOutBase 
                                    </div>
                                </div>)
                            </div>
                        </>)
                        {selectMode === 'search' && (<>
                            <div style={{height: 'calc(100vh - 170px)', overflowY: 'auto'}}>
                                {searchResultList !== null && searchResultList.length > 0 && (<>
                                    <Collapse
                                        defaultActiveKey={['0']
                                        items={buildSearchCollapseItems(searchResultList)
                                        activeKey={searchResultCollapseActiveKey
                                        onChange={onChangeSearchResultCollapse
                                    />
                                </>)
                                {searchResultList !== null && searchResultList.length === 0 && (<>
                                    <Empty />
                                </>)
                            </div>
                        </>)
                    </Card>
                </div>
            </Col>
            <Col span={12} style={{border: '1px solid #ededed'}}>
                <div style={{display: 'flex', justifyContent: 'center', marginTop: '5px'}}>
                    <Card style={{width:'100%', height: 'calc(100vh - 46px)', backgroundColor: 'rgb(247, 247, 247)'}} >
                        {!nowLetterId && book && <>
                            <div style={{display: 'flex', justifyContent: 'center'}}>
                                <img alt={book.bookName} src={`${book.coverImageUrl}`} style={{width: '70%', maxWidth: '500px'}} />
                            </div>
                            <div style={{display: 'flex', justifyContent: 'center'}}>
                                <div>
                                    <h3>{ book.bookName }</h3>
                                    <p>ISBN：{ book.isbn }</p>
                                    <p>出版社：{ book.publisher }</p>
                                </div>
                            </div>
                        </>
                        {nowLetter && <>
                            <div>
                                <div>
                                    <div style={{display: 'flex', justifyContent: 'center'}}>
                                        <div className="big-spell" style={{width: '100px'}}>
                                            { nowLetter.pinyinWithTones 
                                        </div>
                                    </div>
                                    <div style={{display: 'flex', justifyContent: 'center'}}>
                                        <div className="big-letter" style={{height: '120px'}}>
                                            { nowLetter.name }{nowLetter.nameOutBase && nowLetter.name !== nowLetter.nameOutBase && <>({nowLetter.nameOutBase})</>
                                        </div>
                                    </div>
                                    <div style={{display: 'flex', justifyContent: 'center'}}>
                                        <div className="big-info">
                                            <div>
                                                <span>字级：</span>{ nowLetter.level 
                                            </div>
                                            <div>
                                                <span>部首：</span>{ nowLetter.radical 
                                            </div>
                                            <div>
                                                <span>笔画：</span>{ nowLetter.strokeCount 
                                            </div>
                                            <div>
                                                <span>结构：</span>{ nowLetter.structure 
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <Card>
                                            <div style={{display: 'flex', justifyContent: 'center'}}>
                                                <div className="big-mean-head">例证</div>
                                            </div>
                                            <div>{nowLetter.illustration}</div>
                                            <div className="big-mean-end">
                                                ——来自{book?.bookName
                                            </div>
                                        </Card>
                                    </div>
                                </div>
                                <div style={{position: 'fixed', right: '40px', top: '50px'}}>
                                    <Button size="small" type="primary" onClick={() => showSnapshot(nowLetter?.page)}>查看快照</Button>
                                </div>
                                <Image
                                    style={{display: 'none'}
                                    src={nowSnapPath
                                    preview={{
                                        visible: snapshotVisible,
                                        src: nowSnapPath,
                                        onVisibleChange: (value) => {
                                            setSnapshotVisible(value);
                                        },
                                    }
                                />
                            </div>
                        </>
                    </Card>
                </div>
            </Col>
        </Row>
    </>);


export default LetterDictionaryBook;
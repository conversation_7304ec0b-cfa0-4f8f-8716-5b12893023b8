import "./WordDictionaryBook.css";

import {useEffect, useState} from "react";
import {<PERSON><PERSON>, Card, Col, Collapse, CollapseProps, Empty, Image, message, Radio, Row} from "antd";
import Search from "antd/es/input/Search";
import {useParams} from "react-router-dom";
import {
    Letter,
    SearchResourcesParams,
    SearchTypeEnum, Snapshot, SRRListItemVo, SRRListVo, Vocabulary,
} from "../../interface/dictionary.ts";
import {
    invokeGetDictionary, invokeGetLetterById, invokeGetSnapshots,
    invokeGetVocabularyById, invokeSearchResources
} from "../../invoker/dictionary.ts";
import {BookVo} from "../../interface/BookInterface.ts";
import classNames from "classnames";
import {getFeFileUrlPrefix} from "../../invoker/file-url.ts";
import {buildFileUrl} from "../../utils/fileUrl.ts";

function isLetter(x: any): x is Letter {
    return (x as Letter).letterId !== undefined;


function isVocabulary(x: any): x is Vocabulary {
    return (x as Vocabulary).vId !== undefined;


function LetterDictionaryBook() {
    const [urlPrefix, setUrlPrefix] = useState<string>("");
    const {bookId} = useParams<{ bookId: string }>();
    const [book, setBook] = useState<BookVo | null>(null);
    const [snapshots, setSnapshots] = useState<Snapshot[] | null>(null);
    const [searchText, setSearchText] = useState<string>('');
    // selectMode 管理左侧面板类型
    const [selectMode, setSelectMode] = useState<number>(1);
    // 搜索结果列表
    const [searchResultList, setSearchResultList] = useState<SRRListVo[] | null>(null);
    // 当前搜索结果折叠模板展开的key列表
    const [searchResultCollapseActiveKey, setSearchResultCollapseActiveKey] = useState<string[]>([]);
    // 当前选中的词语id
    const [nowLetterOrVocId, setNowLetterOrVocId] = useState<number | null>(null);
    // 当前选中的词语
    const [nowLetterOrVoc, setNowLetterOrVoc] = useState<Letter | Vocabulary | null>(null);
    // 是否显示快照
    const [snapshotVisible, setSnapshotVisible] = useState<boolean>(false);
    // 当前要查看的快照路径
    const [nowSnapPath, setNowSnapPath] = useState<string>('');


    useEffect(() => {
        async function useEffectAsync() {
            try {
                if (bookId) {
                    const tempBook = await invokeGetDictionary(bookId);
                    const tempUrlPrefix = await getFeFileUrlPrefix();
                    setUrlPrefix(tempUrlPrefix);
                    tempBook.coverImageUrl = buildFileUrl(tempUrlPrefix, tempBook.coverImageUrl);
                    setBook({...tempBook});
                
            } catch (e) {
                message.error(`${e}`);
            
        

        useEffectAsync().then();
    }, []);

    function onChangeSearchText(text: string) {
        setSearchText(text);
    

    async function onChangeSelectMode(tempSelectMode: number) {
        setSelectMode(tempSelectMode);
        const tempText = searchText.trim();
        if (tempText === "") {
            return;
        
        if (bookId) {
            const params: SearchResourcesParams = {
                bookId: bookId, searchText: tempText, searchType: tempSelectMode as SearchTypeEnum
            };
            const result = await invokeSearchResources(params);
            setSearchResultList([...result.list]);
            setSearchResultCollapseActiveKey(['0', (result.list.length - 1).toString()]);
        
    

    async function onSearch() {
        const tempText = searchText.trim();
        if (tempText === "") {
            message.error("请输入搜索文本");
            return;
        
        if (bookId) {
            const params: SearchResourcesParams = {
                bookId: bookId, searchText: tempText, searchType: selectMode as SearchTypeEnum
            };
            const result = await invokeSearchResources(params);
            setSearchResultList([...result.list]);
            setSearchResultCollapseActiveKey(['0', (result.list.length - 1).toString()]);
        
    

    async function onChangeLetterOrVocabulary(it: SRRListItemVo) {
        setNowLetterOrVocId(it.id);
        if (it.type === "VOCABULARY") {
            const tempVoc = await invokeGetVocabularyById(it.id);
            setNowLetterOrVoc({...tempVoc});
        } else if (it.type === "LETTER") {
            const tempLetter = await invokeGetLetterById(it.id);
            setNowLetterOrVoc({...tempLetter});
        
    

    async function showSnapshot(tempPage: number | null) {
        if (tempPage === null) {
            message.error('当前汉字没有纸质书的页码信息，无法查看快照！');
            return;
        
        let tempSnapshots: Snapshot[] = [];
        if (snapshots === null) {
            if (bookId) {
                tempSnapshots = await invokeGetSnapshots(bookId);
                for (const sp of tempSnapshots) {
                    sp.path = buildFileUrl(urlPrefix, sp.path);
                
                setSnapshots([...tempSnapshots]);
            
        } else {
            tempSnapshots = snapshots;
        

        const curSnap = tempSnapshots.find(ss => ss.page === tempPage);
        if (!curSnap) {
            message.error('无法根据当前汉字的页面找到对应快照！');
            return;
        
        setNowSnapPath(curSnap.path);
        setSnapshotVisible(true);
    

    function onChangeSearchResultCollapse(key: string[]) {
        setSearchResultCollapseActiveKey([...key]);
    

    function buildSearchCollapseItems(tempResults: SRRListVo[]) {
        const items: CollapseProps['items'] = [];
        for (let i = 0; i < tempResults.length; i++) {
            const r = tempResults[i];
            items.push({
                key: i,
                label: r.tip,
                children: <div>
                    {r.items.map((it, j) => <>
                        <div key={j} style={{display: 'flex'}}>
                            <div className="left-match">
                                <div className="left-spell">
                                    <span>{ it.pyWithTone }</span>
                                </div>
                                <div className="left-word">
                                    <span
                                        className={classNames({
                                            'select': it.id === nowLetterOrVocId,
                                        })
                                        onClick={() => onChangeLetterOrVocabulary(it)
                                    >
                                        { it.nameOutBase 
                                    </span>
                                </div>
                            </div>
                            <div className="left-ill">
                                <span>{ it.illustration }</span>
                            </div>
                        </div>
                    </>)
                </div>
            });
        

        return items;
    

    return (<>
        <Row>
            <Col span={12} style={{border: '1px solid #ededed'}}>
                <div style={{display: 'flex', justifyContent: 'center', marginTop: '5px'}}>
                    <Search
                        placeholder="请输入搜索文本"
                        allowClear
                        onSearch={onSearch
                        onChange={e => onChangeSearchText(e.target.value)
                        style={{ width: 300 }
                    />
                </div>
                <div style={{display: 'flex', justifyContent: 'center', marginTop: '15px'}}>
                    <Radio.Group value={selectMode} onChange={e => onChangeSelectMode(e.target.value)}>
                        <Radio.Button value={1}>推荐</Radio.Button>
                        <Radio.Button value={2}>同音</Radio.Button>
                        <Radio.Button value={3}>同部首</Radio.Button>
                    </Radio.Group>
                </div>
                <div style={{display: 'flex', justifyContent: 'center', margin: '15px 5px 0 5px'}}>
                    <Card style={{width:'100%', height: 'calc(100vh - 140px)', backgroundColor: 'rgb(247, 247, 247)'}}>
                        <div style={{height: 'calc(100vh - 170px)', overflowY: 'auto'}}>
                            {searchResultList !== null && searchResultList.length > 0 && (<>
                                <Collapse
                                    defaultActiveKey={['0']
                                    items={buildSearchCollapseItems(searchResultList)
                                    activeKey={searchResultCollapseActiveKey
                                    onChange={onChangeSearchResultCollapse
                                />
                            </>)
                            {searchResultList !== null && searchResultList.length === 0 && (<>
                                <Empty />
                            </>)
                        </div>
                    </Card>
                </div>
            </Col>
            <Col span={12} style={{border: '1px solid #ededed'}}>
                <div style={{display: 'flex', justifyContent: 'center', marginTop: '5px'}}>
                    <Card style={{width:'100%', height: 'calc(100vh - 46px)', backgroundColor: 'rgb(247, 247, 247)'}} >
                        {!nowLetterOrVocId && book && <>
                            <div style={{display: 'flex', justifyContent: 'center'}}>
                                <img alt={book.bookName} src={`${book.coverImageUrl}`} style={{width: '70%', maxWidth: '500px'}} />
                            </div>
                            <div style={{display: 'flex', justifyContent: 'center'}}>
                                <div>
                                    <h3>{ book.bookName }</h3>
                                    <p>ISBN：{ book.isbn }</p>
                                    <p>出版社：{ book.publisher }</p>
                                </div>
                            </div>
                        </>
                        {nowLetterOrVoc && <>
                            <div>
                                <div>
                                    <div style={{display: 'flex', justifyContent: 'center'}}>
                                        <div className="big-spell" style={{width: '100px'}}>
                                            {isLetter(nowLetterOrVoc) && (<>
                                                { nowLetterOrVoc.pinyinWithTones 
                                            </>)
                                            {isVocabulary(nowLetterOrVoc) && (<>
                                                { nowLetterOrVoc.pinyinWithTone 
                                            </>)
                                        </div>
                                    </div>
                                    <div style={{display: 'flex', justifyContent: 'center'}}>
                                        <div className="big-letter" style={{height: '120px'}}>
                                            { nowLetterOrVoc.name }{nowLetterOrVoc.nameOutBase && nowLetterOrVoc.name !== nowLetterOrVoc.nameOutBase && <>({nowLetterOrVoc.nameOutBase})</>
                                        </div>
                                    </div>
                                    {isLetter(nowLetterOrVoc) && (<>
                                        <div style={{display: 'flex', justifyContent: 'center'}}>
                                            <div className="big-info">
                                                <div>
                                                    <span>字级：</span>{ nowLetterOrVoc.level 
                                                </div>
                                                <div>
                                                    <span>部首：</span>{ nowLetterOrVoc.radical 
                                                </div>
                                                <div>
                                                    <span>笔画：</span>{ nowLetterOrVoc.strokeCount 
                                                </div>
                                                <div>
                                                    <span>结构：</span>{ nowLetterOrVoc.structure 
                                                </div>
                                            </div>
                                        </div>
                                    </>)
                                    <div>
                                        <Card>
                                            <div style={{display: 'flex', justifyContent: 'center'}}>
                                                <div className="big-mean-head">例证</div>
                                            </div>
                                            <div>{nowLetterOrVoc.illustration}</div>
                                            <div className="big-mean-end">
                                                ——来自{book?.bookName
                                            </div>
                                        </Card>
                                    </div>
                                </div>
                                <div style={{position: 'fixed', right: '40px', top: '50px'}}>
                                    <Button size="small" type="primary" onClick={() => showSnapshot(nowLetterOrVoc?.page)}>查看快照</Button>
                                </div>
                                <Image
                                    style={{display: 'none'}
                                    src={nowSnapPath
                                    preview={{
                                        visible: snapshotVisible,
                                        src: nowSnapPath,
                                        onVisibleChange: (value) => {
                                            setSnapshotVisible(value);
                                        },
                                    }
                                />
                            </div>
                        </>
                    </Card>
                </div>
            </Col>
        </Row>
    </>);


export default LetterDictionaryBook;
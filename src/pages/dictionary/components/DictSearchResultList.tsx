import {Card, List, Pagination} from "antd";
import React, {CSSProperties} from "react";
import {PageResult} from "../../../interface";
import {BookVo} from "../../../interface/BookInterface.ts";

const { Meta } = Card;

interface SearchResultProps {
    style?: CSSProperties | undefined;
    result?: PageResult<BookVo>;
    onChangePageNo?: (pageNo: number) => void;
    onChangePageSize?: (pageSize: number) => void;
    onClickResultItem?: (bookType: string, bookId: string) => void;


function DictSearchResultList(props: SearchResultProps) {

    function onChangePagination(pageNo: number, pageSize: number) {
        pageNo -= 1;
        if (props.onChangePageNo && pageNo !== props.result?.pageNo) {
            props.onChangePageNo(pageNo);
        
        if (props.onChangePageSize && pageSize !== props.result?.pageSize) {
            props.onChangePageSize(pageSize);
        
    

    function onClickResultItem(bookType: string, bookId: string) {
        if (props.onClickResultItem !== undefined) {
            props.onClickResultItem(bookType, bookId);
        
    

    return <>
        {props.result !== undefined && <>
            <List
                size="small"
                style={props.style
                bordered
                grid={{ gutter: 0, column: 5 }
                dataSource={props.result.list
                header={<div>
                    书名搜索结果（共{ props.result.totals }条）
                </div>
                renderItem={(item) => {
                    return <List.Item onClick={() => onClickResultItem(item.type, item.id)}>
                        <Card
                            styles={{body: {padding: '5px'}}
                            hoverable
                            cover={<img alt={item.bookName} src={item.coverImageUrl ? item.coverImageUrl : undefined} style={{ width: '100%', aspectRatio: '1/1.4' }} />
                        >
                            <div style={{fontSize: '11px'}}>
                                《{item.bookName}》
                            </div>
                        </Card>
                    </List.Item>
                }
                footer={<div>
                    <Pagination
                        align={'center'
                        showQuickJumper
                        pageSize={props?.result.pageSize
                        current={props?.result.pageNo + 1
                        defaultCurrent={1
                        total={props?.result.totals
                        onChange={onChangePagination
                    />
                </div>
            />
        </>
    </>


export default DictSearchResultList;
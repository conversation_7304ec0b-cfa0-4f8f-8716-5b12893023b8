import React, {useEffect, useRef, useState} from 'react';
import {Input, Button, message} from 'antd';
import type {GetProps} from 'antd';
import './Activate.css';
import {OTPRef} from "antd/es/input/OTP";
import {sendNotification} from "@tauri-apps/plugin-notification";
import {invokeActivateParseCode, invokeGetActivateKey} from "../../invoker/activation.ts"; // 引入CSS文件

const Activate: React.FC<{
    activateStatus: boolean,
    onActivate: () => void,
    unActivateInfo: string
}> = ({activateStatus, onActivate, unActivateInfo}) => {
    const [messageApi, contextHolder] = message.useMessage();

    const [machineKey, setMachineKey] = useState<string>('-1')
    const [inputCode, setInputCode] = useState<string>('')

    // 激活码输入框属性
    type OTPProps = GetProps<typeof Input.OTP>;
    const onChange: OTPProps['onChange'] = async (text) => {
        setInputCode(text)
    };
    const sharedProps: OTPProps = {onChange,};


    // 激活时候调用onActivate方法，让前端刷新数据
    async function getMachineKey() {

        function sleep(ms: number) {
            return new Promise(resolve => setTimeout(resolve, ms));
        

        while (true) {
            try {
                const machineKey = await invokeGetActivateKey();
                await sleep(10)
                setMachineKey(machineKey)
                break
            } catch (e) {
                await sleep(10)
            
        
    

    useEffect(() => {
        if (inputCode.length > 0)
            submit().then()
    }, [inputCode])
    async function submit() {
        const res = await invokeActivateParseCode(inputCode);
        if (res) {
            sendNotification({title: '设备激活成功'});
        } else {
            messageApi.error("激活码验证失败")
        
        onActivate()
    

    async function copyKey() {
        await navigator.clipboard.writeText(String(machineKey));
        messageApi.info("已复制设备码：" + machineKey)
    

    useEffect(() => {
        getMachineKey().then()
    }, [])

    const inputRef = useRef<OTPRef>(null);

    function inputFocus() {
        inputRef.current?.focus()
    

    return (
        <>
            {contextHolder
            <div className="activation-container" onMouseEnter={inputFocus}>
                {!activateStatus && machineKey === '-1' ? (
                    <div className="error-message">
                        <h1>加载中</h1>
                    </div>
                ) : (
                    <div className="activation-content">
                        <h1 className="title" onClick={copyKey}>{unActivateInfo} 当前机器码：</h1>
                        <div className="machine-key" onClick={copyKey}>{machineKey}</div>
                        <h3>请复制并提供给软件提供方</h3>
                        <Input.OTP
                            ref={inputRef
                            length={10
                            formatter={(str) => str.toUpperCase()
                            className="otp-input"
                            {...sharedProps
                        />
                        <br/>
                        <Button onClick={submit} type="primary" className="submit-btn">
                            提交激活码
                        </Button>
                    </div>
                )
            </div>
        </>
    );
};

export default Activate;

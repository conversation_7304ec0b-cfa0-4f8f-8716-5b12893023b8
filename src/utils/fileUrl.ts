
/**
 * 处理文件的url和img标签的src值，添加跟OS相关的前缀和appDataDir路径
 */


export const buildImgSrc = (urlPrefix: string, content: string | null) => {
    if (!content)
        return ""
    function replaceImgSrc(input: string, transform: (src: string) => string): string {
        return input.replace(/(<img[^>]+src=")([^"]*)("[^>]*>)/g, (_match, p1, p2, p3) => {
            return p1 + transform(p2) + p3;
        });
    

    return replaceImgSrc(content, src => {
        src = src.trim();
        if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('asset://')) {
            return src;  // 如果src已经是绝对路径，就返回原始的src
        } else {
            const separator = (!src.startsWith('/') && !src.startsWith('\\')) ? '/' : '';
            const tempUrl = urlPrefix + separator + src;
            return encodeURI(tempUrl);
        
    })



export const buildFileUrl = (urlPrefix: string, partedUrl: string | null) => {
    if (!partedUrl)
        return ""
    partedUrl = partedUrl.trim();
    if (partedUrl.startsWith('http://') || partedUrl.startsWith('https://') || partedUrl.startsWith('asset://')) {
        return partedUrl;  // 如果已经是绝对路径，就返回原始的url
    } else {
        const separator = (!partedUrl.startsWith('/') && !partedUrl.startsWith('\\')) ? '/' : '';
        const tempUrl = urlPrefix + separator + partedUrl;
        return encodeURI(tempUrl);
    


import type {TreeDataNode} from "antd";
import {Catalogue} from "../interface/BookInterface.ts";
import {invokeGetBookByBookId} from "../invoker/book.ts";


interface CascaderOption {
    value: string | number;
    label: string;
    children?: CascaderOption[];
    disableCheckbox?: boolean;


function useNewspaperReader(type: string, bookName: string) {
    return type === "报纸" && !bookName.startsWith('参考消息')
        && !bookName.includes('南方周末报')
        && !bookName.includes('中学生时事政治报')
        && !bookName.includes('二十一世纪')


async function openOrSwitchWindow(label: string, bookId: string,type:string, page?: number,id?:string) {
    const book = await invokeGetBookByBookId(bookId);
    const res = book;
    if (res.data === null || res.data.length === 0) {
        return;
    
    const bookName: string = res.data[0].bookName;
    let url: string;
    if (useNewspaperReader(type, bookName)) {
        url = '/global-search/newspaperreader/' + bookId
    } else {
        url = '/global-search/reader/' + bookId
    
    if (page) {
        if (useNewspaperReader(type, bookName)) {
            url = url + '/' + page +'/'+ id
        } else {
            url = url + '/' + page
        
    
    // Always open in new browser tab since we removed Tauri
    window.open(url, '_blank');



function convertToCascaderData(parsedData: Catalogue[]): CascaderOption[] {
    const idToNodeMap: Record<string, CascaderOption> = {};

    parsedData.forEach((item) => {
        const {title, code, ...rest} = item;
        const id = item.id
        const disableCheckbox = item.endPage !== null
        idToNodeMap[id] = {label: title, value: code, disableCheckbox: disableCheckbox, ...rest};
    });

    // Build the tree structure
    const treeData: CascaderOption[] = [];
    parsedData.forEach((item) => {
        const {id, parentId} = item;
        const node = idToNodeMap[id];
        if (parentId === null || parentId === "") {
            treeData.push(node);
        } else {
            const parent = idToNodeMap[parentId];
            if (!parent.children) {
                parent.children = [];
            
            parent.children.push(node);
        
    });
    // console.log(JSON.stringify(treeData))
    return treeData


function convertToTreeData(parsedData: Catalogue[]): TreeDataNode[] {
    const idToNodeMap: Record<string, TreeDataNode> = {};

    parsedData.forEach((item) => {
        const {title, catalogueId, ...rest} = item;
        const id = item.catalogueId
        idToNodeMap[id] = {title, key: catalogueId, ...rest};
    });
    // console.log(parsedData)
    // Build the tree structure
    const treeData: TreeDataNode[] = [];
    parsedData.forEach((item) => {
        const {catalogueId, parentId} = item;
        // console.log(item)
        const node = idToNodeMap[catalogueId];
        if (parentId === null || parentId === "") {
            // console.log(node)
            treeData.push(node);
        
        else {
            const parent = idToNodeMap[parentId];
            if (!parent.children) {
                parent.children = [];
            
            parent.children.push(node);
        
    });
    // console.log(JSON.stringify(treeData))

    return treeData;
    // return []


export {openOrSwitchWindow, convertToTreeData, convertToCascaderData};
export type {CascaderOption};


export function highlight_text(text: string, highlights: string[] | undefined) {
    if (!text) {
        return '';  // 如果文本为空，返回空字符串
    
    if (highlights === undefined) {
        return text;  // 如果没有需要高亮的词，直接返回原始文本
    

    // Step 1: 使用正则表达式匹配LaTeX公式部分，并暂时将其从文本中移除
    const latexRegex = /\$(.*?)\$/g;
    const latexParts: string[] = [];
    text = text.replace(latexRegex, (match) => {
        latexParts.push(match);
        return `<<latex${latexParts.length - 1}>>`;
    });

    // Step 2: 使用正则表达式匹配<img>标签部分，并暂时将其从文本中移除
    const imgRegex = /<img[^>]*>/g;
    const imgParts: string[] = [];
    text = text.replace(imgRegex, (match) => {
        imgParts.push(match);
        return `<<img${imgParts.length - 1}>>`;
    });

    // Step 3: 按照高亮词的长度排序，确保长的词先被高亮
    highlights = highlights.sort((a, b) => a.length - b.length).reverse();

    // Step 4: 对文本中的非LaTeX和非<img>部分进行高亮处理
    for (const hl of highlights) {
        text = text.replaceAll(hl, `<span style="color: orange">${hl}</span>`);
    

    // Step 5: 使用占位符恢复之前剥离出来的LaTeX公式
    text = text.replace(/<<latex(\d+)>>/g, (match, index) => {
        return latexParts[parseInt(index, 10)];  // 将占位符替换为实际的LaTeX公式
    });

    // Step 6: 使用占位符恢复之前剥离出来的<img>标签
    text = text.replace(/<<img(\d+)>>/g, (match, index) => {
        return imgParts[parseInt(index, 10)];  // 将占位符替换为实际的<img>标签
    });

    return text;  // 返回最终处理后的文本



export function processContent(content: string): string {
    // 匹配图片标签（假设图片标签是 <img>）
    const imgRegex = /<img[^>]+src="([^">]+)"[^>]*>/g;
    let imgCount = 0;
    let firstImgSrc = '';

    // 替换图片标签，只保留第一张
    const processedContent = content.replace(imgRegex, (match, src) => {
        if (imgCount === 0) {
            imgCount++;
            firstImgSrc = src; // 保存第一张图片的 src
            return `<img src="${src}" alt="图片" style="max-width: 100%; height: auto;" />`; // 保留第一张图片
        
        return ''; // 跳过其他图片
    });

    // 截取前300个字符（去掉 HTML 标签）
    const textContent = processedContent.replace(/<[^>]+>/g, ''); // 去掉所有 HTML 标签
    const truncatedText = textContent.length > 300
        ? textContent.substring(0, 300) + '......' // 如果内容超过300个字符，则截断并加上省略号
        : textContent;

    // 将截取的文本和第一张图片组合
    const imageTag = firstImgSrc
        ? `<img src="${firstImgSrc}" alt="图片" style="max-width: 50%; height: auto;" />`
        : '';

    // 返回拼接后的字符串
    return `${imageTag} ${truncatedText}`;


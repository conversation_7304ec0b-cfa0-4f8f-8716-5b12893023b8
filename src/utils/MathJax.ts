import {MathMLToLaTeX} from 'mathml-to-latex';

interface MathJaxRenderOptions {
    em?: number;
    ex?: number;
    display?: boolean;


const MATH_G: RegExp = /(<math.*?<\/math>)/g;
const MATH: RegExp = /(<math.*?<\/math>)/;
const DOMAIN_IMAGE: string = '/static';

class MathJaxRender {
    private name: string;
    private style: object;
    private options: MathJaxRenderOptions;
    private type: string;

    constructor(options: MathJaxRenderOptions = {}, type: string) {
        this.name = 'qct-latex';
        this.style = {};
        this.options = { em: 20, ex: 10, display: true, ...options };
        this.type = type;
    

    toSvg(formula:string) {
        const _MathJax = window.MathJax
        const $mathJax = _MathJax.tex2svg(formula, this.options)
        const $svg = $mathJax.querySelector('svg')
        // const $math = $mathJax.querySelector('math')
        Object.assign($svg.style, this.style)
        if (this.type === 'editor-src') {
            const img = document.createElement('img')
            const svgEncode = encodeURIComponent($svg.outerHTML)
            // img.className = 'Wirisformula'
            // img.setAttribute('style', 'max-width:none;vertical-align:-4px;')
            img.src = `data:image/svg+xml;charset=utf8,${svgEncode}`
            img.alt = encodeURIComponent('saveLatex' + formula)
            return img.outerHTML
        
        return $mathJax.innerHTML
    

    toImage(formula: string): Promise<{dataURL: string, width: number, height: number}> {
        const _MathJax = window.MathJax
        return new Promise((resolve, reject) => {
            _MathJax.texReset()
            const $mathJax = _MathJax.tex2svg(formula, this.options)
            const $svg = $mathJax.querySelector('svg')
            Object.assign($svg.style, this.style)
            const image = new Image()
            image.src = 'data:image/svg+xml;base64,' + window.btoa(unescape(encodeURIComponent($svg.outerHTML)))
            image.onload = () => {
                const { width, height } = image
                const canvas = document.createElement('canvas')
                canvas.width = width
                canvas.height = height
                const context = canvas.getContext('2d')
                if (context === null) {
                    reject()
                    return
                
                context.drawImage(image, 0, 0)
                resolve({ dataURL: canvas.toDataURL('image/png'), width, height })
            
            image.onerror = () => {
                reject()
            
        })
    

    async render(text = '') {
        text = text.replace(/<img\s*src\s*=\s*(["'])([^/])/g, '<img src=$1' + DOMAIN_IMAGE + '/$2')
        const matchList = text.match(MATH_G)
        if (matchList) {
            for (const i in matchList) {
                const item = MATH.exec(matchList[i])
                if (!item) {
                    continue
                
                const mathHtml = MathMLToLaTeX.convert(item[1])
                const $mathHtml = '\\(' + mathHtml + '\\)'
                text = text.replace(item[1], $mathHtml)
            
        
        try {
            let input:string = typeof text === 'string' ? text : ''
            const nl_tag = `--nl--`
            const nl_reg = /--nl--/g
            input = input.replace(/\n/g, nl_tag)
            input = input.replace(/\r\n/g, nl_tag)
            input = input.replace(/\r/g, nl_tag)
            input = input.replace('<', '< ')
            input = input.replace('< br', '<br').replace('< table', '<table').replace('< tr', '<tr').replace('< td', '<td').replace('< img', '<img').replace('< style', '<style').replace('< p', '<p')
            input = input.replace('< span', '<span').replace('< div', '<div').replace('< figure', '<figure').replace('< math', '<math')
            let $latexes = input.match(/\\\[.+?\\\]/g)
            if ($latexes) {
                $latexes.some(str => {
                    const len = str.length
                    let latexe = str.substr(2, len - 4)
                    latexe = latexe.replace(nl_reg, ` `)
                    input = input.replace(
                        str,
                        `<span class="${this.name} latex inline" title="${latexe}"></span>`
                    )
                })
            
            // 去除数学公式\({\quad}\)
            $latexes = input.match(/.+?\\\(\{\\quad\}\\\)/g)
            if ($latexes && $latexes.length === 1) {
                $latexes.some(str => {
                    const len = str.length
                    let latexe = str.substr(0, len - 11)
                    latexe = latexe.replace(nl_reg, ` `)
                    input = input.replace(str, `${latexe}`)
                })
            
            $latexes = input.match(/\\\(.+?\\\)/g)
            if ($latexes) {
                $latexes.some(str => {
                    const len = str.length
                    let latexe = str.substr(2, len - 4)
                    latexe = latexe.replace(nl_reg, ` `)
                    input = input.replace(
                        str,
                        `<span class="${this.name} latex inline" title="${latexe}"></span>`
                    )
                })
            
            $latexes = input.match(/\$\$.+?\$\$/g)
            // 避免title混淆内部""
            const $latexInline = input.match(/qct-latex latex inline/g)
            if ($latexes) {
                $latexes.some(str => {
                    const len = str.length
                    let latexe = str.substr(2, len - 4)
                    latexe = latexe.replace(nl_reg, ` `)
                    if ($latexInline) {
                        input = input.replace(
                            str,
                            `$$${latexe}$$`
                        )
                    } else {
                        input = input.replace(
                            str,
                            `<span class="${this.name} latex" title="${latexe}"></span>`
                        )
                    
                })
            
            $latexes = input.match(/\$.+?\$/g)
            if ($latexes) {
                $latexes.some(str => {
                    const len = str.length
                    let latexe = str.substr(1, len - 2)
                    latexe = latexe.replace(nl_reg, ` `)
                    input = input.replace(
                        str,
                        `<span class="${this.name} latex inline" title="${latexe}"></span>`
                    )
                })
            
            $latexes = input.match(/.+?\\\\left/g)
            if ($latexes) {
                $latexes.some(str => {
                    let latexe = str.replace('\\left', '\\left')
                    latexe = latexe.replace(nl_reg, ` `)
                    input = input.replace(
                        str,
                        `${latexe}`
                    )
                })
            

            input = input.replace(nl_reg, `<br \\>`)
            const $math = document.createElement(`div`)
            $math.innerHTML = input
            const $mathjax = $math.querySelectorAll(`.${this.name}.latex`)
            if (!$mathjax.length) {
                return input.replace(/f-div/g, 'figure')
            

            for (let i = 0; i < $mathjax.length; i++) {
                const $el: Element = $mathjax[i]
                if (this.type === 'editorError') {
                    const { dataURL, width, height } = await this.toImage($el.title)
                    $el.innerHTML = `<img src="${dataURL}" width="${width}" height="${height}"/>`
                } else {
                    $el.innerHTML = this.toSvg($el.title)
                

            
            return $math.innerHTML
        } catch (e) {
            console.error(e)
        

    


export default MathJaxRender;

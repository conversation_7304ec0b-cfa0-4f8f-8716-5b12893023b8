import React, {createContext, useContext, useEffect, useRef} from "react";
import {invokeGetIncFileUpdRecords} from "../invoker/IncFileUpd.ts";
import {IncFileUpdRecord} from "../interface/IncFileUpd.ts";
import {ResponseVO} from "../interface";
import {importIncrementsQueue} from "../invoker/increment-update.ts";
import {v4 as uuidv4} from "uuid";
import axios from "axios";


interface FileMergingResult {
    remoteFilePath: string;


interface ImportIncQueueParam {
    filePaths: string[];
    deleteFiles: boolean | null;



const UploadContext = createContext<{
    incFileUpdRecords: IncFileUpdRecord[],
    uploadIncrementInBrowser: (fileList: FileList) => Promise<ResponseVO<null>>,
    updateIncFileUpdRecords: () => Promise<void>,
}>({
    incFileUpdRecords: [],
    uploadIncrementInBrowser: () => Promise.resolve({
        code: 200, data: null, msg: '',
    }),
    updateIncFileUpdRecords: () => Promise.resolve(),
});


export function IncFileUploadProvider(props: {children: React.ReactNode}) {
    const [records, setRecords] = React.useState<IncFileUpdRecord[]>([]);
    const recordsRef = useRef(records);
    const [fileMap] = React.useState<Map<string, File>>(new Map());
    const fileMapRef = useRef(fileMap);
    const [timer, setTimer] = React.useState<NodeJS.Timeout | null>(null);



    async function handleUploadingOneTimeInBrowser() {
        const targetRecord = recordsRef.current.find(r => r.status === 0);
        if (!targetRecord) {
            return;
        
        const targetFile = fileMapRef.current.get(targetRecord.rId);
        if (!targetFile) {
            return;
        

        // 每个分片上传重试次数
        const RETRY_COUNT = 4;
        // 分片大小（2MB）
        const CHUNK_SIZE = 1024 * 1024 * 4;
        
        // 上传分片
        const uploadChunks = async (file: File, rec: IncFileUpdRecord) => {
            const chunks = Math.ceil(file.size / CHUNK_SIZE);
            const requests = [];

            // 更新进度为上传中
            setRecords(recordsRef.current.map(r => {
               if (r.rId !== rec.rId) {
                   return r;
               } else {
                   return {...r, status: 1};
               
            }));

            for (let i = 0; i < chunks; i++) {
                const start = i * CHUNK_SIZE;
                const end = start + CHUNK_SIZE >= file.size ? file.size : start + CHUNK_SIZE;
                const chunk = file.slice(start, end);

                requests.push((async () => {
                    let errorMsg: string | null = null;
                    for (let j = 1; j <= RETRY_COUNT; j++) {
                        try {
                            const res = await fetch(
                                `/api/upload/chunk?identifier=${rec.rId}&chunkIndex=${i}`, {
                                    method: 'POST',
                                    body: chunk
                                });
                            if (res.ok) {
                                break;
                            } else if (j === RETRY_COUNT) {
                                errorMsg = res.statusText;
                            
                        } catch (e) {
                            if ( j === RETRY_COUNT) {
                                errorMsg = `${e}`;
                            
                        
                    
                    if (errorMsg !== null) {
                        throw new Error(`分片上传失败: ${errorMsg}`);
                    
                    // 更新当前上传进度
                    setRecords(recordsRef.current.map(r => {
                        if (r.rId !== rec.rId) {
                            return r;
                        } else {
                            return {...r, updPercentage: i / chunks * 100};
                        
                    }));
                })());

                // 控制并发数量（每次同时上传3个分片）
                if (i % 3 === 0 && i !== 0){
                    try {
                        await Promise.all(requests);
                    } catch (e: any) {
                        // 上传失败
                        setRecords(recordsRef.current.map(r => {
                            if (r.rId !== rec.rId) {
                                return r;
                            } else {
                                return {...r, status: 3, msg: e.toString()};
                            
                        }));
                        // 终止当前上传
                        return;
                    
                
            

            try {
                await Promise.all(requests);
            } catch (e: any) {
                // 上传失败
                setRecords(recordsRef.current.map(r => {
                    if (r.rId !== rec.rId) {
                        return r;
                    } else {
                        return {...r, status: 3, msg: e.toString()};
                    
                }));
                // 终止当前上传
                return;
            

            let errorMsg: string | null = null;

            let mergingResult: FileMergingResult = {remoteFilePath: ""};
            // 合并分片
            try {
                const res = await axios.post<FileMergingResult>(`/api/upload/chunks-merge?identifier=${rec.rId}&fileName=${rec.fileName}&numChunks=${chunks}`);
                if (res.status !== 200 || res.data === null) {
                    errorMsg = res.statusText;
                } else {
                    mergingResult = res.data;
                
            } catch (e) {
                errorMsg = `${e}`;
            
            if (errorMsg !== null) {
                // 设置异常状态
                setRecords(recordsRef.current.map(r => {
                    if (r.rId !== rec.rId) {
                        return r;
                    } else {
                        return {...r, status: 3, msg: `分片合并失败：${errorMsg}`};
                    
                }));
                // 终止当前上传
                return;
            
            // 设置远程文件路径
            setRecords(recordsRef.current.map(r => {
                if (r.rId !== rec.rId) {
                    return r;
                } else {
                    return {...r, status: 2, updPercentage: 100, remoteFilePath: mergingResult.remoteFilePath};
                
            }));

            // 请求远程服务器导入增量包
            try {
                await importIncrementsQueue([mergingResult.remoteFilePath]);
            } catch (e) {
                //
            
        };

        console.log("current: file: ", targetRecord);

        try {
            await uploadChunks(targetFile, targetRecord);
        } catch (e) {
            //
        
    

    async function startUploadInBrowser() {
        if (timer === null) {
            const tempTimer = (function loop() {
                return setTimeout(async function () {
                    // 在这里编写代码逻辑
                    await handleUploadingOneTimeInBrowser();
                    return loop();
                }, 300);
            })();
            setTimer(tempTimer);
        
    

    async function uploadInBrowser(fileList: FileList): Promise<ResponseVO<null>> {
        const tempRecords: IncFileUpdRecord[] = [];
        for (const f of fileList) {
            const rId = uuidv4();
            fileMap.set(rId, f);
            tempRecords.push({
                rId,
                createTime: "",
                fileName: f.name,
                handledTime: null,
                localFilePath: f.name,
                msg: null,
                remoteFilePath: null,
                status: 0,
                updPercentage: 0
            });
        
        setRecords([...records, ...tempRecords]);

        await startUploadInBrowser();

        return {code: 200, data: null, msg: ''};
    

    async function update() {
        try {
            const tempRecords = await invokeGetIncFileUpdRecords();
            setRecords([...tempRecords]);
        } catch (e) {
            console.error("Failed to update records:", e);
        
    

    useEffect(() => {
        update();
    }, []);

    // 同步最新值到 ref
    useEffect(() => {
        recordsRef.current = records;
    }, [records]);
    useEffect(() => {
        fileMapRef.current = fileMap;
    }, [fileMap]);

    return (
        <UploadContext.Provider
            value={{
                incFileUpdRecords: records,
                uploadIncrementInBrowser: uploadInBrowser,
                updateIncFileUpdRecords: update,
            }
        >
            { props.children 
        </UploadContext.Provider>
    )


export const useIncFileUpload = () => useContext(UploadContext);
import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import "./styles.css";
import "./config/api";
import {createBrowserRouter, createRoutesFromElements, Route, RouterProvider} from "react-router-dom";
import SysFeatureManagement from "./pages/systemFeature/SysFeatureManagement.tsx";
import ReleaseNote from "./pages/release/ReleaseNote.tsx";
import GlobalSearch from "./pages/searching/GlobalSearching.tsx";
import PlagiarismPage from "./pages/plagiarism/PlagiarismPage.tsx";
import IncrementUpdate from "./pages/incrementUpdate";
import NewspaperReader from "./pages/newspaper/NewspaperReader.tsx";
import BookReader from "./pages/book/BookReader.tsx";
import DictionarySearch from "./pages/dictionary/DictionarySearch.tsx";
import LetterDictionaryBook from "./pages/dictionary/LetterDictionaryBook.tsx";
import MapPage from "./pages/map";
import WordDictionaryBook from "./pages/dictionary/WordDictionaryBook.tsx";
import LawPage from "./pages/law";
import {IncFileUploadProvider} from "./context/IncrementUpload.tsx";

const router = createBrowserRouter(
    createRoutesFromElements(
        <Route path="/" element={<App/>}>
            <Route index element={<GlobalSearch/>}/>
            <Route path="global-search">
                <Route index element={<GlobalSearch/>}/>
                <Route path="reader/:bookId" element={<BookReader/>}/>
                <Route path="reader/:bookId/:page" element={<BookReader/>}/>
                <Route path="newspaperreader/:bookId" element={<NewspaperReader/>}/>
                <Route path="newspaperreader/:bookId/:page/:id" element={<NewspaperReader/>}/>
            </Route>
            <Route path="plagiarism">
                <Route index element={<PlagiarismPage/>}/>
            </Route>
            <Route path="dictionary">
                <Route index element={<DictionarySearch/>}/>
                <Route path="letter-dictionary-book/:bookId" element={<LetterDictionaryBook/>}/>
                <Route path="word-dictionary-book/:bookId" element={<WordDictionaryBook/>}/>
            </Route>
            <Route path="map">
                <Route index element={<MapPage/>}/>
            </Route>
            <Route path="law">
                <Route index element={<LawPage/>}/>
            </Route>
            <Route path="settings">
                <Route path="increment-update" element={<IncrementUpdate/>}/>
                <Route path="system-feature" element={<SysFeatureManagement/>}/>
                <Route path="release-note" element={<ReleaseNote/>}/>
            </Route>
        </Route>
    )
)

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
    <React.StrictMode>
        <IncFileUploadProvider>
            <RouterProvider router={router}/>
        </IncFileUploadProvider>
    </React.StrictMode>,);

[workspace]
members = ["backend"]
resolver = "2"

[profile.dev]
incremental = true # Compile your binary in smaller steps.
codegen-units = 32 # Default is 16, but you can tweak this for your use case

[profile.release]
incremental = true
codegen-units = 1  # Allows LLVM to perform better optimization.
lto = true         # Enables link-time-optimizations.
opt-level = "s"    # Prioritizes small binary size. Use `3` if you prefer speed.
panic = "abort"    # Higher performance by disabling panic handlers.
strip = true       # Ensures debug symbols are removed.

